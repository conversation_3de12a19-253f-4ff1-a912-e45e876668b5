<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:paddingTop="2.0dip" android:paddingRight="8.0dip" android:paddingBottom="2.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingEnd="8.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="horizontal" android:id="@id/line1" android:paddingTop="6.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/notification_content_margin_start" android:layout_marginStart="@dimen/notification_content_margin_start">
        <TextView android:textAppearance="@style/TextAppearance.Compat.Notification.Title.Media" android:ellipsize="marquee" android:id="@id/title" android:fadingEdge="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:singleLine="true" android:layout_weight="1.0" />
        <DateTimeView android:textAppearance="@style/TextAppearance.Compat.Notification.Time.Media" android:layout_gravity="center" android:id="@id/time" android:paddingLeft="8.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true" android:layout_weight="0.0" android:paddingStart="8.0dip" />
        <Chronometer android:textAppearance="@style/TextAppearance.Compat.Notification.Time.Media" android:layout_gravity="center" android:id="@id/chronometer" android:paddingLeft="8.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true" android:layout_weight="0.0" android:paddingStart="8.0dip" />
    </LinearLayout>
    <TextView android:textAppearance="@style/TextAppearance.Compat.Notification.Line2.Media" android:ellipsize="marquee" android:id="@id/text2" android:visibility="gone" android:fadingEdge="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/notification_content_margin_start" android:layout_marginTop="-2.0dip" android:layout_marginBottom="-2.0dip" android:singleLine="true" android:layout_marginStart="@dimen/notification_content_margin_start" />
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/line3" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/notification_content_margin_start" android:layout_marginStart="@dimen/notification_content_margin_start">
        <TextView android:textAppearance="@style/TextAppearance.Compat.Notification.Media" android:ellipsize="marquee" android:layout_gravity="center" android:id="@id/text" android:fadingEdge="horizontal" android:layout_width="0.0dip" android:layout_height="wrap_content" android:singleLine="true" android:layout_weight="1.0" />
        <TextView android:textAppearance="@style/TextAppearance.Compat.Notification.Info.Media" android:gravity="center" android:layout_gravity="center" android:id="@id/info" android:paddingLeft="8.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true" android:layout_weight="0.0" android:paddingStart="8.0dip" />
    </LinearLayout>
</LinearLayout>
