.class interface abstract Landroid/arch/lifecycle/ReportFragment$ActivityInitializationListener;
.super Ljava/lang/Object;
.source "ReportFragment.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/arch/lifecycle/ReportFragment;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "ActivityInitializationListener"
.end annotation


# virtual methods
.method public abstract onCreate()V
.end method

.method public abstract onResume()V
.end method

.method public abstract onStart()V
.end method
