<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="QMUIButtonStyle" format="reference" />
    <attr name="QMUICommonListItemViewStyle" format="reference" />
    <attr name="QMUIGroupListSectionViewStyle" format="reference" />
    <attr name="QMUIGroupListViewStyle" format="reference" />
    <attr name="QMUILoadingStyle" format="reference" />
    <attr name="QMUIPullRefreshLayoutStyle" format="reference" />
    <attr name="QMUIQQFaceStyle" format="reference" />
    <attr name="QMUIRadiusImageViewStyle" format="reference" />
    <attr name="QMUITabSegmentStyle" format="reference" />
    <attr name="QMUITipNewStyle" format="reference" />
    <attr name="QMUITipPointStyle" format="reference" />
    <attr name="QMUITopBarStyle" format="reference" />
    <attr name="actionBarDivider" format="reference" />
    <attr name="actionBarItemBackground" format="reference" />
    <attr name="actionBarPopupTheme" format="reference" />
    <attr name="actionBarSize" format="dimension">
        <enum name="wrap_content" value="0" />
    </attr>
    <attr name="actionBarSplitStyle" format="reference" />
    <attr name="actionBarStyle" format="reference" />
    <attr name="actionBarTabBarStyle" format="reference" />
    <attr name="actionBarTabStyle" format="reference" />
    <attr name="actionBarTabTextStyle" format="reference" />
    <attr name="actionBarTheme" format="reference" />
    <attr name="actionBarWidgetTheme" format="reference" />
    <attr name="actionButtonStyle" format="reference" />
    <attr name="actionDropDownStyle" format="reference" />
    <attr name="actionLayout" format="reference" />
    <attr name="actionMenuTextAppearance" format="reference" />
    <attr name="actionMenuTextColor" format="reference|color" />
    <attr name="actionModeBackground" format="reference" />
    <attr name="actionModeCloseButtonStyle" format="reference" />
    <attr name="actionModeCloseDrawable" format="reference" />
    <attr name="actionModeCopyDrawable" format="reference" />
    <attr name="actionModeCutDrawable" format="reference" />
    <attr name="actionModeFindDrawable" format="reference" />
    <attr name="actionModePasteDrawable" format="reference" />
    <attr name="actionModePopupWindowStyle" format="reference" />
    <attr name="actionModeSelectAllDrawable" format="reference" />
    <attr name="actionModeShareDrawable" format="reference" />
    <attr name="actionModeSplitBackground" format="reference" />
    <attr name="actionModeStyle" format="reference" />
    <attr name="actionModeWebSearchDrawable" format="reference" />
    <attr name="actionOverflowButtonStyle" format="reference" />
    <attr name="actionOverflowMenuStyle" format="reference" />
    <attr name="actionProviderClass" format="string" />
    <attr name="actionViewClass" format="string" />
    <attr name="activityChooserViewStyle" format="reference" />
    <attr name="ad_marker_color" format="color" />
    <attr name="ad_marker_width" format="dimension" />
    <attr name="alertDialogButtonGroupStyle" format="reference" />
    <attr name="alertDialogCenterButtons" format="boolean" />
    <attr name="alertDialogStyle" format="reference" />
    <attr name="alertDialogTheme" format="reference" />
    <attr name="allowStacking" format="boolean" />
    <attr name="alpha" format="float" />
    <attr name="alphabeticModifiers">
        <flag name="ALT" value="0x00000002" />
        <flag name="CTRL" value="0x00001000" />
        <flag name="FUNCTION" value="0x00000008" />
        <flag name="META" value="0x00010000" />
        <flag name="SHIFT" value="0x00000001" />
        <flag name="SYM" value="0x00000004" />
    </attr>
    <attr name="arrowHeadLength" format="dimension" />
    <attr name="arrowShaftLength" format="dimension" />
    <attr name="autoCompleteTextViewStyle" format="reference" />
    <attr name="autoSizeMaxTextSize" format="dimension" />
    <attr name="autoSizeMinTextSize" format="dimension" />
    <attr name="autoSizePresetSizes" format="reference" />
    <attr name="autoSizeStepGranularity" format="dimension" />
    <attr name="autoSizeTextType">
        <enum name="none" value="0" />
        <enum name="uniform" value="1" />
    </attr>
    <attr name="auto_show" format="boolean" />
    <attr name="background" format="reference" />
    <attr name="backgroundSplit" format="reference|color" />
    <attr name="backgroundStacked" format="reference|color" />
    <attr name="backgroundTint" format="color" />
    <attr name="backgroundTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="barLength" format="dimension" />
    <attr name="bar_height" format="dimension" />
    <attr name="barrierAllowsGoneWidgets" format="boolean" />
    <attr name="barrierDirection">
        <enum name="bottom" value="3" />
        <enum name="end" value="6" />
        <enum name="left" value="0" />
        <enum name="right" value="1" />
        <enum name="start" value="5" />
        <enum name="top" value="2" />
    </attr>
    <attr name="behavior_autoHide" format="boolean" />
    <attr name="behavior_fitToContents" format="boolean" />
    <attr name="behavior_hideable" format="boolean" />
    <attr name="behavior_overlapTop" format="dimension" />
    <attr name="behavior_peekHeight" format="dimension">
        <enum name="auto" value="-1" />
    </attr>
    <attr name="behavior_skipCollapsed" format="boolean" />
    <attr name="borderWidth" format="dimension" />
    <attr name="borderlessButtonStyle" format="reference" />
    <attr name="bottomAppBarStyle" format="reference" />
    <attr name="bottomNavigationStyle" format="reference" />
    <attr name="bottomSheetDialogTheme" format="reference" />
    <attr name="bottomSheetStyle" format="reference" />
    <attr name="boxBackgroundColor" format="color" />
    <attr name="boxBackgroundMode">
        <enum name="filled" value="1" />
        <enum name="none" value="0" />
        <enum name="outline" value="2" />
    </attr>
    <attr name="boxCollapsedPaddingTop" format="dimension" />
    <attr name="boxCornerRadiusBottomEnd" format="dimension" />
    <attr name="boxCornerRadiusBottomStart" format="dimension" />
    <attr name="boxCornerRadiusTopEnd" format="dimension" />
    <attr name="boxCornerRadiusTopStart" format="dimension" />
    <attr name="boxStrokeColor" format="color" />
    <attr name="boxStrokeWidth" format="dimension" />
    <attr name="buffered_color" format="color" />
    <attr name="buttonBarButtonStyle" format="reference" />
    <attr name="buttonBarNegativeButtonStyle" format="reference" />
    <attr name="buttonBarNeutralButtonStyle" format="reference" />
    <attr name="buttonBarPositiveButtonStyle" format="reference" />
    <attr name="buttonBarStyle" format="reference" />
    <attr name="buttonGravity">
        <flag name="bottom" value="0x00000050" />
        <flag name="top" value="0x00000030" />
    </attr>
    <attr name="buttonIconDimen" format="dimension" />
    <attr name="buttonPanelSideLayout" format="reference" />
    <attr name="buttonStyle" format="reference" />
    <attr name="buttonStyleSmall" format="reference" />
    <attr name="buttonTint" format="color" />
    <attr name="buttonTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="cardBackgroundColor" format="color" />
    <attr name="cardCornerRadius" format="dimension" />
    <attr name="cardElevation" format="dimension" />
    <attr name="cardMaxElevation" format="dimension" />
    <attr name="cardPreventCornerOverlap" format="boolean" />
    <attr name="cardUseCompatPadding" format="boolean" />
    <attr name="cardViewStyle" format="reference" />
    <attr name="chainUseRtl" format="boolean" />
    <attr name="checkboxStyle" format="reference" />
    <attr name="checkedChip" format="reference" />
    <attr name="checkedIcon" format="reference" />
    <attr name="checkedIconEnabled" format="boolean" />
    <attr name="checkedIconVisible" format="boolean" />
    <attr name="checkedTextViewStyle" format="reference" />
    <attr name="chipBackgroundColor" format="color" />
    <attr name="chipCornerRadius" format="dimension" />
    <attr name="chipEndPadding" format="dimension" />
    <attr name="chipGroupStyle" format="reference" />
    <attr name="chipIcon" format="reference" />
    <attr name="chipIconEnabled" format="boolean" />
    <attr name="chipIconSize" format="dimension" />
    <attr name="chipIconTint" format="color" />
    <attr name="chipIconVisible" format="boolean" />
    <attr name="chipMinHeight" format="dimension" />
    <attr name="chipSpacing" format="dimension" />
    <attr name="chipSpacingHorizontal" format="dimension" />
    <attr name="chipSpacingVertical" format="dimension" />
    <attr name="chipStandaloneStyle" format="reference" />
    <attr name="chipStartPadding" format="dimension" />
    <attr name="chipStrokeColor" format="color" />
    <attr name="chipStrokeWidth" format="dimension" />
    <attr name="chipStyle" format="reference" />
    <attr name="closeIcon" format="reference" />
    <attr name="closeIconEnabled" format="boolean" />
    <attr name="closeIconEndPadding" format="dimension" />
    <attr name="closeIconSize" format="dimension" />
    <attr name="closeIconStartPadding" format="dimension" />
    <attr name="closeIconTint" format="color" />
    <attr name="closeIconVisible" format="boolean" />
    <attr name="closeItemLayout" format="reference" />
    <attr name="collapseContentDescription" format="string" />
    <attr name="collapseIcon" format="reference" />
    <attr name="collapsedTitleGravity">
        <flag name="bottom" value="0x00000050" />
        <flag name="center" value="0x00000011" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="end" value="0x00800005" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="start" value="0x00800003" />
        <flag name="top" value="0x00000030" />
    </attr>
    <attr name="collapsedTitleTextAppearance" format="reference" />
    <attr name="color" format="color" />
    <attr name="colorAccent" format="color" />
    <attr name="colorBackgroundFloating" format="color" />
    <attr name="colorButtonNormal" format="color" />
    <attr name="colorControlActivated" format="color" />
    <attr name="colorControlHighlight" format="color" />
    <attr name="colorControlNormal" format="color" />
    <attr name="colorError" format="reference|color" />
    <attr name="colorPrimary" format="color" />
    <attr name="colorPrimaryDark" format="color" />
    <attr name="colorSecondary" format="color" />
    <attr name="colorSwitchThumbNormal" format="color" />
    <attr name="commitIcon" format="reference" />
    <attr name="constraintSet" format="reference" />
    <attr name="constraint_referenced_ids" format="string" />
    <attr name="content" format="reference" />
    <attr name="contentDescription" format="string" />
    <attr name="contentInsetEnd" format="dimension" />
    <attr name="contentInsetEndWithActions" format="dimension" />
    <attr name="contentInsetLeft" format="dimension" />
    <attr name="contentInsetRight" format="dimension" />
    <attr name="contentInsetStart" format="dimension" />
    <attr name="contentInsetStartWithNavigation" format="dimension" />
    <attr name="contentPadding" format="dimension" />
    <attr name="contentPaddingBottom" format="dimension" />
    <attr name="contentPaddingLeft" format="dimension" />
    <attr name="contentPaddingRight" format="dimension" />
    <attr name="contentPaddingTop" format="dimension" />
    <attr name="contentScrim" format="color" />
    <attr name="controlBackground" format="reference" />
    <attr name="controller_layout_id" format="reference" />
    <attr name="coordinatorLayoutStyle" format="reference" />
    <attr name="cornerRadius" format="dimension" />
    <attr name="counterEnabled" format="boolean" />
    <attr name="counterMaxLength" format="integer" />
    <attr name="counterOverflowTextAppearance" format="reference" />
    <attr name="counterTextAppearance" format="reference" />
    <attr name="customNavigationLayout" format="reference" />
    <attr name="defaultQueryHint" format="string" />
    <attr name="default_artwork" format="reference" />
    <attr name="dialogCornerRadius" format="dimension" />
    <attr name="dialogPreferredPadding" format="dimension" />
    <attr name="dialogTheme" format="reference" />
    <attr name="disappearedScale" format="float" />
    <attr name="displayOptions">
        <flag name="disableHome" value="0x00000020" />
        <flag name="homeAsUp" value="0x00000004" />
        <flag name="none" value="0x00000000" />
        <flag name="showCustom" value="0x00000010" />
        <flag name="showHome" value="0x00000002" />
        <flag name="showTitle" value="0x00000008" />
        <flag name="useLogo" value="0x00000001" />
    </attr>
    <attr name="divider" format="reference" />
    <attr name="dividerHorizontal" format="reference" />
    <attr name="dividerPadding" format="dimension" />
    <attr name="dividerVertical" format="reference" />
    <attr name="download_bg_line_color" format="color" />
    <attr name="download_bg_line_width" format="integer" />
    <attr name="download_line_color" format="color" />
    <attr name="download_line_width" format="integer" />
    <attr name="download_text_color" format="color" />
    <attr name="download_text_size" format="integer" />
    <attr name="drawableSize" format="dimension" />
    <attr name="drawerArrowStyle" format="reference" />
    <attr name="dropDownListViewStyle" format="reference" />
    <attr name="dropdownListPreferredItemHeight" format="dimension" />
    <attr name="duration" format="integer" />
    <attr name="editTextBackground" format="reference" />
    <attr name="editTextColor" format="reference|color" />
    <attr name="editTextStyle" format="reference" />
    <attr name="elevation" format="dimension" />
    <attr name="emptyVisibility">
        <enum name="gone" value="0" />
        <enum name="invisible" value="1" />
    </attr>
    <attr name="enforceMaterialTheme" format="boolean" />
    <attr name="enforceTextAppearance" format="boolean" />
    <attr name="errorEnabled" format="boolean" />
    <attr name="errorTextAppearance" format="reference" />
    <attr name="excludeClass" format="string" />
    <attr name="excludeId" format="reference" />
    <attr name="excludeName" format="string" />
    <attr name="expandActivityOverflowButtonDrawable" format="reference" />
    <attr name="expanded" format="boolean" />
    <attr name="expandedTitleGravity">
        <flag name="bottom" value="0x00000050" />
        <flag name="center" value="0x00000011" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="end" value="0x00800005" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="start" value="0x00800003" />
        <flag name="top" value="0x00000030" />
    </attr>
    <attr name="expandedTitleMargin" format="dimension" />
    <attr name="expandedTitleMarginBottom" format="dimension" />
    <attr name="expandedTitleMarginEnd" format="dimension" />
    <attr name="expandedTitleMarginStart" format="dimension" />
    <attr name="expandedTitleMarginTop" format="dimension" />
    <attr name="expandedTitleTextAppearance" format="reference" />
    <attr name="fabAlignmentMode">
        <enum name="center" value="0" />
        <enum name="end" value="1" />
    </attr>
    <attr name="fabCradleMargin" format="dimension" />
    <attr name="fabCradleRoundedCornerRadius" format="dimension" />
    <attr name="fabCradleVerticalOffset" format="dimension" />
    <attr name="fabCustomSize" format="dimension" />
    <attr name="fabSize">
        <enum name="auto" value="-1" />
        <enum name="mini" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fadingMode">
        <enum name="fade_in" value="1" />
        <enum name="fade_in_out" value="3" />
        <enum name="fade_out" value="2" />
    </attr>
    <attr name="fastScrollEnabled" format="boolean" />
    <attr name="fastScrollHorizontalThumbDrawable" format="reference" />
    <attr name="fastScrollHorizontalTrackDrawable" format="reference" />
    <attr name="fastScrollVerticalThumbDrawable" format="reference" />
    <attr name="fastScrollVerticalTrackDrawable" format="reference" />
    <attr name="fastforward_increment" format="integer" />
    <attr name="firstBaselineToTopHeight" format="dimension" />
    <attr name="floatingActionButtonStyle" format="reference|string|integer|boolean|color|float|dimension|fraction" />
    <attr name="font" format="reference" />
    <attr name="fontFamily" format="string" />
    <attr name="fontProviderAuthority" format="string" />
    <attr name="fontProviderCerts" format="reference" />
    <attr name="fontProviderFetchStrategy">
        <enum name="async" value="1" />
        <enum name="blocking" value="0" />
    </attr>
    <attr name="fontProviderFetchTimeout" format="integer">
        <enum name="forever" value="-1" />
    </attr>
    <attr name="fontProviderPackage" format="string" />
    <attr name="fontProviderQuery" format="string" />
    <attr name="fontStyle">
        <enum name="italic" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fontVariationSettings" format="string" />
    <attr name="fontWeight" format="integer" />
    <attr name="foregroundInsidePadding" format="boolean" />
    <attr name="fromScene" format="reference" />
    <attr name="gapBetweenBars" format="dimension" />
    <attr name="goIcon" format="reference" />
    <attr name="headerLayout" format="reference" />
    <attr name="height" format="dimension" />
    <attr name="helperText" format="string" />
    <attr name="helperTextEnabled" format="boolean" />
    <attr name="helperTextTextAppearance" format="reference" />
    <attr name="hideMotionSpec" format="reference" />
    <attr name="hideOnContentScroll" format="boolean" />
    <attr name="hideOnScroll" format="boolean" />
    <attr name="hide_during_ads" format="boolean" />
    <attr name="hide_on_touch" format="boolean" />
    <attr name="hintAnimationEnabled" format="boolean" />
    <attr name="hintEnabled" format="boolean" />
    <attr name="hintTextAppearance" format="reference" />
    <attr name="homeAsUpIndicator" format="reference" />
    <attr name="homeLayout" format="reference" />
    <attr name="hoveredFocusedTranslationZ" format="dimension" />
    <attr name="icon" format="reference" />
    <attr name="iconEndPadding" format="dimension" />
    <attr name="iconGravity">
        <flag name="start" value="0x00000001" />
        <flag name="textStart" value="0x00000002" />
    </attr>
    <attr name="iconPadding" format="dimension" />
    <attr name="iconSize" format="dimension" />
    <attr name="iconStartPadding" format="dimension" />
    <attr name="iconTint" format="color" />
    <attr name="iconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="iconifiedByDefault" format="boolean" />
    <attr name="imageButtonStyle" format="reference" />
    <attr name="indeterminateProgressStyle" format="reference" />
    <attr name="indicatorColor" format="color" />
    <attr name="indicatorName" format="string" />
    <attr name="initialActivityCount" format="string" />
    <attr name="insetForeground" format="reference|color" />
    <attr name="interpolator" format="reference" />
    <attr name="isLightTheme" format="boolean" />
    <attr name="isb_clear_default_padding" format="boolean" />
    <attr name="isb_indicator_color" format="reference|color" />
    <attr name="isb_indicator_content_layout" format="reference" />
    <attr name="isb_indicator_text_color" format="reference|color" />
    <attr name="isb_indicator_text_size" format="reference|dimension" />
    <attr name="isb_indicator_top_content_layout" format="reference" />
    <attr name="isb_max" format="float" />
    <attr name="isb_min" format="float" />
    <attr name="isb_only_thumb_draggable" format="boolean" />
    <attr name="isb_progress" format="float" />
    <attr name="isb_progress_value_float" format="boolean" />
    <attr name="isb_r2l" format="boolean" />
    <attr name="isb_seek_smoothly" format="boolean" />
    <attr name="isb_show_indicator">
        <enum name="circular_bubble" value="1" />
        <enum name="custom" value="4" />
        <enum name="none" value="0" />
        <enum name="rectangle" value="3" />
        <enum name="rounded_rectangle" value="2" />
    </attr>
    <attr name="isb_show_thumb_text" format="boolean" />
    <attr name="isb_show_tick_marks_type">
        <enum name="divider" value="3" />
        <enum name="none" value="0" />
        <enum name="oval" value="1" />
        <enum name="square" value="2" />
    </attr>
    <attr name="isb_show_tick_texts" format="boolean" />
    <attr name="isb_thumb_adjust_auto" format="boolean" />
    <attr name="isb_thumb_color" format="reference|color" />
    <attr name="isb_thumb_drawable" format="reference" />
    <attr name="isb_thumb_size" format="reference|dimension" />
    <attr name="isb_thumb_text_color" format="reference|color" />
    <attr name="isb_tick_marks_color" format="reference|color" />
    <attr name="isb_tick_marks_drawable" format="reference" />
    <attr name="isb_tick_marks_ends_hide" format="boolean" />
    <attr name="isb_tick_marks_size" format="reference|dimension" />
    <attr name="isb_tick_marks_swept_hide" format="boolean" />
    <attr name="isb_tick_texts_array" format="reference" />
    <attr name="isb_tick_texts_color" format="reference|color" />
    <attr name="isb_tick_texts_size" format="reference|dimension" />
    <attr name="isb_tick_texts_typeface">
        <enum name="monospace" value="1" />
        <enum name="normal" value="0" />
        <enum name="sans" value="2" />
        <enum name="serif" value="3" />
    </attr>
    <attr name="isb_ticks_count" format="integer" />
    <attr name="isb_track_background_color" format="reference|color" />
    <attr name="isb_track_background_size" format="reference|dimension" />
    <attr name="isb_track_progress_color" format="reference|color" />
    <attr name="isb_track_progress_size" format="reference|dimension" />
    <attr name="isb_track_rounded_corners" format="boolean" />
    <attr name="isb_user_seekable" format="boolean" />
    <attr name="itemBackground" format="reference" />
    <attr name="itemHorizontalPadding" format="dimension" />
    <attr name="itemHorizontalTranslationEnabled" format="boolean" />
    <attr name="itemIconPadding" format="dimension" />
    <attr name="itemIconSize" format="dimension" />
    <attr name="itemIconTint" format="color" />
    <attr name="itemPadding" format="dimension" />
    <attr name="itemSpacing" format="dimension" />
    <attr name="itemTextAppearance" format="reference" />
    <attr name="itemTextAppearanceActive" format="reference" />
    <attr name="itemTextAppearanceInactive" format="reference" />
    <attr name="itemTextColor" format="color" />
    <attr name="keep_content_on_player_reset" format="boolean" />
    <attr name="keylines" format="reference" />
    <attr name="labelVisibilityMode">
        <enum name="auto" value="-1" />
        <enum name="labeled" value="1" />
        <enum name="selected" value="0" />
        <enum name="unlabeled" value="2" />
    </attr>
    <attr name="lastBaselineToBottomHeight" format="dimension" />
    <attr name="layout" format="reference" />
    <attr name="layoutManager" format="string" />
    <attr name="layout_anchor" format="reference" />
    <attr name="layout_anchorGravity">
        <flag name="bottom" value="0x00000050" />
        <flag name="center" value="0x00000011" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="clip_horizontal" value="0x00000008" />
        <flag name="clip_vertical" value="0x00000080" />
        <flag name="end" value="0x00800005" />
        <flag name="fill" value="0x00000077" />
        <flag name="fill_horizontal" value="0x00000007" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="start" value="0x00800003" />
        <flag name="top" value="0x00000030" />
    </attr>
    <attr name="layout_behavior" format="string" />
    <attr name="layout_collapseMode">
        <enum name="none" value="0" />
        <enum name="parallax" value="2" />
        <enum name="pin" value="1" />
    </attr>
    <attr name="layout_collapseParallaxMultiplier" format="float" />
    <attr name="layout_constrainedHeight" format="boolean" />
    <attr name="layout_constrainedWidth" format="boolean" />
    <attr name="layout_constraintBaseline_creator" format="integer" />
    <attr name="layout_constraintBaseline_toBaselineOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_creator" format="integer" />
    <attr name="layout_constraintBottom_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintCircle" format="reference" />
    <attr name="layout_constraintCircleAngle" format="integer" />
    <attr name="layout_constraintCircleRadius" format="dimension" />
    <attr name="layout_constraintDimensionRatio" format="string" />
    <attr name="layout_constraintEnd_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintEnd_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintGuide_begin" format="dimension" />
    <attr name="layout_constraintGuide_end" format="dimension" />
    <attr name="layout_constraintGuide_percent" format="float" />
    <attr name="layout_constraintHeight_default">
        <enum name="percent" value="2" />
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
    </attr>
    <attr name="layout_constraintHeight_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_percent" format="float" />
    <attr name="layout_constraintHorizontal_bias" format="float" />
    <attr name="layout_constraintHorizontal_chainStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="layout_constraintHorizontal_weight" format="float" />
    <attr name="layout_constraintLeft_creator" format="integer" />
    <attr name="layout_constraintLeft_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintLeft_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_creator" format="integer" />
    <attr name="layout_constraintRight_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTop_creator" format="integer" />
    <attr name="layout_constraintTop_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTop_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintVertical_bias" format="float" />
    <attr name="layout_constraintVertical_chainStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="layout_constraintVertical_weight" format="float" />
    <attr name="layout_constraintWidth_default">
        <enum name="percent" value="2" />
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
    </attr>
    <attr name="layout_constraintWidth_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_percent" format="float" />
    <attr name="layout_dodgeInsetEdges">
        <flag name="all" value="0x00000077" />
        <flag name="bottom" value="0x00000050" />
        <flag name="end" value="0x00800005" />
        <flag name="left" value="0x00000003" />
        <flag name="none" value="0x00000000" />
        <flag name="right" value="0x00000005" />
        <flag name="start" value="0x00800003" />
        <flag name="top" value="0x00000030" />
    </attr>
    <attr name="layout_editor_absoluteX" format="dimension" />
    <attr name="layout_editor_absoluteY" format="dimension" />
    <attr name="layout_goneMarginBottom" format="dimension" />
    <attr name="layout_goneMarginEnd" format="dimension" />
    <attr name="layout_goneMarginLeft" format="dimension" />
    <attr name="layout_goneMarginRight" format="dimension" />
    <attr name="layout_goneMarginStart" format="dimension" />
    <attr name="layout_goneMarginTop" format="dimension" />
    <attr name="layout_insetEdge">
        <enum name="bottom" value="80" />
        <enum name="end" value="8388613" />
        <enum name="left" value="3" />
        <enum name="none" value="0" />
        <enum name="right" value="5" />
        <enum name="start" value="8388611" />
        <enum name="top" value="48" />
    </attr>
    <attr name="layout_keyline" format="integer" />
    <attr name="layout_optimizationLevel">
        <flag name="barrier" value="0x00000002" />
        <flag name="chains" value="0x00000004" />
        <flag name="dimensions" value="0x00000008" />
        <flag name="direct" value="0x00000001" />
        <flag name="groups" value="0x00000020" />
        <flag name="none" value="0x00000000" />
        <flag name="standard" value="0x00000007" />
    </attr>
    <attr name="layout_scrollFlags">
        <flag name="enterAlways" value="0x00000004" />
        <flag name="enterAlwaysCollapsed" value="0x00000008" />
        <flag name="exitUntilCollapsed" value="0x00000002" />
        <flag name="scroll" value="0x00000001" />
        <flag name="snap" value="0x00000010" />
        <flag name="snapMargins" value="0x00000020" />
    </attr>
    <attr name="layout_scrollInterpolator" format="reference" />
    <attr name="liftOnScroll" format="boolean" />
    <attr name="lineHeight" format="dimension" />
    <attr name="lineSpacing" format="dimension" />
    <attr name="listChoiceBackgroundIndicator" format="reference" />
    <attr name="listDividerAlertDialog" format="reference" />
    <attr name="listItemLayout" format="reference" />
    <attr name="listLayout" format="reference" />
    <attr name="listMenuViewStyle" format="reference" />
    <attr name="listPopupWindowStyle" format="reference" />
    <attr name="listPreferredItemHeight" format="dimension" />
    <attr name="listPreferredItemHeightLarge" format="dimension" />
    <attr name="listPreferredItemHeightSmall" format="dimension" />
    <attr name="listPreferredItemPaddingLeft" format="dimension" />
    <attr name="listPreferredItemPaddingRight" format="dimension" />
    <attr name="logo" format="reference" />
    <attr name="logoDescription" format="string" />
    <attr name="matchOrder" format="string" />
    <attr name="materialButtonStyle" format="reference" />
    <attr name="materialCardViewStyle" format="reference" />
    <attr name="maxActionInlineWidth" format="dimension" />
    <attr name="maxButtonHeight" format="dimension" />
    <attr name="maxHeight" format="dimension" />
    <attr name="maxImageSize" format="dimension" />
    <attr name="maxWidth" format="dimension" />
    <attr name="maximumAngle" format="float" />
    <attr name="measureWithLargestChild" format="boolean" />
    <attr name="menu" format="reference" />
    <attr name="minHeight" format="dimension" />
    <attr name="minWidth" format="dimension" />
    <attr name="minimumHorizontalAngle" format="float" />
    <attr name="minimumVerticalAngle" format="float" />
    <attr name="multiChoiceItemLayout" format="reference" />
    <attr name="navigationContentDescription" format="string" />
    <attr name="navigationIcon" format="reference" />
    <attr name="navigationMode">
        <enum name="listMode" value="1" />
        <enum name="normal" value="0" />
        <enum name="tabMode" value="2" />
    </attr>
    <attr name="navigationViewStyle" format="reference" />
    <attr name="numericModifiers">
        <flag name="ALT" value="0x00000002" />
        <flag name="CTRL" value="0x00001000" />
        <flag name="FUNCTION" value="0x00000008" />
        <flag name="META" value="0x00010000" />
        <flag name="SHIFT" value="0x00000001" />
        <flag name="SYM" value="0x00000004" />
    </attr>
    <attr name="overlapAnchor" format="boolean" />
    <attr name="paddingBottomNoButtons" format="dimension" />
    <attr name="paddingEnd" format="dimension" />
    <attr name="paddingStart" format="dimension" />
    <attr name="paddingTopNoTitle" format="dimension" />
    <attr name="panelBackground" format="reference" />
    <attr name="panelMenuListTheme" format="reference" />
    <attr name="panelMenuListWidth" format="dimension" />
    <attr name="passwordToggleContentDescription" format="string" />
    <attr name="passwordToggleDrawable" format="reference" />
    <attr name="passwordToggleEnabled" format="boolean" />
    <attr name="passwordToggleTint" format="color" />
    <attr name="passwordToggleTintMode">
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="patternPathData" format="string" />
    <attr name="play_bg_line_color" format="color" />
    <attr name="play_bg_line_width" format="integer" />
    <attr name="play_line_color" format="color" />
    <attr name="play_line_width" format="integer" />
    <attr name="played_ad_marker_color" format="color" />
    <attr name="played_color" format="color" />
    <attr name="player_layout_id" format="reference" />
    <attr name="popupMenuStyle" format="reference" />
    <attr name="popupTheme" format="reference" />
    <attr name="popupWindowStyle" format="reference" />
    <attr name="preserveIconSpacing" format="boolean" />
    <attr name="pressedTranslationZ" format="dimension" />
    <attr name="progressBarPadding" format="dimension" />
    <attr name="progressBarStyle" format="reference" />
    <attr name="qmui_accessory_type">
        <enum name="chevron" value="1" />
        <enum name="custom" value="3" />
        <enum name="none" value="0" />
        <enum name="switcher" value="2" />
    </attr>
    <attr name="qmui_alpha_disabled" format="float" />
    <attr name="qmui_alpha_pressed" format="float" />
    <attr name="qmui_auto_calculate_refresh_end_offset" format="boolean" />
    <attr name="qmui_auto_calculate_refresh_init_offset" format="boolean" />
    <attr name="qmui_backgroundColor" format="color" />
    <attr name="qmui_background_color" format="color" />
    <attr name="qmui_borderColor" format="color" />
    <attr name="qmui_borderWidth" format="dimension" />
    <attr name="qmui_border_color" format="color" />
    <attr name="qmui_border_width" format="dimension" />
    <attr name="qmui_bottomDividerColor" format="reference|color" />
    <attr name="qmui_bottomDividerHeight" format="dimension" />
    <attr name="qmui_bottomDividerInsetLeft" format="dimension" />
    <attr name="qmui_bottomDividerInsetRight" format="dimension" />
    <attr name="qmui_bottom_sheet_button_background" format="reference|color" />
    <attr name="qmui_bottom_sheet_button_height" format="dimension" />
    <attr name="qmui_bottom_sheet_button_text_color" format="reference|color" />
    <attr name="qmui_bottom_sheet_button_text_size" format="dimension" />
    <attr name="qmui_bottom_sheet_grid_bg" format="reference|color" />
    <attr name="qmui_bottom_sheet_grid_item_icon_marginBottom" format="dimension" />
    <attr name="qmui_bottom_sheet_grid_item_icon_marginTop" format="dimension" />
    <attr name="qmui_bottom_sheet_grid_item_icon_size" format="dimension" />
    <attr name="qmui_bottom_sheet_grid_item_mini_width" format="dimension" />
    <attr name="qmui_bottom_sheet_grid_item_paddingBottom" format="dimension" />
    <attr name="qmui_bottom_sheet_grid_item_paddingTop" format="dimension" />
    <attr name="qmui_bottom_sheet_grid_item_text_appearance" format="reference" />
    <attr name="qmui_bottom_sheet_grid_line_padding_horizontal" format="dimension" />
    <attr name="qmui_bottom_sheet_grid_line_vertical_space" format="dimension" />
    <attr name="qmui_bottom_sheet_grid_padding_vertical" format="dimension" />
    <attr name="qmui_bottom_sheet_list_item_bg" format="reference" />
    <attr name="qmui_bottom_sheet_list_item_height" format="dimension" />
    <attr name="qmui_bottom_sheet_list_item_icon_margin_right" format="dimension" />
    <attr name="qmui_bottom_sheet_list_item_icon_size" format="dimension" />
    <attr name="qmui_bottom_sheet_list_item_mark_margin_left" format="dimension" />
    <attr name="qmui_bottom_sheet_list_item_padding_horizontal" format="dimension" />
    <attr name="qmui_bottom_sheet_list_item_text_appearance" format="reference" />
    <attr name="qmui_bottom_sheet_list_item_tip_point_margin_left" format="dimension" />
    <attr name="qmui_bottom_sheet_title_appearance" format="reference" />
    <attr name="qmui_bottom_sheet_title_bg" format="reference" />
    <attr name="qmui_bottom_sheet_title_height" format="dimension" />
    <attr name="qmui_btn_text" format="string" />
    <attr name="qmui_childHorizontalSpacing" format="dimension" />
    <attr name="qmui_childVerticalSpacing" format="dimension" />
    <attr name="qmui_collapsedTitleGravity">
        <flag name="bottom" value="0x00000050" />
        <flag name="center" value="0x00000011" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="end" value="0x00800005" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="start" value="0x00800003" />
        <flag name="top" value="0x00000030" />
    </attr>
    <attr name="qmui_collapsedTitleTextAppearance" format="reference" />
    <attr name="qmui_commonList_detailColor" format="color" />
    <attr name="qmui_commonList_titleColor" format="color" />
    <attr name="qmui_common_list_item_accessory_margin_left" format="dimension" />
    <attr name="qmui_common_list_item_chevron" format="reference" />
    <attr name="qmui_common_list_item_detail_h_text_size" format="dimension" />
    <attr name="qmui_common_list_item_detail_line_space" format="dimension" />
    <attr name="qmui_common_list_item_detail_v_text_size" format="dimension" />
    <attr name="qmui_common_list_item_h_space_min_width" format="dimension" />
    <attr name="qmui_common_list_item_icon_margin_right" format="dimension" />
    <attr name="qmui_common_list_item_switch" format="reference" />
    <attr name="qmui_common_list_item_title_h_text_size" format="dimension" />
    <attr name="qmui_common_list_item_title_v_text_size" format="dimension" />
    <attr name="qmui_config_color_background" format="color" />
    <attr name="qmui_config_color_background_pressed" format="color" />
    <attr name="qmui_config_color_black" format="color" />
    <attr name="qmui_config_color_blue" format="color" />
    <attr name="qmui_config_color_gray_1" format="color" />
    <attr name="qmui_config_color_gray_2" format="color" />
    <attr name="qmui_config_color_gray_3" format="color" />
    <attr name="qmui_config_color_gray_4" format="color" />
    <attr name="qmui_config_color_gray_5" format="color" />
    <attr name="qmui_config_color_gray_6" format="color" />
    <attr name="qmui_config_color_gray_7" format="color" />
    <attr name="qmui_config_color_gray_8" format="color" />
    <attr name="qmui_config_color_gray_9" format="color" />
    <attr name="qmui_config_color_link" format="color" />
    <attr name="qmui_config_color_pressed" format="color" />
    <attr name="qmui_config_color_red" format="color" />
    <attr name="qmui_config_color_separator" format="color" />
    <attr name="qmui_config_color_separator_darken" format="color" />
    <attr name="qmui_contentScrim" format="color" />
    <attr name="qmui_content_padding_horizontal" format="dimension" />
    <attr name="qmui_content_spacing_horizontal" format="dimension" />
    <attr name="qmui_corner_radius" format="dimension" />
    <attr name="qmui_detail_text" format="string" />
    <attr name="qmui_dialog_action_button_padding_horizontal" format="dimension" />
    <attr name="qmui_dialog_action_container_custom_space_index" format="integer" />
    <attr name="qmui_dialog_action_container_justify_content">
        <enum name="custom" value="3" />
        <enum name="end" value="1" />
        <enum name="start" value="0" />
        <enum name="stretch" value="2" />
    </attr>
    <attr name="qmui_dialog_action_container_style" format="reference" />
    <attr name="qmui_dialog_action_height" format="dimension" />
    <attr name="qmui_dialog_action_icon_space" format="dimension" />
    <attr name="qmui_dialog_action_space" format="dimension" />
    <attr name="qmui_dialog_action_style" format="reference" />
    <attr name="qmui_dialog_background_dim_amount" format="float" />
    <attr name="qmui_dialog_bg" format="reference" />
    <attr name="qmui_dialog_edit_content_style" format="reference" />
    <attr name="qmui_dialog_margin_vertical" format="dimension" />
    <attr name="qmui_dialog_max_width" format="dimension" />
    <attr name="qmui_dialog_menu_container_padding_bottom_when_action_exist" format="dimension" />
    <attr name="qmui_dialog_menu_container_padding_top_when_title_exist" format="dimension" />
    <attr name="qmui_dialog_menu_container_single_padding_vertical" format="dimension" />
    <attr name="qmui_dialog_menu_container_style" format="reference" />
    <attr name="qmui_dialog_menu_item_check_drawable" format="reference" />
    <attr name="qmui_dialog_menu_item_check_mark_margin_hor" format="dimension" />
    <attr name="qmui_dialog_menu_item_height" format="dimension" />
    <attr name="qmui_dialog_menu_item_mark_drawable" format="reference" />
    <attr name="qmui_dialog_menu_item_style" format="reference" />
    <attr name="qmui_dialog_message_content_style" format="reference" />
    <attr name="qmui_dialog_min_width" format="dimension" />
    <attr name="qmui_dialog_negative_action_text_color" format="reference|color" />
    <attr name="qmui_dialog_padding_horizontal" format="dimension" />
    <attr name="qmui_dialog_positive_action_text_color" format="reference|color" />
    <attr name="qmui_dialog_radius" format="dimension" />
    <attr name="qmui_dialog_title_style" format="reference" />
    <attr name="qmui_dialog_wrapper_style" format="reference" />
    <attr name="qmui_equal_target_refresh_offset_to_refresh_view_height" format="boolean" />
    <attr name="qmui_expandedTitleGravity">
        <flag name="bottom" value="0x00000050" />
        <flag name="center" value="0x00000011" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="end" value="0x00800005" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="start" value="0x00800003" />
        <flag name="top" value="0x00000030" />
    </attr>
    <attr name="qmui_expandedTitleMargin" format="dimension" />
    <attr name="qmui_expandedTitleMarginBottom" format="dimension" />
    <attr name="qmui_expandedTitleMarginEnd" format="dimension" />
    <attr name="qmui_expandedTitleMarginStart" format="dimension" />
    <attr name="qmui_expandedTitleMarginTop" format="dimension" />
    <attr name="qmui_expandedTitleTextAppearance" format="reference" />
    <attr name="qmui_general_shadow_alpha" format="float" />
    <attr name="qmui_general_shadow_elevation" format="dimension" />
    <attr name="qmui_hideRadiusSide">
        <enum name="bottom" value="3" />
        <enum name="left" value="4" />
        <enum name="none" value="0" />
        <enum name="right" value="2" />
        <enum name="top" value="1" />
    </attr>
    <attr name="qmui_icon_check_mark" format="reference" />
    <attr name="qmui_isRadiusAdjustBounds" format="boolean" />
    <attr name="qmui_is_circle" format="boolean" />
    <attr name="qmui_is_oval" format="boolean" />
    <attr name="qmui_is_touch_select_mode_enabled" format="boolean" />
    <attr name="qmui_layout_collapseMode">
        <enum name="none" value="0" />
        <enum name="parallax" value="2" />
        <enum name="pin" value="1" />
    </attr>
    <attr name="qmui_layout_collapseParallaxMultiplier" format="float" />
    <attr name="qmui_layout_miniContentProtectionSize" format="dimension" />
    <attr name="qmui_layout_priority">
        <enum name="disposable" value="1" />
        <enum name="incompressible" value="3" />
        <enum name="mini_content_protection" value="2" />
    </attr>
    <attr name="qmui_leftDividerColor" format="reference|color" />
    <attr name="qmui_leftDividerInsetBottom" format="dimension" />
    <attr name="qmui_leftDividerInsetTop" format="dimension" />
    <attr name="qmui_leftDividerWidth" format="dimension" />
    <attr name="qmui_linkBackgroundColor" format="color" />
    <attr name="qmui_linkColor" format="color" />
    <attr name="qmui_linkTextColor" format="color" />
    <attr name="qmui_list_item_bg_with_border_bottom" format="reference" />
    <attr name="qmui_list_item_bg_with_border_bottom_inset_left" format="reference" />
    <attr name="qmui_list_item_bg_with_border_bottom_inset_left_pressed" format="reference" />
    <attr name="qmui_list_item_bg_with_border_bottom_pressed" format="reference" />
    <attr name="qmui_list_item_bg_with_border_double" format="reference" />
    <attr name="qmui_list_item_bg_with_border_double_pressed" format="reference" />
    <attr name="qmui_list_item_bg_with_border_top" format="reference" />
    <attr name="qmui_list_item_bg_with_border_top_inset_left" format="reference" />
    <attr name="qmui_list_item_bg_with_border_top_inset_left_pressed" format="reference" />
    <attr name="qmui_list_item_bg_with_border_top_pressed" format="reference" />
    <attr name="qmui_list_item_height" format="dimension" />
    <attr name="qmui_list_item_height_higher" format="dimension" />
    <attr name="qmui_loading_color" format="color" />
    <attr name="qmui_loading_size" format="dimension" />
    <attr name="qmui_loading_view_size" format="dimension" />
    <attr name="qmui_maxNumber" format="integer" />
    <attr name="qmui_maxTextSize" format="dimension" />
    <attr name="qmui_max_value" format="integer" />
    <attr name="qmui_minTextSize" format="dimension" />
    <attr name="qmui_more_action_color" format="color" />
    <attr name="qmui_more_action_text" format="string" />
    <attr name="qmui_orientation">
        <enum name="horizontal" value="0" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="qmui_outerNormalColor" format="reference|color" />
    <attr name="qmui_outlineExcludePadding" format="boolean" />
    <attr name="qmui_outlineInsetBottom" format="dimension" />
    <attr name="qmui_outlineInsetLeft" format="dimension" />
    <attr name="qmui_outlineInsetRight" format="dimension" />
    <attr name="qmui_outlineInsetTop" format="dimension" />
    <attr name="qmui_paddingBottomWhenNotContent" format="dimension" />
    <attr name="qmui_paddingTopWhenNotTitle" format="dimension" />
    <attr name="qmui_popup_arrow_down" format="reference" />
    <attr name="qmui_popup_arrow_down_margin_bottom" format="dimension" />
    <attr name="qmui_popup_arrow_up" format="reference" />
    <attr name="qmui_popup_arrow_up_margin_top" format="dimension" />
    <attr name="qmui_popup_bg" format="reference" />
    <attr name="qmui_progress_color" format="color" />
    <attr name="qmui_radius" format="dimension" />
    <attr name="qmui_radiusBottomLeft" format="dimension" />
    <attr name="qmui_radiusBottomRight" format="dimension" />
    <attr name="qmui_radiusTopLeft" format="dimension" />
    <attr name="qmui_radiusTopRight" format="dimension" />
    <attr name="qmui_refresh_end_offset" format="dimension" />
    <attr name="qmui_refresh_init_offset" format="dimension" />
    <attr name="qmui_rightDividerColor" format="reference|color" />
    <attr name="qmui_rightDividerInsetBottom" format="dimension" />
    <attr name="qmui_rightDividerInsetTop" format="dimension" />
    <attr name="qmui_rightDividerWidth" format="dimension" />
    <attr name="qmui_round_btn_bg_color" format="color" />
    <attr name="qmui_round_btn_border_color" format="color" />
    <attr name="qmui_round_btn_border_width" format="dimension" />
    <attr name="qmui_round_btn_text_color" format="color" />
    <attr name="qmui_round_btn_text_size" format="dimension" />
    <attr name="qmui_s_checkbox" format="reference" />
    <attr name="qmui_s_list_item_bg_with_border_bottom" format="reference" />
    <attr name="qmui_s_list_item_bg_with_border_bottom_inset" format="reference" />
    <attr name="qmui_s_list_item_bg_with_border_bottom_inset_left" format="reference" />
    <attr name="qmui_s_list_item_bg_with_border_double" format="reference" />
    <attr name="qmui_s_list_item_bg_with_border_none" format="reference" />
    <attr name="qmui_s_list_item_bg_with_border_top" format="reference" />
    <attr name="qmui_s_list_item_bg_with_border_top_inset_left" format="reference" />
    <attr name="qmui_scrimAnimationDuration" format="integer" />
    <attr name="qmui_scrimVisibleHeightTrigger" format="dimension" />
    <attr name="qmui_selected_border_color" format="color" />
    <attr name="qmui_selected_border_width" format="dimension" />
    <attr name="qmui_selected_mask_color" format="color" />
    <attr name="qmui_shadowAlpha" format="float" />
    <attr name="qmui_shadowElevation" format="dimension" />
    <attr name="qmui_showBorderOnlyBeforeL" format="boolean" />
    <attr name="qmui_show_loading" format="boolean" />
    <attr name="qmui_special_drawable_padding" format="dimension" />
    <attr name="qmui_statusBarScrim" format="color" />
    <attr name="qmui_stroke_round_cap" format="boolean" />
    <attr name="qmui_stroke_width" format="dimension" />
    <attr name="qmui_tab_has_indicator" format="boolean" />
    <attr name="qmui_tab_icon_position">
        <enum name="bottom" value="3" />
        <enum name="left" value="0" />
        <enum name="right" value="2" />
        <enum name="top" value="1" />
    </attr>
    <attr name="qmui_tab_indicator_height" format="dimension" />
    <attr name="qmui_tab_indicator_top" format="boolean" />
    <attr name="qmui_tab_mode">
        <enum name="fixed" value="1" />
        <enum name="scrollable" value="0" />
    </attr>
    <attr name="qmui_tab_sign_count_view" format="reference" />
    <attr name="qmui_tab_sign_count_view_bg" format="reference" />
    <attr name="qmui_tab_sign_count_view_minSize" format="dimension" />
    <attr name="qmui_tab_sign_count_view_minSize_with_text" format="dimension" />
    <attr name="qmui_tab_sign_count_view_padding_horizontal" format="dimension" />
    <attr name="qmui_tab_space" format="dimension" />
    <attr name="qmui_tab_typeface_provider" format="string" />
    <attr name="qmui_target_init_offset" format="dimension" />
    <attr name="qmui_target_refresh_offset" format="dimension" />
    <attr name="qmui_tip_dialog_bg" format="reference" />
    <attr name="qmui_tip_dialog_margin_horizontal" format="dimension" />
    <attr name="qmui_tip_dialog_min_height" format="dimension" />
    <attr name="qmui_tip_dialog_min_width" format="dimension" />
    <attr name="qmui_tip_dialog_padding_horizontal" format="dimension" />
    <attr name="qmui_tip_dialog_padding_vertical" format="dimension" />
    <attr name="qmui_title" format="string" />
    <attr name="qmui_titleEnabled" format="boolean" />
    <attr name="qmui_title_text" format="string" />
    <attr name="qmui_topBarId" format="reference" />
    <attr name="qmui_topDividerColor" format="reference|color" />
    <attr name="qmui_topDividerHeight" format="dimension" />
    <attr name="qmui_topDividerInsetLeft" format="dimension" />
    <attr name="qmui_topDividerInsetRight" format="dimension" />
    <attr name="qmui_topbar_bg_color" format="color" />
    <attr name="qmui_topbar_height" format="dimension" />
    <attr name="qmui_topbar_image_btn_height" format="dimension" />
    <attr name="qmui_topbar_image_btn_width" format="dimension" />
    <attr name="qmui_topbar_left_back_drawable_id" format="reference" />
    <attr name="qmui_topbar_need_separator" format="boolean" />
    <attr name="qmui_topbar_separator_color" format="color" />
    <attr name="qmui_topbar_separator_height" format="dimension" />
    <attr name="qmui_topbar_subtitle_color" format="color" />
    <attr name="qmui_topbar_subtitle_text_size" format="dimension" />
    <attr name="qmui_topbar_text_btn_color_state_list" format="reference" />
    <attr name="qmui_topbar_text_btn_padding_horizontal" format="dimension" />
    <attr name="qmui_topbar_text_btn_text_size" format="dimension" />
    <attr name="qmui_topbar_title_color" format="color" />
    <attr name="qmui_topbar_title_container_padding_horizontal" format="dimension" />
    <attr name="qmui_topbar_title_gravity">
        <enum name="center" value="17" />
        <enum name="left_center" value="19" />
    </attr>
    <attr name="qmui_topbar_title_margin_horizontal_when_no_btn_aside" format="dimension" />
    <attr name="qmui_topbar_title_text_size" format="dimension" />
    <attr name="qmui_topbar_title_text_size_with_subtitle" format="dimension" />
    <attr name="qmui_type">
        <enum name="type_circle" value="1" />
        <enum name="type_rect" value="0" />
    </attr>
    <attr name="qmui_useThemeGeneralShadowElevation" format="boolean" />
    <attr name="qmui_value" format="integer" />
    <attr name="queryBackground" format="reference" />
    <attr name="queryHint" format="string" />
    <attr name="radioButtonStyle" format="reference" />
    <attr name="ratingBarStyle" format="reference" />
    <attr name="ratingBarStyleIndicator" format="reference" />
    <attr name="ratingBarStyleSmall" format="reference" />
    <attr name="realtimeBlurRadius" format="dimension" />
    <attr name="realtimeDownsampleFactor" format="float" />
    <attr name="realtimeOverlayColor" format="color" />
    <attr name="reparent" format="boolean" />
    <attr name="reparentWithOverlay" format="boolean" />
    <attr name="repeat_toggle_modes">
        <flag name="all" value="0x00000002" />
        <flag name="none" value="0x00000000" />
        <flag name="one" value="0x00000001" />
    </attr>
    <attr name="resizeClip" format="boolean" />
    <attr name="resize_mode">
        <enum name="fill" value="3" />
        <enum name="fit" value="0" />
        <enum name="fixed_height" value="2" />
        <enum name="fixed_width" value="1" />
        <enum name="zoom" value="4" />
    </attr>
    <attr name="reverseLayout" format="boolean" />
    <attr name="rewind_increment" format="integer" />
    <attr name="rippleColor" format="color" />
    <attr name="scrimAnimationDuration" format="integer" />
    <attr name="scrimBackground" format="reference|color" />
    <attr name="scrimVisibleHeightTrigger" format="dimension" />
    <attr name="scrubber_color" format="color" />
    <attr name="scrubber_disabled_size" format="dimension" />
    <attr name="scrubber_dragged_size" format="dimension" />
    <attr name="scrubber_drawable" format="reference" />
    <attr name="scrubber_enabled_size" format="dimension" />
    <attr name="searchHintIcon" format="reference" />
    <attr name="searchIcon" format="reference" />
    <attr name="searchViewStyle" format="reference" />
    <attr name="seekBarStyle" format="reference" />
    <attr name="selectableItemBackground" format="reference" />
    <attr name="selectableItemBackgroundBorderless" format="reference" />
    <attr name="separatorStyle">
        <enum name="none" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="showAsAction">
        <flag name="always" value="0x00000002" />
        <flag name="collapseActionView" value="0x00000008" />
        <flag name="ifRoom" value="0x00000001" />
        <flag name="never" value="0x00000000" />
        <flag name="withText" value="0x00000004" />
    </attr>
    <attr name="showDividers">
        <flag name="beginning" value="0x00000001" />
        <flag name="end" value="0x00000004" />
        <flag name="middle" value="0x00000002" />
        <flag name="none" value="0x00000000" />
    </attr>
    <attr name="showMotionSpec" format="reference" />
    <attr name="showText" format="boolean" />
    <attr name="showTitle" format="boolean" />
    <attr name="show_buffering">
        <enum name="always" value="2" />
        <enum name="never" value="0" />
        <enum name="when_playing" value="1" />
    </attr>
    <attr name="show_shuffle_button" format="boolean" />
    <attr name="show_timeout" format="integer" />
    <attr name="shutter_background_color" format="color" />
    <attr name="singleChoiceItemLayout" format="reference" />
    <attr name="singleLine" format="boolean" />
    <attr name="singleSelection" format="boolean" />
    <attr name="slideEdge">
        <enum name="bottom" value="80" />
        <enum name="left" value="3" />
        <enum name="right" value="5" />
        <enum name="top" value="48" />
    </attr>
    <attr name="snackbarButtonStyle" format="reference" />
    <attr name="snackbarStyle" format="reference" />
    <attr name="spanCount" format="integer" />
    <attr name="spinBars" format="boolean" />
    <attr name="spinnerDropDownItemStyle" format="reference" />
    <attr name="spinnerStyle" format="reference" />
    <attr name="splitTrack" format="boolean" />
    <attr name="srcCompat" format="reference" />
    <attr name="stackFromEnd" format="boolean" />
    <attr name="startDelay" format="integer" />
    <attr name="state_above_anchor" format="boolean" />
    <attr name="state_collapsed" format="boolean" />
    <attr name="state_collapsible" format="boolean" />
    <attr name="state_liftable" format="boolean" />
    <attr name="state_lifted" format="boolean" />
    <attr name="statusBarBackground" format="reference|color" />
    <attr name="statusBarScrim" format="color" />
    <attr name="strokeColor" format="color" />
    <attr name="strokeWidth" format="dimension" />
    <attr name="subMenuArrow" format="reference" />
    <attr name="submitBackground" format="reference" />
    <attr name="subtitle" format="string" />
    <attr name="subtitleTextAppearance" format="reference" />
    <attr name="subtitleTextColor" format="color" />
    <attr name="subtitleTextStyle" format="reference" />
    <attr name="suggestionRowLayout" format="reference" />
    <attr name="surface_type">
        <enum name="none" value="0" />
        <enum name="spherical_view" value="3" />
        <enum name="surface_view" value="1" />
        <enum name="texture_view" value="2" />
    </attr>
    <attr name="switchMinWidth" format="dimension" />
    <attr name="switchPadding" format="dimension" />
    <attr name="switchStyle" format="reference" />
    <attr name="switchTextAppearance" format="reference" />
    <attr name="tabBackground" format="reference" />
    <attr name="tabContentStart" format="dimension" />
    <attr name="tabGravity">
        <enum name="center" value="1" />
        <enum name="fill" value="0" />
    </attr>
    <attr name="tabIconTint" format="color" />
    <attr name="tabIconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tabIndicator" format="reference" />
    <attr name="tabIndicatorAnimationDuration" format="integer" />
    <attr name="tabIndicatorColor" format="color" />
    <attr name="tabIndicatorFullWidth" format="boolean" />
    <attr name="tabIndicatorGravity">
        <enum name="bottom" value="0" />
        <enum name="center" value="1" />
        <enum name="stretch" value="3" />
        <enum name="top" value="2" />
    </attr>
    <attr name="tabIndicatorHeight" format="dimension" />
    <attr name="tabInlineLabel" format="boolean" />
    <attr name="tabMaxWidth" format="dimension" />
    <attr name="tabMinWidth" format="dimension" />
    <attr name="tabMode">
        <enum name="fixed" value="1" />
        <enum name="scrollable" value="0" />
    </attr>
    <attr name="tabPadding" format="dimension" />
    <attr name="tabPaddingBottom" format="dimension" />
    <attr name="tabPaddingEnd" format="dimension" />
    <attr name="tabPaddingStart" format="dimension" />
    <attr name="tabPaddingTop" format="dimension" />
    <attr name="tabRippleColor" format="color" />
    <attr name="tabSelectedTextColor" format="color" />
    <attr name="tabStyle" format="reference" />
    <attr name="tabTextAppearance" format="reference" />
    <attr name="tabTextColor" format="color" />
    <attr name="tabUnboundedRipple" format="boolean" />
    <attr name="targetClass" format="string" />
    <attr name="targetId" format="reference" />
    <attr name="targetName" format="string" />
    <attr name="textAllCaps" format="reference|boolean" />
    <attr name="textAppearanceBody1" format="reference" />
    <attr name="textAppearanceBody2" format="reference" />
    <attr name="textAppearanceButton" format="reference" />
    <attr name="textAppearanceCaption" format="reference" />
    <attr name="textAppearanceHeadline1" format="reference" />
    <attr name="textAppearanceHeadline2" format="reference" />
    <attr name="textAppearanceHeadline3" format="reference" />
    <attr name="textAppearanceHeadline4" format="reference" />
    <attr name="textAppearanceHeadline5" format="reference" />
    <attr name="textAppearanceHeadline6" format="reference" />
    <attr name="textAppearanceLargePopupMenu" format="reference" />
    <attr name="textAppearanceListItem" format="reference" />
    <attr name="textAppearanceListItemSecondary" format="reference" />
    <attr name="textAppearanceListItemSmall" format="reference" />
    <attr name="textAppearanceOverline" format="reference" />
    <attr name="textAppearancePopupMenuHeader" format="reference" />
    <attr name="textAppearanceSearchResultSubtitle" format="reference" />
    <attr name="textAppearanceSearchResultTitle" format="reference" />
    <attr name="textAppearanceSmallPopupMenu" format="reference" />
    <attr name="textAppearanceSubtitle1" format="reference" />
    <attr name="textAppearanceSubtitle2" format="reference" />
    <attr name="textColorAlertDialogListItem" format="reference|color" />
    <attr name="textColorSearchUrl" format="reference|color" />
    <attr name="textEndPadding" format="dimension" />
    <attr name="textInputStyle" format="reference" />
    <attr name="textStartPadding" format="dimension" />
    <attr name="theme" format="reference" />
    <attr name="thickness" format="dimension" />
    <attr name="thumbTextPadding" format="dimension" />
    <attr name="thumbTint" format="color" />
    <attr name="thumbTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tickMark" format="reference" />
    <attr name="tickMarkTint" format="color" />
    <attr name="tickMarkTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tint" format="color" />
    <attr name="tintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="title" format="string" />
    <attr name="titleEnabled" format="boolean" />
    <attr name="titleMargin" format="dimension" />
    <attr name="titleMarginBottom" format="dimension" />
    <attr name="titleMarginEnd" format="dimension" />
    <attr name="titleMarginStart" format="dimension" />
    <attr name="titleMarginTop" format="dimension" />
    <attr name="titleMargins" format="dimension" />
    <attr name="titleTextAppearance" format="reference" />
    <attr name="titleTextColor" format="color" />
    <attr name="titleTextStyle" format="reference" />
    <attr name="toScene" format="reference" />
    <attr name="toolbarId" format="reference" />
    <attr name="toolbarNavigationButtonStyle" format="reference" />
    <attr name="toolbarStyle" format="reference" />
    <attr name="tooltipForegroundColor" format="reference|color" />
    <attr name="tooltipFrameBackground" format="reference" />
    <attr name="tooltipText" format="string" />
    <attr name="touch_target_height" format="dimension" />
    <attr name="track" format="reference" />
    <attr name="trackTint" format="color" />
    <attr name="trackTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="transition" format="reference" />
    <attr name="transitionOrdering">
        <enum name="sequential" value="1" />
        <enum name="together" value="0" />
    </attr>
    <attr name="transitionVisibilityMode">
        <flag name="mode_in" value="0x00000001" />
        <flag name="mode_out" value="0x00000002" />
    </attr>
    <attr name="ttcIndex" format="integer" />
    <attr name="tv_colSpan" format="integer" />
    <attr name="tv_horizontalDivider" format="reference" />
    <attr name="tv_horizontalSpacingWithMargins" format="dimension" />
    <attr name="tv_isIntelligentScroll" format="boolean" />
    <attr name="tv_isMemoryFocus" format="boolean" />
    <attr name="tv_isMenu" format="boolean" />
    <attr name="tv_laneCountsStr" format="string" />
    <attr name="tv_layoutManager" format="string" />
    <attr name="tv_loadMoreBeforehandCount" format="integer" />
    <attr name="tv_numColumns" format="integer" />
    <attr name="tv_numRows" format="integer" />
    <attr name="tv_optimizeLayout" format="boolean" />
    <attr name="tv_rowSpan" format="integer" />
    <attr name="tv_selectedItemIsCentered" format="boolean" />
    <attr name="tv_selectedItemOffsetEnd" format="dimension" />
    <attr name="tv_selectedItemOffsetStart" format="dimension" />
    <attr name="tv_span" format="integer" />
    <attr name="tv_verticalDivider" format="reference" />
    <attr name="tv_verticalSpacingWithMargins" format="dimension" />
    <attr name="unplayed_color" format="color" />
    <attr name="useCompatPadding" format="boolean" />
    <attr name="use_artwork" format="boolean" />
    <attr name="use_controller" format="boolean" />
    <attr name="viewInflaterClass" format="string" />
    <attr name="voiceIcon" format="reference" />
    <attr name="windowActionBar" format="boolean" />
    <attr name="windowActionBarOverlay" format="boolean" />
    <attr name="windowActionModeOverlay" format="boolean" />
    <attr name="windowFixedHeightMajor" format="dimension|fraction" />
    <attr name="windowFixedHeightMinor" format="dimension|fraction" />
    <attr name="windowFixedWidthMajor" format="dimension|fraction" />
    <attr name="windowFixedWidthMinor" format="dimension|fraction" />
    <attr name="windowMinWidthMajor" format="dimension|fraction" />
    <attr name="windowMinWidthMinor" format="dimension|fraction" />
    <attr name="windowNoTitle" format="boolean" />
</resources>
