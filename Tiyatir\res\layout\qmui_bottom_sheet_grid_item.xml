<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.widget.dialog.QMUIBottomSheetItemView android:gravity="center_horizontal" android:orientation="vertical" android:paddingTop="?qmui_bottom_sheet_grid_item_paddingTop" android:paddingBottom="?qmui_bottom_sheet_grid_item_paddingBottom" android:layout_width="?qmui_bottom_sheet_grid_item_mini_width" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:layout_width="?qmui_bottom_sheet_grid_item_icon_size" android:layout_height="?qmui_bottom_sheet_grid_item_icon_size" android:layout_marginTop="?qmui_bottom_sheet_grid_item_icon_marginTop" android:layout_marginBottom="?qmui_bottom_sheet_grid_item_icon_marginBottom">
        <android.support.v7.widget.AppCompatImageView android:id="@id/grid_item_image" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="centerInside" android:layout_centerInParent="true" />
        <ViewStub android:id="@id/grid_item_subscript" android:visibility="gone" android:layout="@layout/qmui_bottom_sheet_grid_item_subscript" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentTop="true" android:layout_alignParentRight="true" />
    </RelativeLayout>
    <TextView android:textAppearance="?qmui_bottom_sheet_grid_item_text_appearance" android:gravity="center_horizontal" android:id="@id/grid_item_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="2" />
</com.qmuiteam.qmui.widget.dialog.QMUIBottomSheetItemView>
