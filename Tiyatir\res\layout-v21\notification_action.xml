<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="horizontal" android:id="@id/action_container" android:layout_width="0.0dip" android:layout_height="48.0dip" android:layout_weight="1.0" android:paddingStart="4.0dip" style="@style/Widget.Compat.NotificationActionContainer"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:layout_gravity="start|center" android:id="@id/action_image" android:layout_width="@dimen/notification_action_icon_size" android:layout_height="@dimen/notification_action_icon_size" android:scaleType="centerInside" />
    <TextView android:ellipsize="end" android:layout_gravity="start|center" android:id="@id/action_text" android:clickable="false" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true" android:paddingStart="4.0dip" style="@style/Widget.Compat.NotificationActionText" />
</LinearLayout>
