.class public interface abstract Landroid/support/design/chip/ChipGroup$OnCheckedChangeListener;
.super Ljava/lang/Object;
.source "ChipGroup.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/design/chip/ChipGroup;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnCheckedChangeListener"
.end annotation


# virtual methods
.method public abstract onCheckedChanged(Landroid/support/design/chip/ChipGroup;I)V
.end method
