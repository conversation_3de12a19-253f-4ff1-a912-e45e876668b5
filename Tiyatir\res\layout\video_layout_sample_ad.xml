<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="@android:color/black" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:gravity="center" android:id="@id/surface_container" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <RelativeLayout android:gravity="center" android:id="@id/widget_container" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <RelativeLayout android:id="@id/thumb" android:background="#ff000000" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="fitCenter" android:layout_alignParentLeft="true" android:layout_alignParentTop="true" android:layout_alignParentRight="true" android:layout_alignParentBottom="true" />
        <ProgressBar android:id="@id/bottom_progressbar" android:layout_width="fill_parent" android:layout_height="1.5dip" android:max="100" android:progressDrawable="@drawable/video_progress" android:layout_alignParentBottom="true" style="?android:progressBarStyleHorizontal" />
        <ImageView android:id="@id/back_tiny" android:visibility="gone" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginLeft="6.0dip" android:layout_marginTop="6.0dip" />
        <LinearLayout android:gravity="center_vertical" android:id="@id/layout_top" android:background="@drawable/video_title_bg" android:layout_width="fill_parent" android:layout_height="48.0dip">
            <ImageView android:id="@id/back" android:paddingLeft="10.0dip" android:layout_width="48.0dip" android:layout_height="48.0dip" android:src="@drawable/video_back" android:scaleType="centerInside" />
            <TextView android:textSize="18.0sp" android:textColor="@android:color/white" android:id="@id/title" android:paddingLeft="10.0dip" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        </LinearLayout>
        <moe.codeest.enviews.ENDownloadView android:id="@id/loading" android:visibility="invisible" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_centerHorizontal="true" android:layout_centerVertical="true" />
        <ImageView android:layout_gravity="center_vertical" android:id="@id/start" android:layout_width="60.0dip" android:layout_height="60.0dip" android:src="@drawable/video_click_play_selector" android:layout_centerHorizontal="true" android:layout_centerVertical="true" />
        <ImageView android:id="@id/small_close" android:paddingLeft="10.0dip" android:paddingTop="10.0dip" android:visibility="gone" android:layout_width="30.0dip" android:layout_height="30.0dip" android:src="@drawable/video_small_close" android:scaleType="centerInside" />
        <ImageView android:id="@id/lock_screen" android:visibility="gone" android:layout_width="30.0dip" android:layout_height="30.0dip" android:layout_marginRight="50.0dip" android:src="@drawable/unlock" android:scaleType="centerInside" android:layout_alignParentRight="true" android:layout_centerVertical="true" />
    </RelativeLayout>
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/layout_bottom" android:background="@color/bottom_container_bg" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_alignParentBottom="true">
        <TextView android:textColor="#ffffffff" android:id="@id/current" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="16.0dip" android:text="00:00" />
        <SeekBar android:layout_gravity="center_vertical" android:id="@id/progress" android:background="@null" android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxHeight="4.0dip" android:max="100" android:progressDrawable="@drawable/video_seek_progress" android:minHeight="4.0dip" android:thumb="@drawable/video_seek_thumb" android:layout_weight="1.0" />
        <TextView android:textColor="#ffffffff" android:id="@id/total" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginRight="16.0dip" android:text="00:00" />
        <ImageView android:id="@id/fullscreen" android:paddingRight="16.0dip" android:layout_width="wrap_content" android:layout_height="fill_parent" android:src="@drawable/video_enlarge" android:scaleType="center" />
    </LinearLayout>
    <TextView android:textColor="#ffffffff" android:id="@id/jump_ad" android:background="@drawable/video_jump_btn_bg" android:paddingLeft="10.0dip" android:paddingTop="4.0dip" android:paddingRight="10.0dip" android:paddingBottom="4.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="20.0dip" android:layout_marginRight="20.0dip" android:text="@string/jump_ad" android:layout_alignParentRight="true" />
    <TextView android:textColor="#ffffffff" android:id="@id/ad_time" android:background="@drawable/video_jump_btn_bg" android:paddingLeft="10.0dip" android:paddingTop="4.0dip" android:paddingRight="10.0dip" android:paddingBottom="4.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="20.0dip" android:layout_marginTop="20.0dip" android:text="00" android:layout_alignParentLeft="true" />
</RelativeLayout>
