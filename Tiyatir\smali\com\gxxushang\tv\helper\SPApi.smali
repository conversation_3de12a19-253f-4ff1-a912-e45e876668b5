.class public Lcom/gxxushang/tv/helper/SPApi;
.super Ljava/lang/Object;
.source "SPApi.java"

# interfaces
.implements Lokhttp3/Callback;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lokhttp3/Callback;"
    }
.end annotation


# instance fields
.field cacheKey:Ljava/lang/String;

.field cacheTime:I

.field callback:Lcom/gxxushang/tv/helper/SPCallback;

.field completeCallback:Lcom/gxxushang/tv/helper/SPCompleteCallback;

.field method:Ljava/lang/String;

.field param:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field responseType:Lcom/gxxushang/tv/general/SPConstant$SPResponseType;

.field type:Ljava/lang/reflect/Type;


# direct methods
.method public constructor <init>(Ljava/lang/Class;Ljava/lang/String;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "TT;>;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 49
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 42
    iput v0, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheTime:I

    .line 50
    iput-object p2, p0, Lcom/gxxushang/tv/helper/SPApi;->method:Ljava/lang/String;

    .line 51
    new-instance p2, Ljava/util/HashMap;

    invoke-direct {p2}, Ljava/util/HashMap;-><init>()V

    iput-object p2, p0, Lcom/gxxushang/tv/helper/SPApi;->param:Ljava/util/HashMap;

    .line 52
    invoke-static {p1}, Lcom/google/gson/reflect/TypeToken;->get(Ljava/lang/Class;)Lcom/google/gson/reflect/TypeToken;

    move-result-object p1

    invoke-virtual {p1}, Lcom/google/gson/reflect/TypeToken;->getType()Ljava/lang/reflect/Type;

    move-result-object p1

    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPApi;->type:Ljava/lang/reflect/Type;

    return-void
.end method

.method static synthetic lambda$onResponse$0(Ljava/lang/Exception;)V
    .locals 1

    .line 173
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/gxxushang/tv/view/common/SPLoading;->getInstance(Landroid/content/Context;)Lcom/gxxushang/tv/view/common/SPLoading;

    move-result-object v0

    invoke-virtual {v0}, Lcom/gxxushang/tv/view/common/SPLoading;->hide()V

    .line 174
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Ljava/lang/Exception;->getLocalizedMessage()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p0, "##onResponse"

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->showError(Ljava/lang/String;)V

    return-void
.end method

.method static synthetic lambda$updateVip$3(Lcom/gxxushang/tv/helper/SPCommonCallback;Lcom/gxxushang/tv/model/SPVip;ILjava/lang/String;)V
    .locals 4

    if-nez p1, :cond_0

    return-void

    .line 219
    :cond_0
    # 设置VIP状态为永久
    const-string p3, "is_vip"

    const/4 v0, 0x1

    invoke-static {p3, v0}, Lcom/gxxushang/tv/helper/SPUtils;->setLocalData(Ljava/lang/String;I)V

    # 修改VIP对象的到期时间为永久
    const-string p3, "永久VIP"

    iput-object p3, p1, Lcom/gxxushang/tv/model/SPVip;->expire:Ljava/lang/String;

    const-string p3, "VIP会员"

    iput-object p3, p1, Lcom/gxxushang/tv/model/SPVip;->state:Ljava/lang/String;

    # 设置到期时间戳为一个很大的值（2099年）
    const p3, 0x7fffffff

    iput p3, p1, Lcom/gxxushang/tv/model/SPVip;->expired:I

    :goto_0
    if-eqz p0, :cond_2

    .line 225
    invoke-interface {p0}, Lcom/gxxushang/tv/helper/SPCommonCallback;->onComplete()V

    :cond_2
    return-void
.end method

.method public static post(Ljava/lang/Class;Ljava/lang/String;)Lcom/gxxushang/tv/helper/SPApi;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Class<",
            "TT;>;",
            "Ljava/lang/String;",
            ")",
            "Lcom/gxxushang/tv/helper/SPApi<",
            "TT;>;"
        }
    .end annotation

    .line 46
    new-instance v0, Lcom/gxxushang/tv/helper/SPApi;

    invoke-direct {v0, p0, p1}, Lcom/gxxushang/tv/helper/SPApi;-><init>(Ljava/lang/Class;Ljava/lang/String;)V

    return-object v0
.end method

.method private request()V
    .locals 6

    .line 127
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->param:Ljava/util/HashMap;

    iget-object v1, p0, Lcom/gxxushang/tv/helper/SPApi;->method:Ljava/lang/String;

    invoke-static {v0, v1}, Lcom/gxxushang/tv/helper/SPApi;->setupParam(Ljava/util/HashMap;Ljava/lang/String;)Ljava/util/HashMap;

    move-result-object v0

    iput-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->param:Ljava/util/HashMap;

    .line 130
    invoke-virtual {v0}, Ljava/util/HashMap;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->getMd5String(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheKey:Ljava/lang/String;

    .line 131
    iget v0, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheTime:I

    if-lez v0, :cond_0

    .line 132
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v1, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheKey:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "time"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->getLocalDataAsInt(Ljava/lang/String;)I

    move-result v0

    .line 133
    iget v1, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheTime:I

    add-int/2addr v0, v1

    int-to-long v0, v0

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v2

    const-wide/16 v4, 0x3e8

    div-long/2addr v2, v4

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    .line 134
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheKey:Ljava/lang/String;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->getLocalData(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 135
    new-instance v1, Lcom/google/gson/Gson;

    invoke-direct {v1}, Lcom/google/gson/Gson;-><init>()V

    const-class v2, Lcom/gxxushang/tv/model/SPResponse;

    invoke-virtual {v1, v0, v2}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/gxxushang/tv/model/SPResponse;

    .line 136
    invoke-virtual {p0, v0}, Lcom/gxxushang/tv/helper/SPApi;->onCallback(Lcom/gxxushang/tv/model/SPResponse;)V

    return-void

    .line 141
    :cond_0
    new-instance v0, Lokhttp3/logging/HttpLoggingInterceptor;

    invoke-direct {v0}, Lokhttp3/logging/HttpLoggingInterceptor;-><init>()V

    .line 142
    sget-object v1, Lokhttp3/logging/HttpLoggingInterceptor$Level;->NONE:Lokhttp3/logging/HttpLoggingInterceptor$Level;

    invoke-virtual {v0, v1}, Lokhttp3/logging/HttpLoggingInterceptor;->setLevel(Lokhttp3/logging/HttpLoggingInterceptor$Level;)Lokhttp3/logging/HttpLoggingInterceptor;

    .line 143
    new-instance v1, Lokhttp3/OkHttpClient$Builder;

    invoke-direct {v1}, Lokhttp3/OkHttpClient$Builder;-><init>()V

    .line 144
    invoke-virtual {v1, v0}, Lokhttp3/OkHttpClient$Builder;->addInterceptor(Lokhttp3/Interceptor;)Lokhttp3/OkHttpClient$Builder;

    move-result-object v0

    sget-object v1, Ljava/net/Proxy;->NO_PROXY:Ljava/net/Proxy;

    .line 145
    invoke-virtual {v0, v1}, Lokhttp3/OkHttpClient$Builder;->proxy(Ljava/net/Proxy;)Lokhttp3/OkHttpClient$Builder;

    move-result-object v0

    .line 146
    invoke-virtual {v0}, Lokhttp3/OkHttpClient$Builder;->build()Lokhttp3/OkHttpClient;

    move-result-object v0

    .line 147
    new-instance v1, Lokhttp3/FormBody$Builder;

    invoke-direct {v1}, Lokhttp3/FormBody$Builder;-><init>()V

    .line 148
    iget-object v2, p0, Lcom/gxxushang/tv/helper/SPApi;->param:Ljava/util/HashMap;

    invoke-virtual {v2}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v2

    invoke-interface {v2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v2

    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    .line 149
    move-object v4, v3

    check-cast v4, Ljava/lang/String;

    iget-object v5, p0, Lcom/gxxushang/tv/helper/SPApi;->param:Ljava/util/HashMap;

    invoke-virtual {v5, v3}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    invoke-static {v3}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1, v4, v3}, Lokhttp3/FormBody$Builder;->add(Ljava/lang/String;Ljava/lang/String;)Lokhttp3/FormBody$Builder;

    goto :goto_0

    .line 151
    :cond_1
    invoke-virtual {v1}, Lokhttp3/FormBody$Builder;->build()Lokhttp3/FormBody;

    move-result-object v1

    .line 152
    new-instance v2, Lokhttp3/Request$Builder;

    invoke-direct {v2}, Lokhttp3/Request$Builder;-><init>()V

    const-string v3, "https://www.tiyatir.com"

    invoke-virtual {v2, v3}, Lokhttp3/Request$Builder;->url(Ljava/lang/String;)Lokhttp3/Request$Builder;

    move-result-object v2

    invoke-virtual {v2, v1}, Lokhttp3/Request$Builder;->post(Lokhttp3/RequestBody;)Lokhttp3/Request$Builder;

    move-result-object v1

    invoke-virtual {v1}, Lokhttp3/Request$Builder;->build()Lokhttp3/Request;

    move-result-object v1

    .line 153
    invoke-virtual {v0, v1}, Lokhttp3/OkHttpClient;->newCall(Lokhttp3/Request;)Lokhttp3/Call;

    move-result-object v0

    invoke-interface {v0, p0}, Lokhttp3/Call;->enqueue(Lokhttp3/Callback;)V

    return-void
.end method

.method public static setupParam(Ljava/util/HashMap;Ljava/lang/String;)Ljava/util/HashMap;
    .locals 4

    const-string v0, "terminal"

    const-string v1, "app"

    .line 98
    invoke-virtual {p0, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "client_id"

    .line 99
    invoke-virtual {p0, v0}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    if-nez v1, :cond_0

    const-string v1, "26"

    .line 100
    invoke-virtual {p0, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    const-string/jumbo v0, "v"

    const-string v1, "api_1_4"

    .line 102
    invoke-virtual {p0, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string/jumbo v0, "v2"

    const-string v1, "android_tv_1_1"

    .line 103
    invoke-virtual {p0, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "os"

    const-string v1, "android"

    .line 104
    invoke-virtual {p0, v0, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string v0, "impl[api]"

    .line 105
    invoke-virtual {p0, v0, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 106
    const/4 p1, 0x1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    const-string/jumbo v0, "user_id"

    invoke-virtual {p0, v0, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 107
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getWebLang()Ljava/lang/String;

    move-result-object p1

    const-string v0, "lang"

    invoke-virtual {p0, v0, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string p1, "access_token"

    .line 108
    const-string v0, "vip_access_token_bypass"

    invoke-virtual {p0, p1, v0}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    const-string p1, "agent_id"

    .line 109
    invoke-static {p1}, Lcom/gxxushang/tv/helper/SPUtils;->getLocalDataAsInt(Ljava/lang/String;)I

    move-result v0

    if-lez v0, :cond_1

    .line 110
    invoke-static {p1}, Lcom/gxxushang/tv/helper/SPUtils;->getLocalDataAsInt(Ljava/lang/String;)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    const-string v0, "partner_id"

    invoke-virtual {p0, v0, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 113
    :cond_1
    new-instance p1, Ljava/util/TreeSet;

    invoke-direct {p1}, Ljava/util/TreeSet;-><init>()V

    .line 114
    invoke-virtual {p0}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-virtual {p1, v0}, Ljava/util/TreeSet;->addAll(Ljava/util/Collection;)Z

    .line 115
    invoke-virtual {p1}, Ljava/util/TreeSet;->iterator()Ljava/util/Iterator;

    move-result-object p1

    const-string v0, ""

    move-object v1, v0

    :goto_0
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_2

    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 116
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, "="

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0, v2}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    goto :goto_0

    .line 118
    :cond_2
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "467342378648478363423"

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    .line 119
    sget-object v1, Ljava/text/Normalizer$Form;->NFD:Ljava/text/Normalizer$Form;

    invoke-static {p1, v1}, Ljava/text/Normalizer;->normalize(Ljava/lang/CharSequence;Ljava/text/Normalizer$Form;)Ljava/lang/String;

    move-result-object p1

    const-string v1, "[^A-Za-z0-9]"

    .line 120
    invoke-virtual {p1, v1, v0}, Ljava/lang/String;->replaceAll(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    .line 121
    invoke-static {p1}, Lcom/gxxushang/tv/helper/SPUtils;->getMd5String(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "sign"

    invoke-virtual {p0, v0, p1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p0
.end method

.method public static updateVip()V
    .locals 1

    const/4 v0, 0x0

    .line 212
    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPApi;->updateVip(Lcom/gxxushang/tv/helper/SPCommonCallback;)V

    return-void
.end method

.method public static updateVip(Lcom/gxxushang/tv/helper/SPCommonCallback;)V
    .locals 3

    .line 216
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getUserId()I

    move-result v0

    if-nez v0, :cond_0

    return-void

    .line 217
    :cond_0
    const-class v0, Lcom/gxxushang/tv/model/SPVip;

    const-string/jumbo v1, "<EMAIL>"

    invoke-static {v0, v1}, Lcom/gxxushang/tv/helper/SPApi;->post(Ljava/lang/Class;Ljava/lang/String;)Lcom/gxxushang/tv/helper/SPApi;

    move-result-object v0

    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getUserId()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "filter[user_id]"

    invoke-virtual {v0, v2, v1}, Lcom/gxxushang/tv/helper/SPApi;->addParam(Ljava/lang/String;Ljava/lang/Object;)Lcom/gxxushang/tv/helper/SPApi;

    move-result-object v0

    new-instance v1, Lcom/gxxushang/tv/helper/-$$Lambda$SPApi$gNbsKqPZgXJGvNr0glEsgcPqcKQ;

    invoke-direct {v1, p0}, Lcom/gxxushang/tv/helper/-$$Lambda$SPApi$gNbsKqPZgXJGvNr0glEsgcPqcKQ;-><init>(Lcom/gxxushang/tv/helper/SPCommonCallback;)V

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPApi;->onOne(Lcom/gxxushang/tv/helper/SPCallback;)Lcom/gxxushang/tv/helper/SPApi;

    return-void
.end method


# virtual methods
.method public addParam(Lcom/google/gson/internal/LinkedTreeMap;)Lcom/gxxushang/tv/helper/SPApi;
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/google/gson/internal/LinkedTreeMap;",
            ")",
            "Lcom/gxxushang/tv/helper/SPApi<",
            "TT;>;"
        }
    .end annotation

    .line 56
    invoke-static {p1}, Lcom/gxxushang/tv/helper/SPUtils;->linkTreeMapToParam(Lcom/google/gson/internal/LinkedTreeMap;)Ljava/util/HashMap;

    move-result-object p1

    .line 57
    invoke-virtual {p1}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_0

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    .line 58
    iget-object v2, p0, Lcom/gxxushang/tv/helper/SPApi;->param:Ljava/util/HashMap;

    move-object v3, v1

    check-cast v3, Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v2, v3, v1}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_0
    return-object p0
.end method

.method public addParam(Ljava/lang/String;Ljava/lang/Object;)Lcom/gxxushang/tv/helper/SPApi;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/Object;",
            ")",
            "Lcom/gxxushang/tv/helper/SPApi<",
            "TT;>;"
        }
    .end annotation

    .line 65
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->param:Ljava/util/HashMap;

    invoke-virtual {v0, p1, p2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-object p0
.end method

.method public cache(I)Lcom/gxxushang/tv/helper/SPApi;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I)",
            "Lcom/gxxushang/tv/helper/SPApi<",
            "TT;>;"
        }
    .end annotation

    .line 70
    iput p1, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheTime:I

    return-object p0
.end method

.method public synthetic lambda$onCallback$1$SPApi(Lcom/gxxushang/tv/model/SPResponse;)V
    .locals 3

    .line 184
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->callback:Lcom/gxxushang/tv/helper/SPCallback;

    iget v1, p1, Lcom/gxxushang/tv/model/SPResponse;->rc:I

    iget-object p1, p1, Lcom/gxxushang/tv/model/SPResponse;->msg:Ljava/lang/String;

    const/4 v2, 0x0

    invoke-interface {v0, v2, v1, p1}, Lcom/gxxushang/tv/helper/SPCallback;->onSuccess(Ljava/lang/Object;ILjava/lang/String;)V

    return-void
.end method

.method public synthetic lambda$onCallback$2$SPApi(Ljava/lang/Object;Lcom/gxxushang/tv/model/SPResponse;)V
    .locals 3

    .line 203
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->callback:Lcom/gxxushang/tv/helper/SPCallback;

    iget v1, p2, Lcom/gxxushang/tv/model/SPResponse;->rc:I

    iget-object v2, p2, Lcom/gxxushang/tv/model/SPResponse;->msg:Ljava/lang/String;

    invoke-interface {v0, p1, v1, v2}, Lcom/gxxushang/tv/helper/SPCallback;->onSuccess(Ljava/lang/Object;ILjava/lang/String;)V

    .line 204
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPApi;->completeCallback:Lcom/gxxushang/tv/helper/SPCompleteCallback;

    if-eqz p1, :cond_0

    .line 205
    invoke-interface {p1, p2}, Lcom/gxxushang/tv/helper/SPCompleteCallback;->onComplete(Lcom/gxxushang/tv/model/SPResponse;)V

    :cond_0
    return-void
.end method

.method public onAll(Lcom/gxxushang/tv/helper/SPCallback;)Lcom/gxxushang/tv/helper/SPApi;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/gxxushang/tv/helper/SPCallback<",
            "Ljava/util/ArrayList<",
            "TT;>;>;)",
            "Lcom/gxxushang/tv/helper/SPApi<",
            "TT;>;"
        }
    .end annotation

    .line 81
    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPApi;->callback:Lcom/gxxushang/tv/helper/SPCallback;

    .line 82
    sget-object p1, Lcom/gxxushang/tv/general/SPConstant$SPResponseType;->All:Lcom/gxxushang/tv/general/SPConstant$SPResponseType;

    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPApi;->responseType:Lcom/gxxushang/tv/general/SPConstant$SPResponseType;

    .line 83
    invoke-direct {p0}, Lcom/gxxushang/tv/helper/SPApi;->request()V

    return-object p0
.end method

.method public onCallback(Lcom/gxxushang/tv/model/SPResponse;)V
    .locals 5

    .line 181
    iget v0, p1, Lcom/gxxushang/tv/model/SPResponse;->rc:I

    const/4 v1, 0x2

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-gez v0, :cond_0

    new-array v0, v1, [Ljava/lang/Object;

    .line 182
    iget-object v1, p1, Lcom/gxxushang/tv/model/SPResponse;->msg:Ljava/lang/String;

    aput-object v1, v0, v2

    iget v1, p1, Lcom/gxxushang/tv/model/SPResponse;->rc:I

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    aput-object v1, v0, v3

    invoke-static {v0}, Lcom/blankj/utilcode/util/LogUtils;->e([Ljava/lang/Object;)V

    .line 183
    new-instance v0, Lcom/gxxushang/tv/helper/-$$Lambda$SPApi$_2-BHzZql_mp6pXPGG9TJRaRf54;

    invoke-direct {v0, p0, p1}, Lcom/gxxushang/tv/helper/-$$Lambda$SPApi$_2-BHzZql_mp6pXPGG9TJRaRf54;-><init>(Lcom/gxxushang/tv/helper/SPApi;Lcom/gxxushang/tv/model/SPResponse;)V

    invoke-static {v0}, Lcom/blankj/utilcode/util/ThreadUtils;->runOnUiThread(Ljava/lang/Runnable;)V

    return-void

    .line 189
    :cond_0
    sget-object v0, Lcom/gxxushang/tv/helper/SPApi$1;->$SwitchMap$com$gxxushang$tv$general$SPConstant$SPResponseType:[I

    iget-object v4, p0, Lcom/gxxushang/tv/helper/SPApi;->responseType:Lcom/gxxushang/tv/general/SPConstant$SPResponseType;

    invoke-virtual {v4}, Lcom/gxxushang/tv/general/SPConstant$SPResponseType;->ordinal()I

    move-result v4

    aget v0, v0, v4

    if-eq v0, v3, :cond_2

    if-eq v0, v1, :cond_1

    goto :goto_0

    .line 194
    :cond_1
    const-class v0, Ljava/util/ArrayList;

    new-array v1, v3, [Ljava/lang/reflect/Type;

    iget-object v4, p0, Lcom/gxxushang/tv/helper/SPApi;->type:Ljava/lang/reflect/Type;

    aput-object v4, v1, v2

    invoke-static {v0, v1}, Lcom/google/gson/reflect/TypeToken;->getParameterized(Ljava/lang/reflect/Type;[Ljava/lang/reflect/Type;)Lcom/google/gson/reflect/TypeToken;

    move-result-object v0

    invoke-virtual {v0}, Lcom/google/gson/reflect/TypeToken;->getType()Ljava/lang/reflect/Type;

    move-result-object v0

    iput-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->type:Ljava/lang/reflect/Type;

    goto :goto_0

    .line 191
    :cond_2
    const-class v0, Lcom/gxxushang/tv/model/SPPaginate;

    new-array v1, v3, [Ljava/lang/reflect/Type;

    iget-object v4, p0, Lcom/gxxushang/tv/helper/SPApi;->type:Ljava/lang/reflect/Type;

    aput-object v4, v1, v2

    invoke-static {v0, v1}, Lcom/google/gson/reflect/TypeToken;->getParameterized(Ljava/lang/reflect/Type;[Ljava/lang/reflect/Type;)Lcom/google/gson/reflect/TypeToken;

    move-result-object v0

    invoke-virtual {v0}, Lcom/google/gson/reflect/TypeToken;->getType()Ljava/lang/reflect/Type;

    move-result-object v0

    iput-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->type:Ljava/lang/reflect/Type;

    .line 198
    :goto_0
    new-instance v0, Lcom/google/gson/GsonBuilder;

    invoke-direct {v0}, Lcom/google/gson/GsonBuilder;-><init>()V

    new-array v1, v3, [I

    const/16 v4, 0x80

    aput v4, v1, v2

    .line 199
    invoke-virtual {v0, v1}, Lcom/google/gson/GsonBuilder;->excludeFieldsWithModifiers([I)Lcom/google/gson/GsonBuilder;

    .line 200
    const-class v1, Ljava/util/ArrayList;

    new-array v3, v3, [Ljava/lang/reflect/Type;

    const-class v4, Lcom/gxxushang/tv/model/SPViewModel;

    aput-object v4, v3, v2

    invoke-static {v1, v3}, Lcom/google/gson/reflect/TypeToken;->getParameterized(Ljava/lang/reflect/Type;[Ljava/lang/reflect/Type;)Lcom/google/gson/reflect/TypeToken;

    move-result-object v1

    invoke-virtual {v1}, Lcom/google/gson/reflect/TypeToken;->getType()Ljava/lang/reflect/Type;

    move-result-object v1

    new-instance v2, Lcom/gxxushang/tv/model/SPViewModel$Deserializer;

    invoke-direct {v2}, Lcom/gxxushang/tv/model/SPViewModel$Deserializer;-><init>()V

    invoke-virtual {v0, v1, v2}, Lcom/google/gson/GsonBuilder;->registerTypeAdapter(Ljava/lang/reflect/Type;Ljava/lang/Object;)Lcom/google/gson/GsonBuilder;

    .line 201
    invoke-virtual {v0}, Lcom/google/gson/GsonBuilder;->create()Lcom/google/gson/Gson;

    move-result-object v0

    new-instance v1, Lcom/google/gson/Gson;

    invoke-direct {v1}, Lcom/google/gson/Gson;-><init>()V

    iget-object v2, p1, Lcom/gxxushang/tv/model/SPResponse;->data:Ljava/lang/Object;

    invoke-virtual {v1, v2}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lcom/gxxushang/tv/helper/SPApi;->type:Ljava/lang/reflect/Type;

    invoke-virtual {v0, v1, v2}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/reflect/Type;)Ljava/lang/Object;

    move-result-object v0

    .line 202
    new-instance v1, Lcom/gxxushang/tv/helper/-$$Lambda$SPApi$fnVlMtXDhzaYFKNbDiUYAhE1hEE;

    invoke-direct {v1, p0, v0, p1}, Lcom/gxxushang/tv/helper/-$$Lambda$SPApi$fnVlMtXDhzaYFKNbDiUYAhE1hEE;-><init>(Lcom/gxxushang/tv/helper/SPApi;Ljava/lang/Object;Lcom/gxxushang/tv/model/SPResponse;)V

    invoke-static {v1}, Lcom/blankj/utilcode/util/ThreadUtils;->runOnUiThread(Ljava/lang/Runnable;)V

    return-void
.end method

.method public onComplete(Lcom/gxxushang/tv/helper/SPCompleteCallback;)V
    .locals 0

    .line 93
    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPApi;->completeCallback:Lcom/gxxushang/tv/helper/SPCompleteCallback;

    return-void
.end method

.method public onFailure(Lokhttp3/Call;Ljava/io/IOException;)V
    .locals 1

    const/4 p1, 0x1

    new-array p1, p1, [Ljava/lang/Object;

    .line 158
    invoke-virtual {p2}, Ljava/io/IOException;->getLocalizedMessage()Ljava/lang/String;

    move-result-object p2

    const/4 v0, 0x0

    aput-object p2, p1, v0

    invoke-static {p1}, Lcom/blankj/utilcode/util/LogUtils;->a([Ljava/lang/Object;)V

    return-void
.end method

.method public onList(Lcom/gxxushang/tv/helper/SPCallback;)Lcom/gxxushang/tv/helper/SPApi;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/gxxushang/tv/helper/SPCallback<",
            "Lcom/gxxushang/tv/model/SPPaginate<",
            "TT;>;>;)",
            "Lcom/gxxushang/tv/helper/SPApi<",
            "TT;>;"
        }
    .end annotation

    .line 87
    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPApi;->callback:Lcom/gxxushang/tv/helper/SPCallback;

    .line 88
    sget-object p1, Lcom/gxxushang/tv/general/SPConstant$SPResponseType;->Paginate:Lcom/gxxushang/tv/general/SPConstant$SPResponseType;

    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPApi;->responseType:Lcom/gxxushang/tv/general/SPConstant$SPResponseType;

    .line 89
    invoke-direct {p0}, Lcom/gxxushang/tv/helper/SPApi;->request()V

    return-object p0
.end method

.method public onOne(Lcom/gxxushang/tv/helper/SPCallback;)Lcom/gxxushang/tv/helper/SPApi;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/gxxushang/tv/helper/SPCallback<",
            "TT;>;)",
            "Lcom/gxxushang/tv/helper/SPApi<",
            "TT;>;"
        }
    .end annotation

    .line 75
    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPApi;->callback:Lcom/gxxushang/tv/helper/SPCallback;

    .line 76
    sget-object p1, Lcom/gxxushang/tv/general/SPConstant$SPResponseType;->One:Lcom/gxxushang/tv/general/SPConstant$SPResponseType;

    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPApi;->responseType:Lcom/gxxushang/tv/general/SPConstant$SPResponseType;

    .line 77
    invoke-direct {p0}, Lcom/gxxushang/tv/helper/SPApi;->request()V

    return-object p0
.end method

.method public onResponse(Lokhttp3/Call;Lokhttp3/Response;)V
    .locals 5
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 163
    invoke-virtual {p2}, Lokhttp3/Response;->body()Lokhttp3/ResponseBody;

    move-result-object p1

    invoke-virtual {p1}, Lokhttp3/ResponseBody;->string()Ljava/lang/String;

    move-result-object p1

    .line 164
    iget p2, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheTime:I

    if-lez p2, :cond_0

    .line 165
    iget-object p2, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheKey:Ljava/lang/String;

    invoke-static {p2, p1}, Lcom/gxxushang/tv/helper/SPUtils;->setLocalData(Ljava/lang/String;Ljava/lang/String;)V

    .line 166
    new-instance p2, Ljava/lang/StringBuilder;

    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPApi;->cacheKey:Ljava/lang/String;

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "time"

    invoke-virtual {p2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v1

    const-wide/16 v3, 0x3e8

    div-long/2addr v1, v3

    invoke-virtual {v0, v1, v2}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    const-string v1, ""

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {p2, v0}, Lcom/gxxushang/tv/helper/SPUtils;->setLocalData(Ljava/lang/String;Ljava/lang/String;)V

    .line 169
    :cond_0
    :try_start_0
    new-instance p2, Lcom/google/gson/Gson;

    invoke-direct {p2}, Lcom/google/gson/Gson;-><init>()V

    const-class v0, Lcom/gxxushang/tv/model/SPResponse;

    invoke-virtual {p2, p1, v0}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lcom/gxxushang/tv/model/SPResponse;

    .line 170
    invoke-virtual {p0, p1}, Lcom/gxxushang/tv/helper/SPApi;->onCallback(Lcom/gxxushang/tv/model/SPResponse;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 172
    new-instance p2, Lcom/gxxushang/tv/helper/-$$Lambda$SPApi$0hMjhDz9N4PMhXcfHMvH_gNsbww;

    invoke-direct {p2, p1}, Lcom/gxxushang/tv/helper/-$$Lambda$SPApi$0hMjhDz9N4PMhXcfHMvH_gNsbww;-><init>(Ljava/lang/Exception;)V

    invoke-static {p2}, Lcom/blankj/utilcode/util/ThreadUtils;->runOnUiThread(Ljava/lang/Runnable;)V

    :goto_0
    return-void
.end method
