.class public Lcom/gxxushang/tv/helper/SPPlayerManager;
.super Ljava/lang/Object;
.source "SPPlayerManager.java"

# interfaces
.implements Ltv/danmaku/ijk/media/player/IMediaPlayer$OnCompletionListener;
.implements Ltv/danmaku/ijk/media/player/IMediaPlayer$OnInfoListener;
.implements Ltv/danmaku/ijk/media/player/IMediaPlayer$OnBufferingUpdateListener;
.implements Ltv/danmaku/ijk/media/player/IMediaPlayer$OnVideoSizeChangedListener;
.implements Ltv/danmaku/ijk/media/player/IMediaPlayer$OnPreparedListener;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;
    }
.end annotation


# static fields
.field private static volatile mMoviePlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;

.field private static volatile mMusicPlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;


# instance fields
.field public autoPlay:Ljava/lang/Boolean;

.field public controlView:Lcom/gxxushang/tv/base/player/SPBaseControlView;

.field public currentModel:Lcom/gxxushang/tv/model/SPBaseModel;

.field public handler:Landroid/os/Handler;

.field public list:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lcom/gxxushang/tv/model/SPShortVideo;",
            ">;"
        }
    .end annotation
.end field

.field public listener:Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;

.field public locked:Ljava/lang/Boolean;

.field public mContext:Landroid/content/Context;

.field public mediaPlayerSet:Ljava/util/HashMap;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/HashMap<",
            "Ljava/lang/String;",
            "Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;",
            ">;"
        }
    .end annotation
.end field

.field public orientation:I

.field public playRange:I

.field public playerId:Ljava/lang/String;

.field public playerView:Lcom/gxxushang/tv/view/video/SPVideoPlayerView;

.field public schedule:J

.field public title:Ljava/lang/String;

.field public videoRatio:F


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    .line 77
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, 0x0

    .line 42
    iput v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->videoRatio:F

    const/4 v0, 0x0

    .line 44
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    iput-object v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->locked:Ljava/lang/Boolean;

    const/4 v0, 0x1

    .line 45
    invoke-static {v0}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v0

    iput-object v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->autoPlay:Ljava/lang/Boolean;

    .line 78
    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mContext:Landroid/content/Context;

    .line 79
    new-instance p1, Ljava/util/HashMap;

    invoke-direct {p1}, Ljava/util/HashMap;-><init>()V

    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mediaPlayerSet:Ljava/util/HashMap;

    .line 80
    new-instance p1, Ljava/util/ArrayList;

    invoke-direct {p1}, Ljava/util/ArrayList;-><init>()V

    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->list:Ljava/util/ArrayList;

    .line 81
    new-instance p1, Landroid/os/Handler;

    new-instance v0, Lcom/gxxushang/tv/helper/-$$Lambda$SPPlayerManager$MaZfzeiOGvEXoDOdMdatpfh_DLA;

    invoke-direct {v0, p0}, Lcom/gxxushang/tv/helper/-$$Lambda$SPPlayerManager$MaZfzeiOGvEXoDOdMdatpfh_DLA;-><init>(Lcom/gxxushang/tv/helper/SPPlayerManager;)V

    invoke-direct {p1, v0}, Landroid/os/Handler;-><init>(Landroid/os/Handler$Callback;)V

    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->handler:Landroid/os/Handler;

    .line 102
    new-instance p1, Lcom/gxxushang/tv/model/SPBaseModel;

    invoke-direct {p1}, Lcom/gxxushang/tv/model/SPBaseModel;-><init>()V

    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentModel:Lcom/gxxushang/tv/model/SPBaseModel;

    return-void
.end method

.method public static getMoviePlayer(Landroid/content/Context;)Lcom/gxxushang/tv/helper/SPPlayerManager;
    .locals 2

    .line 57
    sget-object v0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mMoviePlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;

    if-nez v0, :cond_1

    .line 58
    const-class v0, Lcom/gxxushang/tv/helper/SPPlayerManager;

    monitor-enter v0

    .line 59
    :try_start_0
    sget-object v1, Lcom/gxxushang/tv/helper/SPPlayerManager;->mMoviePlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;

    if-nez v1, :cond_0

    .line 60
    new-instance v1, Lcom/gxxushang/tv/helper/SPPlayerManager;

    invoke-direct {v1, p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;-><init>(Landroid/content/Context;)V

    sput-object v1, Lcom/gxxushang/tv/helper/SPPlayerManager;->mMoviePlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;

    .line 62
    :cond_0
    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception p0

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p0

    .line 64
    :cond_1
    :goto_0
    sget-object p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mMoviePlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;

    return-object p0
.end method

.method public static getMusicPlayer(Landroid/content/Context;)Lcom/gxxushang/tv/helper/SPPlayerManager;
    .locals 2

    .line 67
    sget-object v0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mMusicPlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;

    if-nez v0, :cond_1

    .line 68
    const-class v0, Lcom/gxxushang/tv/helper/SPPlayerManager;

    monitor-enter v0

    .line 69
    :try_start_0
    sget-object v1, Lcom/gxxushang/tv/helper/SPPlayerManager;->mMusicPlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;

    if-nez v1, :cond_0

    .line 70
    new-instance v1, Lcom/gxxushang/tv/helper/SPPlayerManager;

    invoke-direct {v1, p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;-><init>(Landroid/content/Context;)V

    sput-object v1, Lcom/gxxushang/tv/helper/SPPlayerManager;->mMusicPlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;

    .line 72
    :cond_0
    monitor-exit v0

    goto :goto_0

    :catchall_0
    move-exception p0

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p0

    .line 74
    :cond_1
    :goto_0
    sget-object p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mMusicPlayer:Lcom/gxxushang/tv/helper/SPPlayerManager;

    return-object p0
.end method


# virtual methods
.method public currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;
    .locals 2

    .line 106
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mediaPlayerSet:Ljava/util/HashMap;

    iget-object v1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->playerId:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    return-object v0
.end method

.method public getCurrentPosition()J
    .locals 2

    .line 199
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->getCurrentPosition()J

    move-result-wide v0

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public getDuration()J
    .locals 2

    .line 204
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->getDuration()J

    move-result-wide v0

    return-wide v0

    :cond_0
    const-wide/16 v0, 0x0

    return-wide v0
.end method

.method public isPlaying()Z
    .locals 1

    .line 194
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->isPlaying()Z

    move-result v0

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public synthetic lambda$new$0$SPPlayerManager(Landroid/os/Message;)Z
    .locals 5

    .line 82
    iget p1, p1, Landroid/os/Message;->what:I

    const/4 v0, 0x0

    const/16 v1, 0x7d2

    if-eq p1, v1, :cond_0

    goto :goto_0

    .line 84
    :cond_0
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object p1

    if-eqz p1, :cond_3

    .line 85
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->controlView:Lcom/gxxushang/tv/base/player/SPBaseControlView;

    if-eqz p1, :cond_1

    .line 86
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v2

    invoke-interface {v2}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->getCurrentPosition()J

    move-result-wide v2

    long-to-int v3, v2

    invoke-virtual {p1, v3, v0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekUpdate(IZ)V

    .line 88
    :cond_1
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->handler:Landroid/os/Handler;

    const-wide/16 v2, 0x3e8

    invoke-virtual {p1, v1, v2, v3}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    :cond_3
    :goto_0
    return v0
.end method

.method public onBufferingUpdate(Ltv/danmaku/ijk/media/player/IMediaPlayer;I)V
    .locals 0

    return-void
.end method

.method public onCompletion(Ltv/danmaku/ijk/media/player/IMediaPlayer;)V
    .locals 0

    return-void
.end method

.method public onInfo(Ltv/danmaku/ijk/media/player/IMediaPlayer;II)Z
    .locals 0

    const/16 p1, 0x2bd

    if-ne p2, p1, :cond_1

    .line 222
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->controlView:Lcom/gxxushang/tv/base/player/SPBaseControlView;

    if-eqz p1, :cond_0

    .line 223
    sget-object p2, Lcom/gxxushang/tv/general/SPConstant$PlayerState;->Loading:Lcom/gxxushang/tv/general/SPConstant$PlayerState;

    invoke-virtual {p1, p2}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->setState(Lcom/gxxushang/tv/general/SPConstant$PlayerState;)V

    .line 225
    :cond_0
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->listener:Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;

    if-eqz p1, :cond_3

    .line 226
    sget-object p2, Lcom/gxxushang/tv/general/SPConstant$PlayerState;->Loading:Lcom/gxxushang/tv/general/SPConstant$PlayerState;

    invoke-interface {p1, p2}, Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;->onStateChanged(Lcom/gxxushang/tv/general/SPConstant$PlayerState;)V

    goto :goto_0

    :cond_1
    const/16 p1, 0x2be

    if-ne p2, p1, :cond_3

    .line 229
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->controlView:Lcom/gxxushang/tv/base/player/SPBaseControlView;

    if-eqz p1, :cond_2

    .line 230
    sget-object p2, Lcom/gxxushang/tv/general/SPConstant$PlayerState;->Play:Lcom/gxxushang/tv/general/SPConstant$PlayerState;

    invoke-virtual {p1, p2}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->setState(Lcom/gxxushang/tv/general/SPConstant$PlayerState;)V

    .line 232
    :cond_2
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->listener:Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;

    if-eqz p1, :cond_3

    .line 233
    sget-object p2, Lcom/gxxushang/tv/general/SPConstant$PlayerState;->Play:Lcom/gxxushang/tv/general/SPConstant$PlayerState;

    invoke-interface {p1, p2}, Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;->onStateChanged(Lcom/gxxushang/tv/general/SPConstant$PlayerState;)V

    :cond_3
    :goto_0
    const/4 p1, 0x0

    return p1
.end method

.method public onPrepared(Ltv/danmaku/ijk/media/player/IMediaPlayer;)V
    .locals 4

    .line 176
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->autoPlay:Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    if-eqz p1, :cond_0

    .line 177
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->play()V

    .line 178
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->handler:Landroid/os/Handler;

    const/16 v0, 0x7d2

    invoke-virtual {p1, v0}, Landroid/os/Handler;->removeMessages(I)V

    .line 179
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->handler:Landroid/os/Handler;

    invoke-virtual {p1, v0}, Landroid/os/Handler;->sendEmptyMessage(I)Z

    .line 180
    iget-wide v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->schedule:J

    const-wide/16 v2, 0x0

    cmp-long p1, v0, v2

    if-lez p1, :cond_0

    .line 181
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object p1

    iget-wide v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->schedule:J

    invoke-interface {p1, v0, v1}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->seekTo(J)V

    .line 182
    iput-wide v2, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->schedule:J

    .line 185
    :cond_0
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->controlView:Lcom/gxxushang/tv/base/player/SPBaseControlView;

    if-eqz p1, :cond_1

    .line 186
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->title:Ljava/lang/String;

    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v1

    invoke-interface {v1}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->getDuration()J

    move-result-wide v1

    invoke-virtual {p1, v0, v1, v2}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->setInfo(Ljava/lang/String;J)V

    :cond_1
    return-void
.end method

.method public onVideoSizeChanged(Ltv/danmaku/ijk/media/player/IMediaPlayer;IIII)V
    .locals 0

    int-to-float p1, p3

    int-to-float p2, p2

    const/high16 p3, 0x3f800000    # 1.0f

    mul-float p2, p2, p3

    div-float/2addr p1, p2

    .line 246
    iput p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->videoRatio:F

    .line 247
    iget-object p2, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->playerView:Lcom/gxxushang/tv/view/video/SPVideoPlayerView;

    if-eqz p2, :cond_0

    .line 248
    invoke-virtual {p2, p1}, Lcom/gxxushang/tv/view/video/SPVideoPlayerView;->videoRatioChanged(F)V

    .line 250
    :cond_0
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->listener:Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;

    if-eqz p1, :cond_1

    .line 251
    iget p2, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->videoRatio:F

    invoke-interface {p1, p2}, Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;->videoRatioChanged(F)V

    :cond_1
    return-void
.end method

.method public pause()V
    .locals 2

    .line 149
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->isPlaying()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 150
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->pause()V

    .line 151
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->controlView:Lcom/gxxushang/tv/base/player/SPBaseControlView;

    if-eqz v0, :cond_0

    .line 152
    sget-object v1, Lcom/gxxushang/tv/general/SPConstant$PlayerState;->Pause:Lcom/gxxushang/tv/general/SPConstant$PlayerState;

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->setState(Lcom/gxxushang/tv/general/SPConstant$PlayerState;)V

    .line 154
    :cond_0
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->listener:Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;

    if-eqz v0, :cond_1

    .line 155
    sget-object v1, Lcom/gxxushang/tv/general/SPConstant$PlayerState;->Pause:Lcom/gxxushang/tv/general/SPConstant$PlayerState;

    invoke-interface {v0, v1}, Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;->onStateChanged(Lcom/gxxushang/tv/general/SPConstant$PlayerState;)V

    :cond_1
    return-void
.end method

.method public play()V
    .locals 2

    .line 139
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->start()V

    .line 140
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->controlView:Lcom/gxxushang/tv/base/player/SPBaseControlView;

    if-eqz v0, :cond_0

    .line 141
    sget-object v1, Lcom/gxxushang/tv/general/SPConstant$PlayerState;->Play:Lcom/gxxushang/tv/general/SPConstant$PlayerState;

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->setState(Lcom/gxxushang/tv/general/SPConstant$PlayerState;)V

    .line 143
    :cond_0
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->listener:Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;

    if-eqz v0, :cond_1

    .line 144
    sget-object v1, Lcom/gxxushang/tv/general/SPConstant$PlayerState;->Play:Lcom/gxxushang/tv/general/SPConstant$PlayerState;

    invoke-interface {v0, v1}, Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;->onStateChanged(Lcom/gxxushang/tv/general/SPConstant$PlayerState;)V

    :cond_1
    return-void
.end method

.method public playPause()V
    .locals 1

    .line 161
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 162
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    invoke-interface {v0}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->isPlaying()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 163
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->pause()V

    goto :goto_0

    .line 165
    :cond_0
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->play()V

    :cond_1
    :goto_0
    return-void
.end method

.method public release()V
    .locals 3

    .line 127
    iget-object v0, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mediaPlayerSet:Ljava/util/HashMap;

    invoke-virtual {v0}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object v0

    invoke-interface {v0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ljava/lang/String;

    .line 128
    iget-object v2, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mediaPlayerSet:Ljava/util/HashMap;

    invoke-virtual {v2, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    if-eqz v2, :cond_0

    .line 130
    invoke-interface {v2}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->stop()V

    .line 131
    invoke-interface {v2}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->releaseSurface()V

    .line 132
    invoke-interface {v2}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->release()V

    .line 134
    :cond_0
    iget-object v2, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mediaPlayerSet:Ljava/util/HashMap;

    invoke-virtual {v2, v1}, Ljava/util/HashMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_0

    :cond_1
    return-void
.end method

.method public setListener(Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;)V
    .locals 0

    .line 171
    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->listener:Lcom/gxxushang/tv/helper/SPPlayerManager$Listener;

    return-void
.end method

.method public setSpeed(F)V
    .locals 2

    .line 216
    invoke-virtual {p0}, Lcom/gxxushang/tv/helper/SPPlayerManager;->currentPlayer()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object v0

    const/4 v1, 0x1

    invoke-interface {v0, p1, v1}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->setSpeed(FZ)V

    return-void
.end method

.method public setUp(Ljava/lang/String;Ljava/lang/String;Z)V
    .locals 8

    .line 110
    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->title:Ljava/lang/String;

    .line 111
    invoke-static {p2}, Lcom/gxxushang/tv/helper/SPUtils;->getMd5String(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->playerId:Ljava/lang/String;

    .line 113
    const-class p1, Lcom/shuyu/gsyvideoplayer/player/SystemPlayerManager;

    invoke-static {p1}, Lcom/shuyu/gsyvideoplayer/player/PlayerFactory;->setPlayManager(Ljava/lang/Class;)V

    .line 114
    new-instance p1, Landroid/os/Message;

    invoke-direct {p1}, Landroid/os/Message;-><init>()V

    .line 115
    new-instance p3, Lcom/shuyu/gsyvideoplayer/model/GSYModel;

    new-instance v2, Ljava/util/HashMap;

    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    const/4 v3, 0x0

    const/high16 v4, 0x3f800000    # 1.0f

    const/4 v5, 0x0

    const/4 v6, 0x0

    const-string v7, ""

    move-object v0, p3

    move-object v1, p2

    invoke-direct/range {v0 .. v7}, Lcom/shuyu/gsyvideoplayer/model/GSYModel;-><init>(Ljava/lang/String;Ljava/util/Map;ZFZLjava/io/File;Ljava/lang/String;)V

    iput-object p3, p1, Landroid/os/Message;->obj:Ljava/lang/Object;

    .line 116
    invoke-static {}, Lcom/shuyu/gsyvideoplayer/player/PlayerFactory;->getPlayManager()Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;

    move-result-object p2

    .line 117
    iget-object p3, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mContext:Landroid/content/Context;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    invoke-static {}, Lcom/shuyu/gsyvideoplayer/cache/CacheFactory;->getCacheManager()Lcom/shuyu/gsyvideoplayer/cache/ICacheManager;

    move-result-object v1

    invoke-interface {p2, p3, p1, v0, v1}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->initVideoPlayer(Landroid/content/Context;Landroid/os/Message;Ljava/util/List;Lcom/shuyu/gsyvideoplayer/cache/ICacheManager;)V

    .line 118
    invoke-interface {p2}, Lcom/shuyu/gsyvideoplayer/player/IPlayerManager;->getMediaPlayer()Ltv/danmaku/ijk/media/player/IMediaPlayer;

    move-result-object p1

    .line 119
    invoke-interface {p1, p0}, Ltv/danmaku/ijk/media/player/IMediaPlayer;->setOnCompletionListener(Ltv/danmaku/ijk/media/player/IMediaPlayer$OnCompletionListener;)V

    .line 120
    invoke-interface {p1, p0}, Ltv/danmaku/ijk/media/player/IMediaPlayer;->setOnBufferingUpdateListener(Ltv/danmaku/ijk/media/player/IMediaPlayer$OnBufferingUpdateListener;)V

    .line 121
    invoke-interface {p1, p0}, Ltv/danmaku/ijk/media/player/IMediaPlayer;->setOnInfoListener(Ltv/danmaku/ijk/media/player/IMediaPlayer$OnInfoListener;)V

    .line 122
    invoke-interface {p1, p0}, Ltv/danmaku/ijk/media/player/IMediaPlayer;->setOnPreparedListener(Ltv/danmaku/ijk/media/player/IMediaPlayer$OnPreparedListener;)V

    .line 123
    iget-object p1, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->mediaPlayerSet:Ljava/util/HashMap;

    iget-object p3, p0, Lcom/gxxushang/tv/helper/SPPlayerManager;->playerId:Ljava/lang/String;

    invoke-virtual {p1, p3, p2}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method
