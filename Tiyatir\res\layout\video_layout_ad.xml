<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="@android:color/black" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:gravity="center" android:id="@id/surface_container" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="40.0dip" android:layout_alignParentBottom="true">
        <View android:layout_gravity="center_vertical" android:background="@null" android:paddingTop="8.0dip" android:paddingBottom="8.0dip" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxHeight="4.0dip" android:max="100" android:minHeight="4.0dip" android:layout_weight="1.0" />
        <ImageView android:id="@id/fullscreen" android:paddingRight="16.0dip" android:layout_width="wrap_content" android:layout_height="fill_parent" android:src="@drawable/video_enlarge" android:scaleType="center" />
    </LinearLayout>
    <ProgressBar android:id="@id/loading" android:visibility="invisible" android:layout_width="28.0dip" android:layout_height="28.0dip" android:layout_centerHorizontal="true" android:layout_centerVertical="true" style="?android:progressBarStyleLarge" />
    <ImageView android:layout_gravity="center_vertical" android:id="@id/start" android:layout_width="60.0dip" android:layout_height="60.0dip" android:layout_centerHorizontal="true" android:layout_centerVertical="true" />
    <TextView android:textColor="#ffffffff" android:id="@id/jump_ad" android:background="@drawable/video_jump_btn_bg" android:paddingLeft="10.0dip" android:paddingTop="4.0dip" android:paddingRight="10.0dip" android:paddingBottom="4.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="20.0dip" android:layout_marginRight="20.0dip" android:text="@string/jump_ad" android:layout_alignParentRight="true" />
    <TextView android:textColor="#ffffffff" android:id="@id/ad_time" android:background="@drawable/video_jump_btn_bg" android:paddingLeft="10.0dip" android:paddingTop="4.0dip" android:paddingRight="10.0dip" android:paddingBottom="4.0dip" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="20.0dip" android:layout_marginTop="20.0dip" android:text="00" android:layout_alignParentLeft="true" />
</RelativeLayout>
