.class public Lcom/gxxushang/tv/base/player/SPBaseControlView;
.super Lcom/gxxushang/tv/base/SPConstraintLayout;
.source "SPBaseControlView.java"

# interfaces
.implements Lcom/warkiz/widget/OnSeekChangeListener;


# instance fields
.field animator:Landroid/animation/ValueAnimator;

.field protected current:J

.field protected currentTimeView:Lcom/gxxushang/tv/base/SPTextView;

.field protected duration:J

.field fastMoveStartTime:J

.field protected gradientView:Lcom/gxxushang/tv/base/SPConstraintLayout;

.field public handler:Landroid/os/Handler;

.field protected hidePlayButton:Ljava/lang/Boolean;

.field public isLive:Z

.field protected loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

.field public manager:Lcom/gxxushang/tv/helper/SPVideoPlayerManager;

.field protected oldProgress:I

.field protected playButton:Lcom/gxxushang/tv/base/SPImageButton;

.field protected playConfig:Lcom/gxxushang/tv/model/SPPlayConfig;

.field protected seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

.field protected swiping:Z

.field protected targetTime:J

.field protected timeWrapper:Lcom/gxxushang/tv/base/SPConstraintLayout;

.field protected titleView:Lcom/gxxushang/tv/base/SPTextView;

.field protected totalTimeView:Lcom/gxxushang/tv/base/SPTextView;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 2

    .line 57
    invoke-direct {p0, p1}, Lcom/gxxushang/tv/base/SPConstraintLayout;-><init>(Landroid/content/Context;)V

    const/4 p1, 0x0

    .line 42
    iput-boolean p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    .line 43
    iput p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->oldProgress:I

    const-wide/16 v0, 0x0

    .line 44
    iput-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    .line 45
    iput-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->duration:J

    .line 46
    iput-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->current:J

    .line 47
    invoke-static {p1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object p1

    iput-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->hidePlayButton:Ljava/lang/Boolean;

    .line 53
    iput-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->fastMoveStartTime:J

    .line 58
    new-instance p1, Landroid/os/Handler;

    new-instance v0, Lcom/gxxushang/tv/base/player/-$$Lambda$SPBaseControlView$EiHXTbUdD_NU5U8lsuLRJbDs89Y;

    invoke-direct {v0, p0}, Lcom/gxxushang/tv/base/player/-$$Lambda$SPBaseControlView$EiHXTbUdD_NU5U8lsuLRJbDs89Y;-><init>(Lcom/gxxushang/tv/base/player/SPBaseControlView;)V

    invoke-direct {p1, v0}, Landroid/os/Handler;-><init>(Landroid/os/Handler$Callback;)V

    iput-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    .line 134
    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->setupControlView()V

    return-void
.end method

.method static synthetic lambda$setupControlView$3(Landroid/view/View;)V
    .locals 1

    .line 186
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object p0

    const/4 v0, 0x0

    invoke-virtual {p0, v0}, Landroid/app/Activity;->setRequestedOrientation(I)V

    return-void
.end method


# virtual methods
.method public hideControl()V
    .locals 4

    .line 313
    iget-boolean v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    if-nez v0, :cond_0

    .line 314
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    const/16 v1, 0x7d1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    .line 315
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    const-wide/16 v2, 0xbb8

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    :cond_0
    return-void
.end method

.method public synthetic lambda$new$0$SPBaseControlView(I[ILandroid/animation/ValueAnimator;)V
    .locals 5

    const/4 v0, 0x0

    const/16 v1, 0x9

    if-ne p1, v1, :cond_0

    .line 118
    iget-wide v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    invoke-virtual {p3}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    aget v3, p2, v0

    sub-int/2addr p1, v3

    int-to-long v3, p1

    sub-long/2addr v1, v3

    iput-wide v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    const-wide/16 v3, 0x0

    .line 119
    invoke-static {v3, v4, v1, v2}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v1

    iput-wide v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    goto :goto_0

    .line 121
    :cond_0
    iget-wide v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    invoke-virtual {p3}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    aget v3, p2, v0

    sub-int/2addr p1, v3

    int-to-long v3, p1

    add-long/2addr v1, v3

    iput-wide v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    .line 122
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->manager:Lcom/gxxushang/tv/helper/SPVideoPlayerManager;

    invoke-virtual {p1}, Lcom/gxxushang/tv/helper/SPVideoPlayerManager;->getDuration()J

    move-result-wide v1

    iget-wide v3, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    invoke-static {v1, v2, v3, v4}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v1

    long-to-int p1, v1

    int-to-long v1, p1

    iput-wide v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    .line 124
    :goto_0
    invoke-virtual {p3}, Landroid/animation/ValueAnimator;->getAnimatedValue()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Integer;

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    aput p1, p2, v0

    .line 125
    iget-wide p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    long-to-int p2, p1

    const/4 p1, 0x1

    invoke-virtual {p0, p2, p1}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekUpdate(IZ)V

    return-void
.end method

.method public synthetic lambda$new$1$SPBaseControlView(Landroid/os/Message;)Z
    .locals 11

    .line 59
    iget v0, p1, Landroid/os/Message;->what:I

    const/4 v1, 0x3

    const/16 v2, 0x9

    const/16 v3, 0x8

    const-wide/16 v4, 0x190

    const/4 v6, 0x1

    if-eq v0, v1, :cond_6

    const/4 v1, 0x4

    const/4 v7, 0x0

    if-eq v0, v1, :cond_1

    const/16 v1, 0x7d1

    if-eq v0, v1, :cond_0

    packed-switch v0, :pswitch_data_0

    goto/16 :goto_1

    .line 111
    :pswitch_0
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->manager:Lcom/gxxushang/tv/helper/SPVideoPlayerManager;

    invoke-virtual {v0}, Lcom/gxxushang/tv/helper/SPVideoPlayerManager;->getDuration()J

    move-result-wide v0

    long-to-float v0, v0

    const/high16 v1, 0x41c80000    # 25.0f

    div-float/2addr v0, v1

    float-to-int v0, v0

    new-array v1, v6, [I

    aput v7, v1, v7

    .line 113
    iget v2, p1, Landroid/os/Message;->what:I

    const/4 v3, 0x2

    new-array v3, v3, [I

    aput v7, v3, v7

    aput v0, v3, v6

    .line 114
    invoke-static {v3}, Landroid/animation/ValueAnimator;->ofInt([I)Landroid/animation/ValueAnimator;

    move-result-object v0

    iput-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->animator:Landroid/animation/ValueAnimator;

    .line 115
    invoke-virtual {v0, v4, v5}, Landroid/animation/ValueAnimator;->setDuration(J)Landroid/animation/ValueAnimator;

    .line 116
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->animator:Landroid/animation/ValueAnimator;

    new-instance v3, Lcom/gxxushang/tv/base/player/-$$Lambda$SPBaseControlView$6DrQTZ3uMRex8lKd9rQ2LwnfJb0;

    invoke-direct {v3, p0, v2, v1}, Lcom/gxxushang/tv/base/player/-$$Lambda$SPBaseControlView$6DrQTZ3uMRex8lKd9rQ2LwnfJb0;-><init>(Lcom/gxxushang/tv/base/player/SPBaseControlView;I[I)V

    invoke-virtual {v0, v3}, Landroid/animation/ValueAnimator;->addUpdateListener(Landroid/animation/ValueAnimator$AnimatorUpdateListener;)V

    .line 127
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->animator:Landroid/animation/ValueAnimator;

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->start()V

    .line 128
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    iget p1, p1, Landroid/os/Message;->what:I

    invoke-virtual {v0, p1, v4, v5}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    goto/16 :goto_1

    .line 61
    :cond_0
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-static {p1}, Lcom/gxxushang/tv/helper/SPAnimate;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPAnimate;

    move-result-object p1

    const-wide/16 v0, 0x64

    invoke-virtual {p1, v0, v1}, Lcom/gxxushang/tv/helper/SPAnimate;->hide(J)Lcom/gxxushang/tv/helper/SPAnimate;

    goto/16 :goto_1

    .line 84
    :cond_1
    :pswitch_1
    iget-boolean v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->isLive:Z

    if-eqz v0, :cond_2

    return v6

    .line 85
    :cond_2
    iget-boolean v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    if-eqz v0, :cond_9

    .line 86
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->animator:Landroid/animation/ValueAnimator;

    if-eqz v0, :cond_3

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 88
    :cond_3
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iget-wide v4, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->fastMoveStartTime:J

    sub-long/2addr v0, v4

    const-wide/16 v4, 0x12c

    const-wide/16 v8, 0x0

    cmp-long v10, v0, v4

    if-gez v10, :cond_5

    .line 89
    iget p1, p1, Landroid/os/Message;->what:I

    const/4 v0, 0x7

    const-wide/16 v4, 0x4e20

    if-ne p1, v0, :cond_4

    .line 90
    iget-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    sub-long/2addr v0, v4

    .line 91
    invoke-static {v8, v9, v0, v1}, Ljava/lang/Math;->max(JJ)J

    move-result-wide v0

    goto :goto_0

    .line 93
    :cond_4
    iget-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    add-long/2addr v0, v4

    .line 94
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->manager:Lcom/gxxushang/tv/helper/SPVideoPlayerManager;

    invoke-virtual {p1}, Lcom/gxxushang/tv/helper/SPVideoPlayerManager;->getDuration()J

    move-result-wide v4

    invoke-static {v4, v5, v0, v1}, Ljava/lang/Math;->min(JJ)J

    move-result-wide v0

    long-to-int p1, v0

    int-to-long v0, p1

    goto :goto_0

    .line 97
    :cond_5
    iget-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    :goto_0
    long-to-int p1, v0

    .line 99
    invoke-virtual {p0, p1, v6}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekUpdate(IZ)V

    .line 100
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->manager:Lcom/gxxushang/tv/helper/SPVideoPlayerManager;

    invoke-virtual {p1, v0, v1}, Lcom/gxxushang/tv/helper/SPVideoPlayerManager;->seekTo(J)V

    .line 101
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    invoke-virtual {p1, v3}, Landroid/os/Handler;->removeMessages(I)V

    .line 102
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    invoke-virtual {p1, v2}, Landroid/os/Handler;->removeMessages(I)V

    .line 103
    iput-wide v8, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->fastMoveStartTime:J

    .line 104
    iput-wide v8, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    .line 105
    iput-boolean v7, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    goto :goto_1

    .line 66
    :cond_6
    :pswitch_2
    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->showControl()V

    .line 67
    iget-boolean v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    if-nez v0, :cond_9

    .line 68
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->animator:Landroid/animation/ValueAnimator;

    if-eqz v0, :cond_7

    invoke-virtual {v0}, Landroid/animation/ValueAnimator;->cancel()V

    .line 69
    :cond_7
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    invoke-virtual {v0, v3}, Landroid/os/Handler;->removeMessages(I)V

    .line 70
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    invoke-virtual {v0, v2}, Landroid/os/Handler;->removeMessages(I)V

    .line 71
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->fastMoveStartTime:J

    .line 72
    iput-boolean v6, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    .line 73
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->manager:Lcom/gxxushang/tv/helper/SPVideoPlayerManager;

    invoke-virtual {v0}, Lcom/gxxushang/tv/helper/SPVideoPlayerManager;->getCurrentPosition()J

    move-result-wide v0

    long-to-int v1, v0

    int-to-long v0, v1

    iput-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->targetTime:J

    .line 74
    iget p1, p1, Landroid/os/Message;->what:I

    const/4 v0, 0x6

    if-ne p1, v0, :cond_8

    .line 75
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    invoke-virtual {p1, v2, v4, v5}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    goto :goto_1

    .line 77
    :cond_8
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    invoke-virtual {p1, v3, v4, v5}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    :cond_9
    :goto_1
    return v6

    :pswitch_data_0
    .packed-switch 0x6
        :pswitch_2
        :pswitch_1
        :pswitch_0
        :pswitch_0
    .end packed-switch
.end method

.method public synthetic lambda$setupControlView$2$SPBaseControlView(Landroid/view/View;)V
    .locals 0

    .line 172
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->manager:Lcom/gxxushang/tv/helper/SPVideoPlayerManager;

    invoke-virtual {p1}, Lcom/gxxushang/tv/helper/SPVideoPlayerManager;->playPause()V

    return-void
.end method

.method public onSeeking(Lcom/warkiz/widget/SeekParams;)V
    .locals 4

    .line 286
    iget-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->duration:J

    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    invoke-virtual {p1}, Lcom/warkiz/widget/IndicatorSeekBar;->getProgress()I

    move-result p1

    int-to-long v2, p1

    mul-long v0, v0, v2

    long-to-float p1, v0

    const/high16 v0, 0x447a0000    # 1000.0f

    div-float/2addr p1, v0

    float-to-int p1, p1

    const/4 v0, 0x1

    invoke-virtual {p0, p1, v0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekUpdate(IZ)V

    return-void
.end method

.method public onStartTrackingTouch(Lcom/warkiz/widget/IndicatorSeekBar;)V
    .locals 1

    const/4 p1, 0x1

    .line 291
    iput-boolean p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    .line 292
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    const/4 v0, 0x4

    invoke-virtual {p1, v0}, Lcom/gxxushang/tv/base/SPImageButton;->setVisibility(I)V

    return-void
.end method

.method public onStopTrackingTouch(Lcom/warkiz/widget/IndicatorSeekBar;)V
    .locals 4

    const/4 v0, 0x0

    .line 297
    iput-boolean v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    .line 298
    iget-wide v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->duration:J

    invoke-virtual {p1}, Lcom/warkiz/widget/IndicatorSeekBar;->getProgress()I

    move-result p1

    int-to-long v2, p1

    mul-long v0, v0, v2

    long-to-float p1, v0

    const/high16 v0, 0x447a0000    # 1000.0f

    div-float/2addr p1, v0

    float-to-long v0, p1

    .line 302
    :cond_0
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->manager:Lcom/gxxushang/tv/helper/SPVideoPlayerManager;

    invoke-virtual {p1, v0, v1}, Lcom/gxxushang/tv/helper/SPVideoPlayerManager;->seekTo(J)V

    return-void
.end method

.method public resetInfo()V
    .locals 2

    .line 212
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->currentTimeView:Lcom/gxxushang/tv/base/SPTextView;

    const-string v1, "00:00"

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/base/SPTextView;->setText(Ljava/lang/CharSequence;)V

    .line 213
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->totalTimeView:Lcom/gxxushang/tv/base/SPTextView;

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/base/SPTextView;->setText(Ljava/lang/CharSequence;)V

    .line 214
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/warkiz/widget/IndicatorSeekBar;->setProgress(F)V

    return-void
.end method

.method public seekUpdate(IZ)V
    .locals 3

    .line 219
    iget-boolean v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    if-eqz v0, :cond_0

    if-eqz p2, :cond_2

    .line 220
    :cond_0
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->currentTimeView:Lcom/gxxushang/tv/base/SPTextView;

    invoke-static {p1}, Lcom/gxxushang/tv/helper/SPUtils;->stringForTime(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/base/SPTextView;->setText(Ljava/lang/CharSequence;)V

    int-to-float v0, p1

    .line 221
    iget-wide v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->duration:J

    long-to-float v1, v1

    const/high16 v2, 0x3f800000    # 1.0f

    mul-float v1, v1, v2

    div-float/2addr v0, v1

    const/high16 v1, 0x447a0000    # 1000.0f

    mul-float v0, v0, v1

    float-to-double v1, v0

    .line 222
    invoke-static {v1, v2}, Ljava/lang/Double;->isNaN(D)Z

    move-result v1

    if-nez v1, :cond_1

    .line 223
    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    invoke-virtual {v1, v0}, Lcom/warkiz/widget/IndicatorSeekBar;->setProgress(F)V

    goto :goto_0

    .line 225
    :cond_1
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Lcom/warkiz/widget/IndicatorSeekBar;->setProgress(F)V

    :cond_2
    :goto_0
    if-nez p2, :cond_3

    int-to-long p1, p1

    .line 229
    iput-wide p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->current:J

    :cond_3
    return-void
.end method

.method public setInfo(Ljava/lang/String;J)V
    .locals 1

    .line 234
    iput-wide p2, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->duration:J

    .line 235
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->titleView:Lcom/gxxushang/tv/base/SPTextView;

    invoke-virtual {v0, p1}, Lcom/gxxushang/tv/base/SPTextView;->setText(Ljava/lang/CharSequence;)V

    .line 236
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->totalTimeView:Lcom/gxxushang/tv/base/SPTextView;

    long-to-int p3, p2

    invoke-static {p3}, Lcom/gxxushang/tv/helper/SPUtils;->stringForTime(I)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p1, p2}, Lcom/gxxushang/tv/base/SPTextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setPlayConfig(Lcom/gxxushang/tv/model/SPPlayConfig;)V
    .locals 0

    .line 241
    iput-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playConfig:Lcom/gxxushang/tv/model/SPPlayConfig;

    return-void
.end method

.method public setState(Lcom/gxxushang/tv/general/SPConstant$PlayerState;)V
    .locals 2

    .line 249
    iget-boolean v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->swiping:Z

    if-eqz v0, :cond_0

    return-void

    .line 250
    :cond_0
    sget-object v0, Lcom/gxxushang/tv/base/player/SPBaseControlView$1;->$SwitchMap$com$gxxushang$tv$general$SPConstant$PlayerState:[I

    invoke-virtual {p1}, Lcom/gxxushang/tv/general/SPConstant$PlayerState;->ordinal()I

    move-result p1

    aget p1, v0, p1

    const/4 v0, 0x1

    const/4 v1, 0x0

    if-eq p1, v0, :cond_5

    const/4 v0, 0x2

    if-eq p1, v0, :cond_3

    const/4 v0, 0x3

    const/4 v1, 0x4

    if-eq p1, v0, :cond_2

    if-eq p1, v1, :cond_1

    goto :goto_0

    .line 276
    :cond_1
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    invoke-virtual {p1, v1}, Lcom/gxxushang/tv/base/SPImageButton;->setVisibility(I)V

    .line 277
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-virtual {p1}, Lcom/wang/avi/AVLoadingIndicatorView;->getVisibility()I

    move-result p1

    if-nez p1, :cond_7

    .line 278
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-virtual {p1}, Lcom/wang/avi/AVLoadingIndicatorView;->smoothToHide()V

    goto :goto_0

    .line 272
    :cond_2
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    invoke-virtual {p1, v1}, Lcom/gxxushang/tv/base/SPImageButton;->setVisibility(I)V

    .line 273
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-virtual {p1}, Lcom/wang/avi/AVLoadingIndicatorView;->smoothToShow()V

    goto :goto_0

    .line 262
    :cond_3
    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->showControl()V

    .line 263
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-virtual {p1}, Lcom/wang/avi/AVLoadingIndicatorView;->getVisibility()I

    move-result p1

    if-nez p1, :cond_4

    .line 264
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-virtual {p1}, Lcom/wang/avi/AVLoadingIndicatorView;->smoothToHide()V

    .line 266
    :cond_4
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->hidePlayButton:Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    if-nez p1, :cond_7

    .line 267
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    invoke-virtual {p1, v1}, Lcom/gxxushang/tv/base/SPImageButton;->setVisibility(I)V

    .line 268
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    const v0, 0x7f0700b0

    invoke-virtual {p1, v0}, Lcom/gxxushang/tv/base/SPImageButton;->setImageResource(I)V

    goto :goto_0

    .line 252
    :cond_5
    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->hideControl()V

    .line 253
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-virtual {p1}, Lcom/wang/avi/AVLoadingIndicatorView;->getVisibility()I

    move-result p1

    if-nez p1, :cond_6

    .line 254
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-virtual {p1}, Lcom/wang/avi/AVLoadingIndicatorView;->smoothToHide()V

    .line 256
    :cond_6
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->hidePlayButton:Ljava/lang/Boolean;

    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p1

    if-nez p1, :cond_7

    .line 257
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    invoke-virtual {p1, v1}, Lcom/gxxushang/tv/base/SPImageButton;->setVisibility(I)V

    .line 258
    iget-object p1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    const v0, 0x7f0700ae

    invoke-virtual {p1, v0}, Lcom/gxxushang/tv/base/SPImageButton;->setImageResource(I)V

    :cond_7
    :goto_0
    return-void
.end method

.method public setupControlView()V
    .locals 9

    .line 138
    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->addContainer()V

    .line 139
    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0}, Lcom/warkiz/widget/IndicatorSeekBar;->with(Landroid/content/Context;)Lcom/warkiz/widget/Builder;

    move-result-object v0

    sget v1, Lcom/gxxushang/tv/helper/SPColor;->white:I

    .line 140
    invoke-virtual {v0, v1}, Lcom/warkiz/widget/Builder;->indicatorColor(I)Lcom/warkiz/widget/Builder;

    move-result-object v0

    sget v1, Lcom/gxxushang/tv/helper/SPColor;->white:I

    const v2, 0x3f19999a    # 0.6f

    .line 141
    invoke-static {v2, v1}, Lcom/gxxushang/tv/helper/SPColor;->getColorWithAlpha(FI)I

    move-result v1

    invoke-virtual {v0, v1}, Lcom/warkiz/widget/Builder;->trackBackgroundColor(I)Lcom/warkiz/widget/Builder;

    move-result-object v0

    sget v1, Lcom/gxxushang/tv/helper/SPColor;->white:I

    .line 142
    invoke-virtual {v0, v1}, Lcom/warkiz/widget/Builder;->trackProgressColor(I)Lcom/warkiz/widget/Builder;

    move-result-object v0

    const/4 v1, 0x1

    .line 143
    invoke-virtual {v0, v1}, Lcom/warkiz/widget/Builder;->trackRoundedCorners(Z)Lcom/warkiz/widget/Builder;

    move-result-object v0

    const/4 v2, 0x2

    .line 144
    invoke-virtual {v0, v2}, Lcom/warkiz/widget/Builder;->trackBackgroundSize(I)Lcom/warkiz/widget/Builder;

    move-result-object v0

    const/4 v3, 0x4

    .line 145
    invoke-virtual {v0, v3}, Lcom/warkiz/widget/Builder;->trackProgressSize(I)Lcom/warkiz/widget/Builder;

    move-result-object v0

    const/16 v4, 0xf

    .line 146
    invoke-virtual {v0, v4}, Lcom/warkiz/widget/Builder;->thumbSize(I)Lcom/warkiz/widget/Builder;

    move-result-object v0

    const/4 v4, 0x0

    .line 147
    invoke-virtual {v0, v4}, Lcom/warkiz/widget/Builder;->min(F)Lcom/warkiz/widget/Builder;

    move-result-object v0

    const/high16 v4, 0x447a0000    # 1000.0f

    .line 148
    invoke-virtual {v0, v4}, Lcom/warkiz/widget/Builder;->max(F)Lcom/warkiz/widget/Builder;

    move-result-object v0

    const/4 v4, 0x0

    .line 149
    invoke-virtual {v0, v4}, Lcom/warkiz/widget/Builder;->onlyThumbDraggable(Z)Lcom/warkiz/widget/Builder;

    move-result-object v0

    const/16 v5, 0x10

    .line 150
    invoke-virtual {v0, v5}, Lcom/warkiz/widget/Builder;->indicatorTextSize(I)Lcom/warkiz/widget/Builder;

    move-result-object v0

    .line 151
    invoke-virtual {v0, v4}, Lcom/warkiz/widget/Builder;->showIndicatorType(I)Lcom/warkiz/widget/Builder;

    move-result-object v0

    sget v5, Lcom/gxxushang/tv/helper/SPColor;->white:I

    .line 152
    invoke-virtual {v0, v5}, Lcom/warkiz/widget/Builder;->thumbColor(I)Lcom/warkiz/widget/Builder;

    move-result-object v0

    .line 153
    invoke-virtual {v0}, Lcom/warkiz/widget/Builder;->build()Lcom/warkiz/widget/IndicatorSeekBar;

    move-result-object v0

    iput-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    .line 154
    invoke-virtual {v0, p0}, Lcom/warkiz/widget/IndicatorSeekBar;->setOnSeekChangeListener(Lcom/warkiz/widget/OnSeekChangeListener;)V

    .line 155
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v5, 0x11

    if-lt v0, v5, :cond_0

    .line 156
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    invoke-virtual {v0, v4}, Lcom/warkiz/widget/IndicatorSeekBar;->setLayoutDirection(I)V

    .line 158
    :cond_0
    new-instance v0, Lcom/gxxushang/tv/base/SPTextView;

    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->getContext()Landroid/content/Context;

    move-result-object v5

    sget v6, Lcom/gxxushang/tv/helper/SPColor;->white:I

    const/high16 v7, 0x41400000    # 12.0f

    invoke-direct {v0, v5, v7, v6}, Lcom/gxxushang/tv/base/SPTextView;-><init>(Landroid/content/Context;FI)V

    iput-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->currentTimeView:Lcom/gxxushang/tv/base/SPTextView;

    .line 159
    new-instance v0, Lcom/gxxushang/tv/base/SPTextView;

    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->getContext()Landroid/content/Context;

    move-result-object v5

    sget v6, Lcom/gxxushang/tv/helper/SPColor;->white:I

    invoke-direct {v0, v5, v7, v6}, Lcom/gxxushang/tv/base/SPTextView;-><init>(Landroid/content/Context;FI)V

    iput-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->totalTimeView:Lcom/gxxushang/tv/base/SPTextView;

    .line 161
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->currentTimeView:Lcom/gxxushang/tv/base/SPTextView;

    const-string v5, "00:00"

    invoke-virtual {v0, v5}, Lcom/gxxushang/tv/base/SPTextView;->setText(Ljava/lang/CharSequence;)V

    .line 162
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->totalTimeView:Lcom/gxxushang/tv/base/SPTextView;

    invoke-virtual {v0, v5}, Lcom/gxxushang/tv/base/SPTextView;->setText(Ljava/lang/CharSequence;)V

    .line 164
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->currentTimeView:Lcom/gxxushang/tv/base/SPTextView;

    sget-object v5, Landroid/graphics/Typeface;->DEFAULT:Landroid/graphics/Typeface;

    invoke-virtual {v0, v5}, Lcom/gxxushang/tv/base/SPTextView;->setTypeface(Landroid/graphics/Typeface;)V

    .line 165
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->totalTimeView:Lcom/gxxushang/tv/base/SPTextView;

    sget-object v5, Landroid/graphics/Typeface;->DEFAULT:Landroid/graphics/Typeface;

    invoke-virtual {v0, v5}, Lcom/gxxushang/tv/base/SPTextView;->setTypeface(Landroid/graphics/Typeface;)V

    .line 167
    new-instance v0, Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->getContext()Landroid/content/Context;

    move-result-object v5

    invoke-direct {v0, v5}, Lcom/gxxushang/tv/base/SPConstraintLayout;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->timeWrapper:Lcom/gxxushang/tv/base/SPConstraintLayout;

    const/4 v5, 0x3

    new-array v6, v5, [Landroid/view/View;

    .line 168
    iget-object v7, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    aput-object v7, v6, v4

    iget-object v7, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->currentTimeView:Lcom/gxxushang/tv/base/SPTextView;

    aput-object v7, v6, v1

    iget-object v7, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->totalTimeView:Lcom/gxxushang/tv/base/SPTextView;

    aput-object v7, v6, v2

    invoke-virtual {v0, v6}, Lcom/gxxushang/tv/base/SPConstraintLayout;->addViews([Landroid/view/View;)V

    .line 170
    new-instance v0, Lcom/gxxushang/tv/base/SPImageButton;

    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->getContext()Landroid/content/Context;

    move-result-object v6

    const v7, 0x7f0700b0

    invoke-direct {v0, v6, v7}, Lcom/gxxushang/tv/base/SPImageButton;-><init>(Landroid/content/Context;I)V

    iput-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    .line 171
    invoke-virtual {v0, v3}, Lcom/gxxushang/tv/base/SPImageButton;->setVisibility(I)V

    .line 172
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    new-instance v6, Lcom/gxxushang/tv/base/player/-$$Lambda$SPBaseControlView$YBUJaymoV6PJQ5qm8PQ2YqFHaUs;

    invoke-direct {v6, p0}, Lcom/gxxushang/tv/base/player/-$$Lambda$SPBaseControlView$YBUJaymoV6PJQ5qm8PQ2YqFHaUs;-><init>(Lcom/gxxushang/tv/base/player/SPBaseControlView;)V

    invoke-virtual {v0, v6}, Lcom/gxxushang/tv/base/SPImageButton;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 175
    new-instance v0, Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->getContext()Landroid/content/Context;

    move-result-object v6

    invoke-direct {v0, v6}, Lcom/wang/avi/AVLoadingIndicatorView;-><init>(Landroid/content/Context;)V

    iput-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    const-string v6, "BallRotateIndicator"

    .line 176
    invoke-virtual {v0, v6}, Lcom/wang/avi/AVLoadingIndicatorView;->setIndicator(Ljava/lang/String;)V

    .line 177
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    sget v6, Lcom/gxxushang/tv/helper/SPColor;->white:I

    invoke-virtual {v0, v6}, Lcom/wang/avi/AVLoadingIndicatorView;->setIndicatorColor(I)V

    .line 178
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-virtual {v0, v4}, Lcom/wang/avi/AVLoadingIndicatorView;->setClickable(Z)V

    .line 180
    new-instance v0, Landroid/graphics/drawable/GradientDrawable;

    sget-object v6, Landroid/graphics/drawable/GradientDrawable$Orientation;->TOP_BOTTOM:Landroid/graphics/drawable/GradientDrawable$Orientation;

    new-array v7, v2, [I

    fill-array-data v7, :array_0

    invoke-direct {v0, v6, v7}, Landroid/graphics/drawable/GradientDrawable;-><init>(Landroid/graphics/drawable/GradientDrawable$Orientation;[I)V

    .line 181
    new-instance v6, Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->getContext()Landroid/content/Context;

    move-result-object v7

    invoke-direct {v6, v7}, Lcom/gxxushang/tv/base/SPConstraintLayout;-><init>(Landroid/content/Context;)V

    iput-object v6, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->gradientView:Lcom/gxxushang/tv/base/SPConstraintLayout;

    .line 182
    invoke-virtual {v6, v0}, Lcom/gxxushang/tv/base/SPConstraintLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 184
    new-instance v0, Lcom/gxxushang/tv/base/SPTextView;

    invoke-virtual {p0}, Lcom/gxxushang/tv/base/player/SPBaseControlView;->getContext()Landroid/content/Context;

    move-result-object v6

    const/high16 v7, 0x40e00000    # 7.0f

    sget v8, Lcom/gxxushang/tv/helper/SPColor;->white:I

    invoke-direct {v0, v6, v7, v8}, Lcom/gxxushang/tv/base/SPTextView;-><init>(Landroid/content/Context;FI)V

    iput-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->titleView:Lcom/gxxushang/tv/base/SPTextView;

    .line 185
    sget-object v6, Lcom/gxxushang/tv/base/player/-$$Lambda$SPBaseControlView$WplT0gJXImKvdpn3ls1syCjdsRs;->INSTANCE:Lcom/gxxushang/tv/base/player/-$$Lambda$SPBaseControlView$WplT0gJXImKvdpn3ls1syCjdsRs;

    invoke-virtual {v0, v6}, Lcom/gxxushang/tv/base/SPTextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 189
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    const/4 v6, 0x5

    new-array v6, v6, [Landroid/view/View;

    iget-object v7, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->gradientView:Lcom/gxxushang/tv/base/SPConstraintLayout;

    aput-object v7, v6, v4

    iget-object v4, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->timeWrapper:Lcom/gxxushang/tv/base/SPConstraintLayout;

    aput-object v4, v6, v1

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->titleView:Lcom/gxxushang/tv/base/SPTextView;

    aput-object v1, v6, v2

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    aput-object v1, v6, v5

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    aput-object v1, v6, v3

    invoke-virtual {v0, v6}, Lcom/gxxushang/tv/base/SPConstraintLayout;->addViews([Landroid/view/View;)V

    .line 197
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->gradientView:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    invoke-virtual {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->widthMatchParent()Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    const/high16 v1, 0x43480000    # 200.0f

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPDPLayout;->height(F)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPDPLayout;->bottomToBottomOf(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    .line 198
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->timeWrapper:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    invoke-virtual {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->widthMatchParent()Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    const/high16 v2, 0x42200000    # 40.0f

    invoke-virtual {v0, v1, v2}, Lcom/gxxushang/tv/helper/SPDPLayout;->bottomToBottomOf(Landroid/view/View;F)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    invoke-virtual {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->heightWrapContent()Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    const/high16 v1, 0x42480000    # 50.0f

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPDPLayout;->height(F)Lcom/gxxushang/tv/helper/SPDPLayout;

    .line 199
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    invoke-virtual {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->widthMatchConstraint()Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    invoke-virtual {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->centerY()Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->currentTimeView:Lcom/gxxushang/tv/base/SPTextView;

    const/high16 v3, 0x41700000    # 15.0f

    invoke-virtual {v0, v1, v3}, Lcom/gxxushang/tv/helper/SPDPLayout;->leftToRightOf(Landroid/view/View;F)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->totalTimeView:Lcom/gxxushang/tv/base/SPTextView;

    invoke-virtual {v0, v1, v3}, Lcom/gxxushang/tv/helper/SPDPLayout;->rightToLeftOf(Landroid/view/View;F)Lcom/gxxushang/tv/helper/SPDPLayout;

    .line 200
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->currentTimeView:Lcom/gxxushang/tv/base/SPTextView;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->timeWrapper:Lcom/gxxushang/tv/base/SPConstraintLayout;

    const/high16 v3, 0x41f00000    # 30.0f

    invoke-virtual {v0, v1, v3}, Lcom/gxxushang/tv/helper/SPDPLayout;->leftToLeftOf(Landroid/view/View;F)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPDPLayout;->centerY(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    .line 201
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->totalTimeView:Lcom/gxxushang/tv/base/SPTextView;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->timeWrapper:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-virtual {v0, v1, v3}, Lcom/gxxushang/tv/helper/SPDPLayout;->rightToRightOf(Landroid/view/View;F)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->seekBar:Lcom/warkiz/widget/IndicatorSeekBar;

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPDPLayout;->centerY(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    .line 202
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->titleView:Lcom/gxxushang/tv/base/SPTextView;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    const/high16 v3, 0x41200000    # 10.0f

    invoke-virtual {v0, v1, v3}, Lcom/gxxushang/tv/helper/SPDPLayout;->topToTopOf(Landroid/view/View;F)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPDPLayout;->centerX(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    .line 203
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    const/high16 v1, 0x42700000    # 60.0f

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPDPLayout;->size(F)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPDPLayout;->center(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    .line 204
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->loadingIndicatorView:Lcom/wang/avi/AVLoadingIndicatorView;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPDPLayout;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    invoke-virtual {v0, v2}, Lcom/gxxushang/tv/helper/SPDPLayout;->size(F)Lcom/gxxushang/tv/helper/SPDPLayout;

    move-result-object v0

    iget-object v1, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPDPLayout;->center(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPDPLayout;

    .line 206
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->hidePlayButton:Ljava/lang/Boolean;

    invoke-virtual {v0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result v0

    if-eqz v0, :cond_1

    .line 207
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->playButton:Lcom/gxxushang/tv/base/SPImageButton;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/base/SPImageButton;->setVisibility(I)V

    :cond_1
    return-void

    nop

    :array_0
    .array-data 4
        0x0
        -0x1000000
    .end array-data
.end method

.method public showControl()V
    .locals 3

    .line 306
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->handler:Landroid/os/Handler;

    const/16 v1, 0x7d1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    .line 307
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-virtual {v0}, Lcom/gxxushang/tv/base/SPConstraintLayout;->getVisibility()I

    move-result v0

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    .line 308
    iget-object v0, p0, Lcom/gxxushang/tv/base/player/SPBaseControlView;->view:Lcom/gxxushang/tv/base/SPConstraintLayout;

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPAnimate;->init(Landroid/view/View;)Lcom/gxxushang/tv/helper/SPAnimate;

    move-result-object v0

    const-wide/16 v1, 0x64

    invoke-virtual {v0, v1, v2}, Lcom/gxxushang/tv/helper/SPAnimate;->show(J)Lcom/gxxushang/tv/helper/SPAnimate;

    :cond_0
    return-void
.end method
