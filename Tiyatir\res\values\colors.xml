<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="abc_input_method_navigation_guard">@android:color/black</color>
    <color name="abc_search_url_text_normal">#ff7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="airplay_control_bg">#ff242526</color>
    <color name="airplay_slider_color">#ff242526</color>
    <color name="alipay">#ff00a3ee</color>
    <color name="background">#ffffffff</color>
    <color name="background2">#ffffffff</color>
    <color name="background2_night">#ff181818</color>
    <color name="background3">#fff5f5f5</color>
    <color name="background3_night">#ff1c1c1d</color>
    <color name="background4">#ff1d1e1f</color>
    <color name="background5">#ff272829</color>
    <color name="background6">#ff0e002c</color>
    <color name="background7">#ffededed</color>
    <color name="background7_night">#ff111111</color>
    <color name="background_floating_material_dark">@color/material_grey_800</color>
    <color name="background_floating_material_light">@android:color/white</color>
    <color name="background_material_dark">@color/material_grey_850</color>
    <color name="background_material_light">@color/material_grey_50</color>
    <color name="background_night">#ff000000</color>
    <color name="black">#ff000000</color>
    <color name="bottom_container_bg">#99000000</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color>
    <color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="btn_filled_blue_bg_disabled">#80416f96</color>
    <color name="btn_filled_blue_bg_normal">#ff416f96</color>
    <color name="btn_filled_blue_bg_pressed">#ff243f55</color>
    <color name="btn_ghost_blue_border_disabled">#801b88ee</color>
    <color name="btn_ghost_blue_border_normal">@color/qmui_config_color_blue</color>
    <color name="btn_ghost_blue_border_pressed">#801b88ee</color>
    <color name="btn_ghost_blue_text_disabled">#801b88ee</color>
    <color name="btn_ghost_blue_text_normal">@color/qmui_config_color_blue</color>
    <color name="btn_ghost_blue_text_pressed">#801b88ee</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="cardview_dark_background">#ff424242</color>
    <color name="cardview_light_background">#ffffffff</color>
    <color name="cardview_shadow_end_color">#03000000</color>
    <color name="cardview_shadow_start_color">#37000000</color>
    <color name="colorPrimary">#ff3498db</color>
    <color name="colorPrimary2">#fffce59e</color>
    <color name="colorPrimary3">#ffff6a00</color>
    <color name="colorPrimaryDark">#ff403098</color>
    <color name="colorPrimaryLight">#ffa697f7</color>
    <color name="danger">#ffdd4b39</color>
    <color name="dark">#ff020200</color>
    <color name="defaultTextColor">#ffffffff</color>
    <color name="design_bottom_navigation_shadow_color">#14000000</color>
    <color name="design_default_color_primary">#ff3f51b5</color>
    <color name="design_default_color_primary_dark">#ff303f9f</color>
    <color name="design_fab_shadow_end_color">@android:color/transparent</color>
    <color name="design_fab_shadow_mid_color">#14000000</color>
    <color name="design_fab_shadow_start_color">#44000000</color>
    <color name="design_fab_stroke_end_inner_color">#0a000000</color>
    <color name="design_fab_stroke_end_outer_color">#0f000000</color>
    <color name="design_fab_stroke_top_inner_color">#1affffff</color>
    <color name="design_fab_stroke_top_outer_color">#2effffff</color>
    <color name="design_snackbar_background_color">#ff323232</color>
    <color name="dim_foreground_disabled_material_dark">#80bebebe</color>
    <color name="dim_foreground_disabled_material_light">#80323232</color>
    <color name="dim_foreground_material_dark">#ffbebebe</color>
    <color name="dim_foreground_material_light">#ff323232</color>
    <color name="errorColor">#ffd50000</color>
    <color name="error_color_material_dark">#ffff7043</color>
    <color name="error_color_material_light">#ffff5722</color>
    <color name="exo_edit_mode_background_color">#fff4f3f0</color>
    <color name="exo_error_message_background_color">#aa000000</color>
    <color name="foreground_material_dark">@android:color/white</color>
    <color name="foreground_material_light">@android:color/black</color>
    <color name="glass2">#59000000</color>
    <color name="gray">#ffe0e0e0</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="infoColor">#ff3f51b5</color>
    <color name="line1">#fff2f2f2</color>
    <color name="line1_night">#ff212121</color>
    <color name="material_blue_grey_800">#ff37474f</color>
    <color name="material_blue_grey_900">#ff263238</color>
    <color name="material_blue_grey_950">#ff21272b</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff009688</color>
    <color name="material_grey_100">#fff5f5f5</color>
    <color name="material_grey_300">#ffe0e0e0</color>
    <color name="material_grey_50">#fffafafa</color>
    <color name="material_grey_600">#ff757575</color>
    <color name="material_grey_800">#ff424242</color>
    <color name="material_grey_850">#ff303030</color>
    <color name="material_grey_900">#ff212121</color>
    <color name="mtrl_btn_bg_color_disabled">#1f000000</color>
    <color name="mtrl_btn_text_color_disabled">#61000000</color>
    <color name="mtrl_btn_transparent_bg_color">#00ffffff</color>
    <color name="mtrl_scrim_color">#52000000</color>
    <color name="mtrl_textinput_default_box_stroke_color">#6b000000</color>
    <color name="mtrl_textinput_disabled_color">#1f000000</color>
    <color name="mtrl_textinput_filled_box_default_background_color">#0a000000</color>
    <color name="mtrl_textinput_hovered_box_stroke_color">#de000000</color>
    <color name="normalColor">#ff353a3e</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="notification_material_background_media_default_color">#ff424242</color>
    <color name="primary_dark_material_dark">@android:color/black</color>
    <color name="primary_dark_material_light">@color/material_grey_600</color>
    <color name="primary_material_dark">@color/material_grey_900</color>
    <color name="primary_material_light">@color/material_grey_100</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="qmui_common_list_item_text_color">?qmui_config_color_black</color>
    <color name="qmui_config_color_10_pure_black">#19000000</color>
    <color name="qmui_config_color_10_white">#19ffffff</color>
    <color name="qmui_config_color_15_pure_black">#26000000</color>
    <color name="qmui_config_color_15_white">#26ffffff</color>
    <color name="qmui_config_color_25_pure_black">#40000000</color>
    <color name="qmui_config_color_25_white">#40ffffff</color>
    <color name="qmui_config_color_50_blue">#801b88ee</color>
    <color name="qmui_config_color_50_pure_black">#80000000</color>
    <color name="qmui_config_color_50_white">#80ffffff</color>
    <color name="qmui_config_color_60_pure_black">#99000000</color>
    <color name="qmui_config_color_75_pure_black">#c0000000</color>
    <color name="qmui_config_color_75_white">#c0ffffff</color>
    <color name="qmui_config_color_background">#fff4f5f7</color>
    <color name="qmui_config_color_background_pressed">#ffeeeff1</color>
    <color name="qmui_config_color_black">#ff212832</color>
    <color name="qmui_config_color_blue">#ff1b88ee</color>
    <color name="qmui_config_color_gray_1">#ff353c46</color>
    <color name="qmui_config_color_gray_2">#ff49505a</color>
    <color name="qmui_config_color_gray_3">#ff5d646e</color>
    <color name="qmui_config_color_gray_4">#ff717882</color>
    <color name="qmui_config_color_gray_5">#ff858c96</color>
    <color name="qmui_config_color_gray_6">#ff99a0aa</color>
    <color name="qmui_config_color_gray_7">#ffadb4be</color>
    <color name="qmui_config_color_gray_8">#ffc4c8d0</color>
    <color name="qmui_config_color_gray_9">#ffd8dce4</color>
    <color name="qmui_config_color_link">#ff547fb0</color>
    <color name="qmui_config_color_pressed">#ffeeeef0</color>
    <color name="qmui_config_color_pure_black">#ff000000</color>
    <color name="qmui_config_color_red">#fffa3a3a</color>
    <color name="qmui_config_color_separator">#ffdee0e2</color>
    <color name="qmui_config_color_separator_darken">#ffd4d6d8</color>
    <color name="qmui_config_color_transparent">#00000000</color>
    <color name="qmui_config_color_white">#ffffffff</color>
    <color name="qmui_drawable_color_list_pressed">#ffdee0e2</color>
    <color name="qmui_drawable_color_list_separator">#ffdee0e2</color>
    <color name="qmui_group_list_section_header_text_color">?qmui_config_color_gray_3</color>
    <color name="qmui_tab_segment_bottom_line_color">?qmui_config_color_blue</color>
    <color name="qmui_tab_segment_text_color">?qmui_config_color_black</color>
    <color name="radio1">#ff45454a</color>
    <color name="radio2">#ff515156</color>
    <color name="ripple_material_dark">#33ffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="style_color">#ff005fff</color>
    <color name="successColor">#ff388e3c</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <color name="tagBackground">#fff2f2f2</color>
    <color name="tagBackground_night">#ff1c1c1d</color>
    <color name="text">#ff424242</color>
    <color name="text2">#ff9b9b9b</color>
    <color name="text2_night">#ff9b9b9b</color>
    <color name="text3">#fff2f2f2</color>
    <color name="text4">#fff5f5f5</color>
    <color name="textWhite">#fff2f2f2</color>
    <color name="text_night">#ffefefef</color>
    <color name="tipBg">#ffe3deff</color>
    <color name="tooltip_background_dark">#e6616161</color>
    <color name="tooltip_background_light">#e6ffffff</color>
    <color name="transparent">#00000000</color>
    <color name="tv_background">#ff191818</color>
    <color name="tv_background2">#ff141414</color>
    <color name="warning">#ffb75301</color>
    <color name="warningColor">#ffffa900</color>
    <color name="wechat">#ff609700</color>
    <color name="white">#ffffffff</color>
</resources>
