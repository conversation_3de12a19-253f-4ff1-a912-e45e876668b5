.class public Lcom/gxxushang/tv/model/SPEpisode;
.super Lcom/gxxushang/tv/model/SPViewModel;
.source "SPEpisode.java"


# instance fields
.field public isActive:Z

.field public require_resolution:Ljava/lang/String;

.field public title:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 7
    invoke-direct {p0}, Lcom/gxxushang/tv/model/SPViewModel;-><init>()V

    const/4 v0, 0x0

    .line 12
    iput-object v0, p0, Lcom/gxxushang/tv/model/SPEpisode;->require_resolution:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public getPlayConfig(Lcom/gxxushang/tv/helper/SPCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/gxxushang/tv/helper/SPCallback<",
            "Lcom/gxxushang/tv/model/SPPlayConfig;",
            ">;)V"
        }
    .end annotation

    const-string v0, "last_resolution"

    .line 14
    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->getLocalData(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Lcom/gxxushang/tv/model/SPEpisode;->require_resolution:Ljava/lang/String;

    .line 15
    const-class v0, Lcom/gxxushang/tv/model/SPPlayConfig;

    const-string v1, "<EMAIL>-config"

    invoke-static {v0, v1}, Lcom/gxxushang/tv/helper/SPApi;->post(Ljava/lang/Class;Ljava/lang/String;)Lcom/gxxushang/tv/helper/SPApi;

    move-result-object v0

    iget v1, p0, Lcom/gxxushang/tv/model/SPEpisode;->id:I

    .line 16
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    const-string v2, "id"

    invoke-virtual {v0, v2, v1}, Lcom/gxxushang/tv/helper/SPApi;->addParam(Ljava/lang/String;Ljava/lang/Object;)Lcom/gxxushang/tv/helper/SPApi;

    move-result-object v0

    .line 18
    iget-object v1, p0, Lcom/gxxushang/tv/model/SPEpisode;->require_resolution:Ljava/lang/String;

    if-nez v1, :cond_0

    const-string v1, "1080"

    :cond_0
    const-string v2, "require_resolution"

    invoke-virtual {v0, v2, v1}, Lcom/gxxushang/tv/helper/SPApi;->addParam(Ljava/lang/String;Ljava/lang/Object;)Lcom/gxxushang/tv/helper/SPApi;

    move-result-object v0

    .line 19
    invoke-virtual {v0, p1}, Lcom/gxxushang/tv/helper/SPApi;->onOne(Lcom/gxxushang/tv/helper/SPCallback;)Lcom/gxxushang/tv/helper/SPApi;

    return-void
.end method
