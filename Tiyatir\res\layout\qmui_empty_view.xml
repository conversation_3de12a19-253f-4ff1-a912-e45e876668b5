<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center_horizontal" android:layout_gravity="center_vertical" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <com.qmuiteam.qmui.widget.QMUILoadingView android:id="@id/empty_view_loading" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" />
        <TextView android:textSize="16.0sp" android:textColor="?qmui_config_color_gray_3" android:gravity="center_horizontal" android:id="@id/empty_view_title" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" />
        <TextView android:textSize="14.0sp" android:textColor="?qmui_config_color_gray_3" android:gravity="center_horizontal" android:id="@id/empty_view_detail" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="16.0dip" />
        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton android:id="@id/empty_view_button" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="48.0dip" android:layout_marginLeft="51.0dip" android:layout_marginTop="10.0dip" android:layout_marginRight="51.0dip" style="@style/QMUI.RoundButton" />
    </LinearLayout>
</merge>
