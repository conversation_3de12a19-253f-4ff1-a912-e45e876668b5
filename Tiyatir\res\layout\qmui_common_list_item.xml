<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <android.support.v7.widget.AppCompatImageView android:id="@id/group_list_item_imageView" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginRight="?qmui_common_list_item_icon_margin_right" android:scaleType="fitCenter" android:adjustViewBounds="true" android:layout_alignParentLeft="true" android:layout_centerVertical="true" android:contentDescription="@null" />
    <FrameLayout android:id="@id/group_list_item_accessoryView" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="?qmui_common_list_item_accessory_margin_left" android:layout_alignParentRight="true" android:layout_centerVertical="true" />
    <LinearLayout android:orientation="horizontal" android:id="@id/group_list_item_textContainer" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_toLeftOf="@id/group_list_item_accessoryView" android:layout_toRightOf="@id/group_list_item_imageView" android:layout_centerVertical="true" android:layout_alignWithParentIfMissing="true">
        <TextView android:textSize="?qmui_common_list_item_title_h_text_size" android:textColor="?qmui_config_color_gray_1" android:ellipsize="end" android:gravity="left|center" android:id="@id/group_list_item_textView" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" />
        <android.support.v4.widget.Space android:id="@id/group_list_item_space" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginRight="?qmui_common_list_item_h_space_min_width" android:layout_weight="1.0" />
        <TextView android:textSize="?qmui_common_list_item_detail_h_text_size" android:textColor="?qmui_config_color_gray_5" android:id="@id/group_list_item_detailTextView" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:includeFontPadding="false" android:lineSpacingExtra="?qmui_common_list_item_detail_line_space" />
    </LinearLayout>
    <ImageView android:id="@id/group_list_item_tips_dot" android:visibility="gone" android:contentDescription="Red dot" style="?QMUITipPointStyle" />
    <ViewStub android:id="@id/group_list_item_tips_new" android:visibility="gone" android:layout="@layout/qmui_common_list_item_tip_new_layout" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerVertical="true" />
</merge>
