.class public final Landroid/support/asynclayoutinflater/R$attr;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/asynclayoutinflater/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "attr"
.end annotation


# static fields
.field public static final alpha:I = 0x7f030035

.field public static final font:I = 0x7f0300f7

.field public static final fontProviderAuthority:I = 0x7f0300f9

.field public static final fontProviderCerts:I = 0x7f0300fa

.field public static final fontProviderFetchStrategy:I = 0x7f0300fb

.field public static final fontProviderFetchTimeout:I = 0x7f0300fc

.field public static final fontProviderPackage:I = 0x7f0300fd

.field public static final fontProviderQuery:I = 0x7f0300fe

.field public static final fontStyle:I = 0x7f0300ff

.field public static final fontVariationSettings:I = 0x7f030100

.field public static final fontWeight:I = 0x7f030101

.field public static final ttcIndex:I = 0x7f030388


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
