.class public interface abstract Landroid/support/design/chip/ChipDrawable$Delegate;
.super Ljava/lang/Object;
.source "ChipDrawable.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/design/chip/ChipDrawable;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Delegate"
.end annotation


# virtual methods
.method public abstract onChipDrawableSizeChange()V
.end method
