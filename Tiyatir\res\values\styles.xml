<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AVLoadingIndicatorView">
        <item name="indicatorName">BallPulseIndicator</item>
        <item name="maxHeight">48.0dip</item>
        <item name="maxWidth">48.0dip</item>
        <item name="minHeight">48.0dip</item>
        <item name="minWidth">48.0dip</item>
    </style>
    <style name="AVLoadingIndicatorView.Large" parent="@style/AVLoadingIndicatorView">
        <item name="indicatorName">BallPulseIndicator</item>
        <item name="maxHeight">76.0dip</item>
        <item name="maxWidth">76.0dip</item>
        <item name="minHeight">76.0dip</item>
        <item name="minWidth">76.0dip</item>
    </style>
    <style name="AVLoadingIndicatorView.Small" parent="@style/AVLoadingIndicatorView">
        <item name="indicatorName">BallPulseIndicator</item>
        <item name="maxHeight">24.0dip</item>
        <item name="maxWidth">24.0dip</item>
        <item name="minHeight">24.0dip</item>
        <item name="minWidth">24.0dip</item>
    </style>
    <style name="ActionBarBase" parent="@android:style/Widget.Holo.Light.ActionBar" />
    <style name="ActionBarTitleTextBase" parent="@android:style/TextAppearance.Holo.Widget.ActionBar.Title" />
    <style name="ActivityTranslucent" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowActionBar">false</item>
    </style>
    <style name="AlertDialog.AppCompat" parent="@style/Base.AlertDialog.AppCompat" />
    <style name="AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat.Light" />
    <style name="Animation.AppCompat.Dialog" parent="@style/Base.Animation.AppCompat.Dialog" />
    <style name="Animation.AppCompat.DropDownUp" parent="@style/Base.Animation.AppCompat.DropDownUp" />
    <style name="Animation.AppCompat.Tooltip" parent="@style/Base.Animation.AppCompat.Tooltip" />
    <style name="Animation.Design.BottomSheetDialog" parent="@style/Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/design_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/design_bottom_sheet_slide_out</item>
    </style>
    <style name="AppBaseTheme" parent="@style/AppRootTheme">
        <item name="android:buttonStyle">@style/Button</item>
        <item name="android:autoCompleteTextViewStyle">@style/AutoCompleteTextView</item>
        <item name="android:dropDownListViewStyle">@style/DropDownListView</item>
        <item name="android:editTextStyle">@style/EditText</item>
        <item name="android:gridViewStyle">@style/GridView</item>
        <item name="android:imageButtonStyle">@style/ImageButton</item>
        <item name="android:listViewStyle">@style/ListView</item>
        <item name="android:textViewStyle">@style/TextView</item>
        <item name="android:listDivider">@drawable/qmui_divider</item>
    </style>
    <style name="AppBaseTheme.Compat" parent="@style/AppRootTheme.Compat">
        <item name="android:buttonStyle">@style/Button.Compat</item>
        <item name="android:autoCompleteTextViewStyle">@style/AutoCompleteTextView.Compat</item>
        <item name="android:dropDownListViewStyle">@style/DropDownListView.Compat</item>
        <item name="android:editTextStyle">@style/EditText.Compat</item>
        <item name="android:gridViewStyle">@style/GridView.Compat</item>
        <item name="android:imageButtonStyle">@style/ImageButton.Compat</item>
        <item name="android:listViewStyle">@style/ListView.Compat</item>
        <item name="android:textViewStyle">@style/TextView.Compat</item>
        <item name="android:listDivider">@drawable/qmui_divider</item>
        <item name="autoCompleteTextViewStyle">@style/AutoCompleteTextView.Compat</item>
        <item name="buttonStyle">@style/Button.Compat</item>
        <item name="dropDownListViewStyle">@style/DropDownListView.Compat</item>
        <item name="editTextStyle">@style/EditText.Compat</item>
        <item name="imageButtonStyle">@style/ImageButton.Compat</item>
    </style>
    <style name="AppConfigTheme" parent="@style/AppBaseTheme" />
    <style name="AppConfigTheme.Compat" parent="@style/AppBaseTheme.Compat">
        <item name="android:textColorPrimary">?qmui_config_color_blue</item>
        <item name="android:textColorSecondary">?qmui_config_color_blue</item>
        <item name="android:windowBackground">@color/qmui_config_color_white</item>
        <item name="android:editTextColor">?qmui_config_color_black</item>
        <item name="colorAccent">?qmui_config_color_blue</item>
        <item name="colorControlNormal">@color/qmui_config_color_white</item>
        <item name="colorPrimary">?qmui_config_color_blue</item>
        <item name="colorPrimaryDark">?qmui_config_color_blue</item>
    </style>
    <style name="AppRootTheme" parent="@android:style/Theme.Holo.Light" />
    <style name="AppRootTheme.Compat" parent="@style/Theme.AppCompat.DayNight" />
    <style name="AutoCompleteTextView" parent="@style/AutoCompleteTextViewBase">
        <item name="android:textColor">?qmui_config_color_gray_1</item>
        <item name="android:textColorHint">?qmui_config_color_gray_4</item>
        <item name="android:textCursorDrawable">@null</item>
    </style>
    <style name="AutoCompleteTextView.Compat" parent="@style/AutoCompleteTextViewBase.Compat">
        <item name="android:textColor">?qmui_config_color_gray_1</item>
        <item name="android:textColorHint">?qmui_config_color_gray_4</item>
        <item name="android:textCursorDrawable">@null</item>
    </style>
    <style name="AutoCompleteTextViewBase" parent="@android:style/Widget.Holo.Light.AutoCompleteTextView" />
    <style name="AutoCompleteTextViewBase.Compat" parent="@style/Widget.AppCompat.AutoCompleteTextView" />
    <style name="Base.AlertDialog.AppCompat" parent="@android:style/Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat" />
    <style name="Base.Animation.AppCompat.Dialog" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.CardView" parent="@android:style/Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="cardUseCompatPadding">false</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?dialogPreferredPadding</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHighlight">?android:textColorHighlight</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorLink">?android:textColorLink</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textAllCaps">true</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium">
        <item name="android:textColor">?android:textColorSecondaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult">
        <item name="android:textSize">18.0sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:textColorTertiary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small">
        <item name="android:textColor">?android:textColorTertiaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">14.0sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textColor">?actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:textColorSecondaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title" />
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="@style/TextAppearance.AppCompat.Button" />
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">?android:textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?colorAccent</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Menu" />
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Menu" />
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@style/TextAppearance.AppCompat.Button" />
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/TextAppearance.AppCompat.Menu" />
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@android:style/TextAppearance.Medium">
        <item name="android:textColor">?android:textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle" />
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title" />
    <style name="Base.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat" />
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V7.Theme.AppCompat.Dialog" />
    <style name="Base.Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="@style/Theme.AppCompat" />
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light" />
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog" />
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Theme.AppCompat.Light" />
    <style name="Base.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents" />
    <style name="Base.Theme.MaterialComponents.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Bridge" />
    <style name="Base.Theme.MaterialComponents.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Dialog" />
    <style name="Base.Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.DialogWhenLarge" parent="@style/Theme.MaterialComponents" />
    <style name="Base.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light" />
    <style name="Base.Theme.MaterialComponents.Light.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge" />
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" />
    <style name="Base.Theme.MaterialComponents.Light.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog" />
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Theme.MaterialComponents.Light" />
    <style name="Base.ThemeOverlay.AppCompat" parent="@style/Platform.ThemeOverlay.AppCompat" />
    <style name="Base.ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="colorControlNormal">?android:textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
        <item name="colorControlNormal">?android:textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.V7.ThemeOverlay.AppCompat.Dialog" />
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog" />
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" />
    <style name="Base.V14.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView.Colored</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="snackbarButtonStyle">?borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout.Colored</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Bridge" parent="@style/Platform.MaterialComponents">
        <item name="colorSecondary">?colorPrimary</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog" parent="@style/Platform.MaterialComponents.Dialog">
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView.Colored</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="colorSecondary">?colorPrimary</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="snackbarButtonStyle">?borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout.Colored</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="snackbarButtonStyle">?borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Bridge" parent="@style/Platform.MaterialComponents.Light">
        <item name="colorSecondary">?colorPrimary</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Theme.AppCompat.Light.DarkActionBar">
        <item name="colorSecondary">?colorPrimary</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog" parent="@style/Platform.MaterialComponents.Light.Dialog">
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="colorSecondary">?colorPrimary</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="snackbarButtonStyle">?borderlessButtonStyle</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
        <item name="textInputStyle">@style/Widget.Design.TextInputLayout</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">android.support.design.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" parent="@style/ThemeOverlay.AppCompat.Dialog">
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V7.Theme.AppCompat" parent="@style/Platform.AppCompat">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?dividerVertical</item>
        <item name="actionBarItemBackground">?selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlActivated">?colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorError">@color/error_color_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">false</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64.0dip</item>
        <item name="listPreferredItemHeightLarge">80.0dip</item>
        <item name="listPreferredItemHeightSmall">48.0dip</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="viewInflaterClass">android.support.v7.app.AppCompatViewInflater</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat">
        <item name="android:colorBackground">?colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="@style/Platform.AppCompat.Light">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?dividerVertical</item>
        <item name="actionBarItemBackground">?selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlActivated">?colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorError">@color/error_color_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">true</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">64.0dip</item>
        <item name="listPreferredItemHeightLarge">80.0dip</item>
        <item name="listPreferredItemHeightSmall">48.0dip</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="viewInflaterClass">android.support.v7.app.AppCompatViewInflater</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackground">?colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.AutoCompleteTextView">
        <item name="android:textAppearance">?android:textAppearanceMediumInverse</item>
        <item name="android:textColor">?editTextColor</item>
        <item name="android:background">?editTextBackground</item>
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="@android:style/Widget.EditText">
        <item name="android:textAppearance">?android:textAppearanceMediumInverse</item>
        <item name="android:textColor">?editTextColor</item>
        <item name="android:background">?editTextBackground</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="@android:style/Widget">
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:minHeight">?actionBarSize</item>
        <item name="buttonGravity">top</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="collapseIcon">?homeAsUpIndicator</item>
        <item name="contentInsetStart">16.0dip</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="titleMargin">4.0dip</item>
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="android:gravity">center_vertical</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="background">@null</item>
        <item name="backgroundSplit">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="displayOptions">showTitle</item>
        <item name="divider">?dividerVertical</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="height">?actionBarSize</item>
        <item name="popupTheme">?actionBarPopupTheme</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="background">?colorPrimary</item>
        <item name="backgroundSplit">?colorPrimary</item>
        <item name="backgroundStacked">?colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?actionBarDivider</item>
        <item name="dividerPadding">8.0dip</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:maxWidth">180.0dip</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:gravity">center_horizontal</item>
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:paddingLeft">16.0dip</item>
        <item name="android:paddingRight">16.0dip</item>
        <item name="android:layout_width">0.0dip</item>
        <item name="android:minWidth">80.0dip</item>
        <item name="android:layout_weight">1.0</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="@style/RtlUnderlay.Widget.AppCompat.ActionButton">
        <item name="android:gravity">center</item>
        <item name="android:background">?actionBarItemBackground</item>
        <item name="android:scaleType">center</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:background">?controlBackground</item>
        <item name="android:minWidth">56.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow">
        <item name="android:background">?actionBarItemBackground</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?actionModeBackground</item>
        <item name="backgroundSplit">?actionModeSplitBackground</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
        <item name="height">?actionBarSize</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?dividerVertical</item>
        <item name="dividerPadding">6.0dip</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.V7.Widget.AppCompat.AutoCompleteTextView" />
    <style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget">
        <item name="android:textAppearance">?android:textAppearanceButton</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:minWidth">88.0dip</item>
        <item name="android:minHeight">48.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64.0dip</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
        <item name="android:background">@drawable/abc_btn_colored_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:minWidth">48.0dip</item>
        <item name="android:minHeight">48.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar" />
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.CompoundButton.CheckBox">
        <item name="android:background">?controlBackground</item>
        <item name="android:button">?android:listChoiceIndicatorMultiple</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.CompoundButton.RadioButton">
        <item name="android:background">?controlBackground</item>
        <item name="android:button">?android:listChoiceIndicatorSingle</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="@android:style/Widget.CompoundButton">
        <item name="android:background">?controlBackground</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18.0dip</item>
        <item name="drawableSize">24.0dip</item>
        <item name="gapBetweenBars">3.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="arrowHeadLength">8.0dip</item>
        <item name="arrowShaftLength">16.0dip</item>
        <item name="color">?android:textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">8.0dip</item>
        <item name="android:paddingRight">8.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="@style/Base.V7.Widget.AppCompat.EditText" />
    <style name="Base.Widget.AppCompat.ImageButton" parent="@android:style/Widget.ImageButton">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
        <item name="background">?colorPrimary</item>
        <item name="backgroundSplit">?colorPrimary</item>
        <item name="backgroundStacked">?colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar" />
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText" />
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow" />
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="@android:style/Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:dropDownHorizontalOffset">0.0dip</item>
        <item name="android:dropDownVerticalOffset">0.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.ListView">
        <item name="android:listSelector">?listChoiceBackgroundIndicator</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView">
        <item name="android:divider">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="@android:style/Widget.ListView.Menu">
        <item name="android:listSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:divider">?dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow" />
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="@android:style/Widget.PopupWindow" />
    <style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Holo.ProgressBar" />
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Holo.ProgressBar.Horizontal" />
    <style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.RatingBar">
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_material</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="@android:style/Widget.RatingBar">
        <item name="android:maxHeight">36.0dip</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36.0dip</item>
        <item name="android:thumb">@null</item>
        <item name="android:isIndicator">true</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="@android:style/Widget.RatingBar">
        <item name="android:maxHeight">16.0dip</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16.0dip</item>
        <item name="android:thumb">@null</item>
        <item name="android:isIndicator">true</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="@android:style/Widget">
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView">
        <item name="defaultQueryHint">@string/abc_search_hint</item>
        <item name="queryBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="submitBackground">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="@android:style/Widget">
        <item name="android:paddingLeft">16.0dip</item>
        <item name="android:paddingRight">16.0dip</item>
        <item name="android:focusable">true</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:progressDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:thumb">@drawable/abc_seekbar_thumb_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="@style/Platform.Widget.AppCompat.Spinner">
        <item name="android:gravity">start|center</item>
        <item name="android:background">@drawable/abc_spinner_mtrl_am_alpha</item>
        <item name="android:clickable">true</item>
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:dropDownHorizontalOffset">0.0dip</item>
        <item name="android:dropDownVerticalOffset">0.0dip</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8.0dip</item>
        <item name="android:paddingRight">8.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="@style/Base.V7.Widget.AppCompat.Toolbar" />
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget">
        <item name="android:background">?controlBackground</item>
        <item name="android:scaleType">center</item>
        <item name="android:minWidth">56.0dip</item>
    </style>
    <style name="Base.Widget.Design.TabLayout" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="tabIconTint">@null</item>
        <item name="tabIndicator">@drawable/mtrl_tabs_default_indicator</item>
        <item name="tabIndicatorAnimationDuration">@integer/design_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?colorAccent</item>
        <item name="tabIndicatorGravity">bottom</item>
        <item name="tabMaxWidth">@dimen/design_tab_max_width</item>
        <item name="tabPaddingEnd">12.0dip</item>
        <item name="tabPaddingStart">12.0dip</item>
        <item name="tabRippleColor">?colorControlHighlight</item>
        <item name="tabTextAppearance">@style/TextAppearance.Design.Tab</item>
        <item name="tabUnboundedRipple">false</item>
    </style>
    <style name="Base.Widget.MaterialComponents.Chip" parent="@android:style/Widget">
        <item name="android:textAppearance">?textAppearanceBody2</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:text">@null</item>
        <item name="android:checkable">false</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
        <item name="checkedIconVisible">true</item>
        <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
        <item name="chipCornerRadius">16.0dip</item>
        <item name="chipEndPadding">6.0dip</item>
        <item name="chipIcon">@null</item>
        <item name="chipIconSize">24.0dip</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">32.0dip</item>
        <item name="chipStartPadding">4.0dip</item>
        <item name="chipStrokeColor">#00000000</item>
        <item name="chipStrokeWidth">0.0dip</item>
        <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>
        <item name="closeIconEndPadding">2.0dip</item>
        <item name="closeIconSize">18.0dip</item>
        <item name="closeIconStartPadding">2.0dip</item>
        <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>
        <item name="closeIconVisible">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconEndPadding">0.0dip</item>
        <item name="iconStartPadding">0.0dip</item>
        <item name="rippleColor">@color/mtrl_chip_ripple_color</item>
        <item name="textEndPadding">6.0dip</item>
        <item name="textStartPadding">8.0dip</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="@style/Widget.AppCompat.EditText">
        <item name="android:paddingLeft">12.0dip</item>
        <item name="android:paddingTop">16.0dip</item>
        <item name="android:paddingRight">12.0dip</item>
        <item name="android:paddingBottom">16.0dip</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputLayout" parent="@style/Widget.Design.TextInputLayout">
        <item name="boxBackgroundColor">@null</item>
        <item name="boxBackgroundMode">outline</item>
        <item name="boxCollapsedPaddingTop">0.0dip</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxCornerRadiusBottomStart">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxCornerRadiusTopEnd">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxCornerRadiusTopStart">@dimen/mtrl_textinput_box_corner_radius_medium</item>
        <item name="boxStrokeColor">?colorControlActivated</item>
    </style>
    <style name="Button" parent="@style/ButtonBase" />
    <style name="Button.Compat" parent="@style/ButtonBase.Compat" />
    <style name="ButtonBase" parent="@android:style/Widget.Holo.Light.Button" />
    <style name="ButtonBase.Compat" parent="@style/Widget.AppCompat.Button">
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="CardView" parent="@style/Base.CardView" />
    <style name="CardView.Dark" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>
    <style name="CardView.Light" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
    <style name="DropDownListView" parent="@style/DropDownListViewBase">
        <item name="android:background">@color/qmui_config_color_white</item>
        <item name="android:divider">@drawable/qmui_divider_bottom_bitmap</item>
        <item name="android:dividerHeight">@dimen/qmui_list_divider_height</item>
    </style>
    <style name="DropDownListView.Compat" parent="@style/DropDownListViewBase.Compat">
        <item name="android:background">@color/qmui_config_color_white</item>
        <item name="android:divider">@drawable/qmui_divider_bottom_bitmap</item>
        <item name="android:dividerHeight">@dimen/qmui_list_divider_height</item>
    </style>
    <style name="DropDownListViewBase" parent="@android:style/Widget.Holo.Light.ListView.DropDown" />
    <style name="DropDownListViewBase.Compat" parent="@style/Widget.AppCompat.ListView.DropDown" />
    <style name="EditText" parent="@style/EditTextBase">
        <item name="android:textColor">?qmui_config_color_gray_1</item>
        <item name="android:textColorHint">?qmui_config_color_gray_4</item>
        <item name="android:textCursorDrawable">@null</item>
    </style>
    <style name="EditText.Compat" parent="@style/EditTextBase.Compat">
        <item name="android:textColor">?qmui_config_color_gray_1</item>
        <item name="android:textColorHint">?qmui_config_color_gray_4</item>
        <item name="android:textCursorDrawable">@null</item>
    </style>
    <style name="EditTextBase" parent="@android:style/Widget.Holo.Light.EditText" />
    <style name="EditTextBase.Compat" parent="@style/Widget.AppCompat.EditText" />
    <style name="ExoMediaButton">
        <item name="android:background">?android:selectableItemBackground</item>
        <item name="android:layout_width">@dimen/exo_media_button_width</item>
        <item name="android:layout_height">@dimen/exo_media_button_height</item>
    </style>
    <style name="ExoMediaButton.FastForward" parent="@style/ExoMediaButton">
        <item name="android:src">@drawable/exo_controls_fastforward</item>
        <item name="android:contentDescription">@string/exo_controls_fastforward_description</item>
    </style>
    <style name="ExoMediaButton.Next" parent="@style/ExoMediaButton">
        <item name="android:src">@drawable/exo_controls_next</item>
        <item name="android:contentDescription">@string/exo_controls_next_description</item>
    </style>
    <style name="ExoMediaButton.Pause" parent="@style/ExoMediaButton">
        <item name="android:src">@drawable/exo_controls_pause</item>
        <item name="android:contentDescription">@string/exo_controls_pause_description</item>
    </style>
    <style name="ExoMediaButton.Play" parent="@style/ExoMediaButton">
        <item name="android:src">@drawable/exo_controls_play</item>
        <item name="android:contentDescription">@string/exo_controls_play_description</item>
    </style>
    <style name="ExoMediaButton.Previous" parent="@style/ExoMediaButton">
        <item name="android:src">@drawable/exo_controls_previous</item>
        <item name="android:contentDescription">@string/exo_controls_previous_description</item>
    </style>
    <style name="ExoMediaButton.Rewind" parent="@style/ExoMediaButton">
        <item name="android:src">@drawable/exo_controls_rewind</item>
        <item name="android:contentDescription">@string/exo_controls_rewind_description</item>
    </style>
    <style name="ExoMediaButton.Shuffle" parent="@style/ExoMediaButton">
        <item name="android:src">@drawable/exo_controls_shuffle</item>
        <item name="android:contentDescription">@string/exo_controls_shuffle_description</item>
    </style>
    <style name="GridView" parent="@style/GridViewBase">
        <item name="android:background">@color/qmui_config_color_transparent</item>
        <item name="android:listSelector">@color/qmui_config_color_transparent</item>
    </style>
    <style name="GridView.Compat" parent="@style/GridViewBase">
        <item name="android:background">@color/qmui_config_color_transparent</item>
        <item name="android:listSelector">@color/qmui_config_color_transparent</item>
    </style>
    <style name="GridViewBase" parent="@android:style/Widget.Holo.Light.GridView" />
    <style name="GridViewBase.Compat" parent="@android:style/Widget.GridView" />
    <style name="ImageButton" parent="@style/ImageButtonBase">
        <item name="android:background">@color/qmui_config_color_transparent</item>
    </style>
    <style name="ImageButton.Compat" parent="@style/ImageButtonBase.Compat">
        <item name="android:background">@color/qmui_config_color_transparent</item>
    </style>
    <style name="ImageButtonBase" parent="@android:style/Widget.Holo.Light.ImageButton" />
    <style name="ImageButtonBase.Compat" parent="@style/Widget.AppCompat.ImageButton" />
    <style name="ListView" parent="@style/ListViewBase">
        <item name="android:background">@color/qmui_config_color_transparent</item>
        <item name="android:listSelector">@color/qmui_config_color_transparent</item>
        <item name="android:divider">@drawable/qmui_divider_bottom_bitmap</item>
        <item name="android:dividerHeight">@dimen/qmui_list_divider_height</item>
    </style>
    <style name="ListView.Compat" parent="@style/ListViewBase.Compat">
        <item name="android:background">@color/qmui_config_color_transparent</item>
        <item name="android:listSelector">@color/qmui_config_color_transparent</item>
        <item name="android:divider">@drawable/qmui_divider_bottom_bitmap</item>
        <item name="android:dividerHeight">@dimen/qmui_list_divider_height</item>
    </style>
    <style name="ListViewBase" parent="@android:style/Widget.Holo.Light.ListView" />
    <style name="ListViewBase.Compat" parent="@style/Widget.AppCompat.ListView" />
    <style name="Main" parent="@style/Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowActionBar">false</item>
    </style>
    <style name="Main.Launcher" parent="@style/Main">
        <item name="android:windowBackground">@drawable/splash_night</item>
    </style>
    <style name="Platform.AppCompat" parent="@android:style/Theme.Holo">
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorLink">?colorAccent</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_dark</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_dark</item>
        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_dark</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:borderlessButtonStyle">?borderlessButtonStyle</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLinkInverse">?colorAccent</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
    <style name="Platform.AppCompat.Light" parent="@android:style/Theme.Holo.Light">
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorLink">?colorAccent</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_light</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_light</item>
        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_light</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:borderlessButtonStyle">?borderlessButtonStyle</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLinkInverse">?colorAccent</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
    </style>
    <style name="Platform.MaterialComponents" parent="@style/Theme.AppCompat" />
    <style name="Platform.MaterialComponents.Dialog" parent="@style/Theme.AppCompat.Dialog" />
    <style name="Platform.MaterialComponents.Light" parent="@style/Theme.AppCompat.Light" />
    <style name="Platform.MaterialComponents.Light.Dialog" parent="@style/Theme.AppCompat.Light.Dialog" />
    <style name="Platform.ThemeOverlay.AppCompat" parent="" />
    <style name="Platform.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat">
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="@android:style/Widget.Holo.Spinner" />
    <style name="QMUI" parent="@style/AppConfigTheme">
        <item name="QMUIButtonStyle">@style/QMUI.RoundButton</item>
        <item name="QMUICommonListItemViewStyle">@style/QMUI.CommonListItemView</item>
        <item name="QMUIGroupListSectionViewStyle">@style/QMUI.GroupListSectionView</item>
        <item name="QMUIGroupListViewStyle">@style/QMUI.GroupListView</item>
        <item name="QMUILoadingStyle">@style/QMUI.Loading</item>
        <item name="QMUIPullRefreshLayoutStyle">@style/QMUI.PullRefreshLayout</item>
        <item name="QMUIQQFaceStyle">@style/QMUI.QQFaceView</item>
        <item name="QMUIRadiusImageViewStyle">@style/QMUI.RadiusImageView</item>
        <item name="QMUITabSegmentStyle">@style/QMUI.TabSegment</item>
        <item name="QMUITipNewStyle">@style/QMUI.TipNew</item>
        <item name="QMUITipPointStyle">@style/QMUI.TipPoint</item>
        <item name="QMUITopBarStyle">@style/QMUI.TopBar</item>
        <item name="qmui_alpha_disabled">0.5</item>
        <item name="qmui_alpha_pressed">0.5</item>
        <item name="qmui_bottom_sheet_button_background">@drawable/qmui_divider_top_bitmap</item>
        <item name="qmui_bottom_sheet_button_height">56.0dip</item>
        <item name="qmui_bottom_sheet_button_text_color">@color/qmui_config_color_gray_2</item>
        <item name="qmui_bottom_sheet_button_text_size">15.0sp</item>
        <item name="qmui_bottom_sheet_grid_bg">@color/qmui_config_color_white</item>
        <item name="qmui_bottom_sheet_grid_item_icon_marginBottom">9.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_icon_marginTop">12.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_icon_size">56.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_mini_width">84.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_paddingBottom">8.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_paddingTop">0.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_text_appearance">@style/QMUITextAppearance.GridItem.Small</item>
        <item name="qmui_bottom_sheet_grid_line_padding_horizontal">12.0dip</item>
        <item name="qmui_bottom_sheet_grid_line_vertical_space">0.0dip</item>
        <item name="qmui_bottom_sheet_grid_padding_vertical">12.0dip</item>
        <item name="qmui_bottom_sheet_list_item_bg">?qmui_s_list_item_bg_with_border_bottom</item>
        <item name="qmui_bottom_sheet_list_item_height">56.0dip</item>
        <item name="qmui_bottom_sheet_list_item_icon_margin_right">12.0dip</item>
        <item name="qmui_bottom_sheet_list_item_icon_size">22.0dip</item>
        <item name="qmui_bottom_sheet_list_item_mark_margin_left">12.0dip</item>
        <item name="qmui_bottom_sheet_list_item_padding_horizontal">?qmui_content_padding_horizontal</item>
        <item name="qmui_bottom_sheet_list_item_text_appearance">@style/QMUITextAppearance.ListItem</item>
        <item name="qmui_bottom_sheet_list_item_tip_point_margin_left">4.0dip</item>
        <item name="qmui_bottom_sheet_title_appearance">@style/QMUITextAppearance.Title.Gray</item>
        <item name="qmui_bottom_sheet_title_bg">?qmui_list_item_bg_with_border_bottom</item>
        <item name="qmui_bottom_sheet_title_height">56.0dip</item>
        <item name="qmui_common_list_item_accessory_margin_left">14.0dip</item>
        <item name="qmui_common_list_item_chevron">@drawable/qmui_icon_chevron</item>
        <item name="qmui_common_list_item_detail_h_text_size">14.0sp</item>
        <item name="qmui_common_list_item_detail_line_space">5.0dip</item>
        <item name="qmui_common_list_item_detail_v_text_size">13.0sp</item>
        <item name="qmui_common_list_item_h_space_min_width">14.0dip</item>
        <item name="qmui_common_list_item_icon_margin_right">12.0dip</item>
        <item name="qmui_common_list_item_switch">@drawable/qmui_s_icon_switch</item>
        <item name="qmui_common_list_item_title_h_text_size">16.0sp</item>
        <item name="qmui_common_list_item_title_v_text_size">15.0sp</item>
        <item name="qmui_config_color_background">@color/qmui_config_color_background</item>
        <item name="qmui_config_color_background_pressed">@color/qmui_config_color_background_pressed</item>
        <item name="qmui_config_color_black">@color/qmui_config_color_black</item>
        <item name="qmui_config_color_blue">@color/qmui_config_color_blue</item>
        <item name="qmui_config_color_gray_1">@color/qmui_config_color_gray_1</item>
        <item name="qmui_config_color_gray_2">@color/qmui_config_color_gray_2</item>
        <item name="qmui_config_color_gray_3">@color/qmui_config_color_gray_3</item>
        <item name="qmui_config_color_gray_4">@color/qmui_config_color_gray_4</item>
        <item name="qmui_config_color_gray_5">@color/qmui_config_color_gray_5</item>
        <item name="qmui_config_color_gray_6">@color/qmui_config_color_gray_6</item>
        <item name="qmui_config_color_gray_7">@color/qmui_config_color_gray_7</item>
        <item name="qmui_config_color_gray_8">@color/qmui_config_color_gray_8</item>
        <item name="qmui_config_color_gray_9">@color/qmui_config_color_gray_9</item>
        <item name="qmui_config_color_link">@color/qmui_config_color_link</item>
        <item name="qmui_config_color_pressed">@color/qmui_config_color_pressed</item>
        <item name="qmui_config_color_red">@color/qmui_config_color_red</item>
        <item name="qmui_config_color_separator">@color/qmui_config_color_separator</item>
        <item name="qmui_config_color_separator_darken">@color/qmui_config_color_separator_darken</item>
        <item name="qmui_content_padding_horizontal">@dimen/qmui_content_padding_horizontal</item>
        <item name="qmui_content_spacing_horizontal">@dimen/qmui_content_spacing_horizontal</item>
        <item name="qmui_dialog_action_container_style">@style/QMUI.Dialog.ActionContainer</item>
        <item name="qmui_dialog_action_style">@style/QMUI.Dialog.Action</item>
        <item name="qmui_dialog_background_dim_amount">0.6</item>
        <item name="qmui_dialog_bg">@drawable/qmui_dialog_bg</item>
        <item name="qmui_dialog_edit_content_style">@style/QMUI.Dialog.EditContent</item>
        <item name="qmui_dialog_margin_vertical">20.0dip</item>
        <item name="qmui_dialog_max_width">304.0dip</item>
        <item name="qmui_dialog_menu_container_style">@style/QMUI.Dialog.MenuContainer</item>
        <item name="qmui_dialog_menu_item_style">@style/QMUI.Dialog_MenuItem</item>
        <item name="qmui_dialog_message_content_style">@style/QMUI.Dialog.MessageContent</item>
        <item name="qmui_dialog_min_width">260.0dip</item>
        <item name="qmui_dialog_padding_horizontal">24.0dip</item>
        <item name="qmui_dialog_radius">@dimen/qmui_dialog_radius</item>
        <item name="qmui_dialog_title_style">@style/QMUI.Dialog.Title</item>
        <item name="qmui_dialog_wrapper_style">@style/QMUI.Dialog.Wrapper</item>
        <item name="qmui_general_shadow_alpha">0.25</item>
        <item name="qmui_general_shadow_elevation">14.0dip</item>
        <item name="qmui_icon_check_mark">@drawable/qmui_icon_checkmark</item>
        <item name="qmui_list_item_bg_with_border_bottom">@drawable/qmui_list_item_bg_with_border_bottom</item>
        <item name="qmui_list_item_bg_with_border_bottom_inset_left">@drawable/qmui_list_item_bg_with_border_bottom_inset_left</item>
        <item name="qmui_list_item_bg_with_border_bottom_inset_left_pressed">@drawable/qmui_list_item_bg_with_border_bottom_inset_left_pressed</item>
        <item name="qmui_list_item_bg_with_border_bottom_pressed">@drawable/qmui_list_item_bg_with_border_bottom_pressed</item>
        <item name="qmui_list_item_bg_with_border_double">@drawable/qmui_list_item_bg_with_double_border</item>
        <item name="qmui_list_item_bg_with_border_double_pressed">@drawable/qmui_list_item_bg_with_double_border_pressed</item>
        <item name="qmui_list_item_bg_with_border_top">@drawable/qmui_list_item_bg_with_border_top</item>
        <item name="qmui_list_item_bg_with_border_top_inset_left">@drawable/qmui_list_item_bg_with_border_top_inset_left</item>
        <item name="qmui_list_item_bg_with_border_top_inset_left_pressed">@drawable/qmui_list_item_bg_with_border_top_inset_left_pressed</item>
        <item name="qmui_list_item_bg_with_border_top_pressed">@drawable/qmui_list_item_bg_with_border_top_pressed</item>
        <item name="qmui_list_item_height">@dimen/qmui_list_item_height</item>
        <item name="qmui_list_item_height_higher">@dimen/qmui_list_item_height_higher</item>
        <item name="qmui_loading_color">@color/qmui_config_color_gray_5</item>
        <item name="qmui_loading_size">20.0dip</item>
        <item name="qmui_popup_arrow_down">@drawable/qmui_popup_arrow_down</item>
        <item name="qmui_popup_arrow_down_margin_bottom">13.0dip</item>
        <item name="qmui_popup_arrow_up">@drawable/qmui_popup_arrow_up</item>
        <item name="qmui_popup_arrow_up_margin_top">13.0dip</item>
        <item name="qmui_popup_bg">@drawable/qmui_popup_bg</item>
        <item name="qmui_round_btn_bg_color">@color/qmui_btn_blue_bg</item>
        <item name="qmui_round_btn_border_color">@color/qmui_btn_blue_border</item>
        <item name="qmui_round_btn_border_width">@dimen/qmui_btn_border_width</item>
        <item name="qmui_round_btn_text_color">@color/qmui_btn_blue_text</item>
        <item name="qmui_round_btn_text_size">@dimen/qmui_btn_text_size</item>
        <item name="qmui_s_checkbox">@drawable/qmui_s_checkbox</item>
        <item name="qmui_s_list_item_bg_with_border_bottom">@drawable/qmui_s_list_item_bg_with_border_bottom</item>
        <item name="qmui_s_list_item_bg_with_border_bottom_inset">@drawable/qmui_s_list_item_bg_with_border_bottom_inset</item>
        <item name="qmui_s_list_item_bg_with_border_bottom_inset_left">@drawable/qmui_s_list_item_bg_with_border_bottom_inset_left</item>
        <item name="qmui_s_list_item_bg_with_border_double">@drawable/qmui_s_list_item_bg_with_border_double</item>
        <item name="qmui_s_list_item_bg_with_border_none">@drawable/qmui_s_list_item_bg_with_border_none</item>
        <item name="qmui_s_list_item_bg_with_border_top">@drawable/qmui_s_list_item_bg_with_border_top</item>
        <item name="qmui_s_list_item_bg_with_border_top_inset_left">@drawable/qmui_s_list_item_bg_with_border_top_inset_left</item>
        <item name="qmui_tab_sign_count_view">@style/qmui_tab_sign_count_view</item>
        <item name="qmui_tab_sign_count_view_bg">@drawable/qmui_sign_count_view_bg</item>
        <item name="qmui_tab_sign_count_view_minSize">@dimen/qmui_tab_sign_count_view_minSize</item>
        <item name="qmui_tab_sign_count_view_minSize_with_text">@dimen/qmui_tab_sign_count_view_minSize_with_text</item>
        <item name="qmui_tab_sign_count_view_padding_horizontal">4.0dip</item>
        <item name="qmui_tip_dialog_bg">@drawable/qmui_tip_dialog_bg</item>
        <item name="qmui_tip_dialog_margin_horizontal">?qmui_content_spacing_horizontal</item>
        <item name="qmui_tip_dialog_min_height">56.0dip</item>
        <item name="qmui_tip_dialog_min_width">120.0dip</item>
        <item name="qmui_tip_dialog_padding_horizontal">?qmui_content_padding_horizontal</item>
        <item name="qmui_tip_dialog_padding_vertical">12.0dip</item>
        <item name="qmui_topbar_height">56.0dip</item>
    </style>
    <style name="QMUI.Animation" parent="@android:style/Animation" />
    <style name="QMUI.Animation.PopDownMenu" parent="@style/QMUI.Animation" />
    <style name="QMUI.Animation.PopDownMenu.Center" parent="@style/QMUI.Animation.PopDownMenu">
        <item name="android:windowEnterAnimation">@anim/grow_from_top</item>
        <item name="android:windowExitAnimation">@anim/shrink_from_bottom</item>
    </style>
    <style name="QMUI.Animation.PopDownMenu.Left" parent="@style/QMUI.Animation.PopDownMenu">
        <item name="android:windowEnterAnimation">@anim/grow_from_topleft_to_bottomright</item>
        <item name="android:windowExitAnimation">@anim/shrink_from_bottomright_to_topleft</item>
    </style>
    <style name="QMUI.Animation.PopDownMenu.Right" parent="@style/QMUI.Animation.PopDownMenu">
        <item name="android:windowEnterAnimation">@anim/grow_from_topright_to_bottomleft</item>
        <item name="android:windowExitAnimation">@anim/shrink_from_bottomleft_to_topright</item>
    </style>
    <style name="QMUI.Animation.PopUpMenu" parent="@style/QMUI.Animation" />
    <style name="QMUI.Animation.PopUpMenu.Center" parent="@style/QMUI.Animation.PopUpMenu">
        <item name="android:windowEnterAnimation">@anim/grow_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/shrink_from_top</item>
    </style>
    <style name="QMUI.Animation.PopUpMenu.Left" parent="@style/QMUI.Animation.PopUpMenu">
        <item name="android:windowEnterAnimation">@anim/grow_from_bottomleft_to_topright</item>
        <item name="android:windowExitAnimation">@anim/shrink_from_topright_to_bottomleft</item>
    </style>
    <style name="QMUI.Animation.PopUpMenu.Right" parent="@style/QMUI.Animation.PopUpMenu">
        <item name="android:windowEnterAnimation">@anim/grow_from_bottomright_to_topleft</item>
        <item name="android:windowExitAnimation">@anim/shrink_from_topleft_to_bottomright</item>
    </style>
    <style name="QMUI.BottomSheet" parent="@android:style/Theme.Dialog">
        <item name="android:backgroundDimAmount">?qmui_dialog_background_dim_amount</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:layout_width">fill_parent</item>
    </style>
    <style name="QMUI.CollapsingTopBarLayoutCollapsed" parent="@style/QMUI">
        <item name="android:textSize">17.0sp</item>
        <item name="android:textColor">@color/qmui_config_color_white</item>
    </style>
    <style name="QMUI.CollapsingTopBarLayoutExpanded" parent="@style/QMUI">
        <item name="android:textSize">24.0sp</item>
        <item name="android:textColor">@color/qmui_config_color_white</item>
    </style>
    <style name="QMUI.CommonListItemView" parent="@style/QMUI">
        <item name="android:background">?qmui_s_list_item_bg_with_border_none</item>
        <item name="android:paddingLeft">?qmui_content_padding_horizontal</item>
        <item name="android:paddingRight">?qmui_content_padding_horizontal</item>
        <item name="qmui_commonList_detailColor">?qmui_config_color_gray_5</item>
        <item name="qmui_commonList_titleColor">?qmui_config_color_gray_1</item>
    </style>
    <style name="QMUI.Compat" parent="@style/AppConfigTheme.Compat">
        <item name="QMUIButtonStyle">@style/QMUI.RoundButton</item>
        <item name="QMUICommonListItemViewStyle">@style/QMUI.CommonListItemView</item>
        <item name="QMUIGroupListSectionViewStyle">@style/QMUI.GroupListSectionView</item>
        <item name="QMUILoadingStyle">@style/QMUI.Loading</item>
        <item name="QMUIPullRefreshLayoutStyle">@style/QMUI.PullRefreshLayout</item>
        <item name="QMUIQQFaceStyle">@style/QMUI.QQFaceView</item>
        <item name="QMUIRadiusImageViewStyle">@style/QMUI.RadiusImageView</item>
        <item name="QMUITabSegmentStyle">@style/QMUI.TabSegment</item>
        <item name="QMUITipNewStyle">@style/QMUI.TipNew</item>
        <item name="QMUITipPointStyle">@style/QMUI.TipPoint</item>
        <item name="QMUITopBarStyle">@style/QMUI.TopBar</item>
        <item name="qmui_alpha_disabled">0.5</item>
        <item name="qmui_alpha_pressed">0.5</item>
        <item name="qmui_bottom_sheet_button_background">@drawable/qmui_divider_top_bitmap</item>
        <item name="qmui_bottom_sheet_button_height">56.0dip</item>
        <item name="qmui_bottom_sheet_button_text_color">@color/qmui_config_color_gray_2</item>
        <item name="qmui_bottom_sheet_button_text_size">15.0sp</item>
        <item name="qmui_bottom_sheet_grid_bg">@color/qmui_config_color_white</item>
        <item name="qmui_bottom_sheet_grid_item_icon_marginBottom">9.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_icon_marginTop">12.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_icon_size">56.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_mini_width">84.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_paddingBottom">8.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_paddingTop">0.0dip</item>
        <item name="qmui_bottom_sheet_grid_item_text_appearance">@style/QMUITextAppearance.GridItem.Small</item>
        <item name="qmui_bottom_sheet_grid_line_padding_horizontal">12.0dip</item>
        <item name="qmui_bottom_sheet_grid_line_vertical_space">0.0dip</item>
        <item name="qmui_bottom_sheet_grid_padding_vertical">12.0dip</item>
        <item name="qmui_bottom_sheet_list_item_bg">?qmui_s_list_item_bg_with_border_bottom</item>
        <item name="qmui_bottom_sheet_list_item_height">56.0dip</item>
        <item name="qmui_bottom_sheet_list_item_icon_margin_right">12.0dip</item>
        <item name="qmui_bottom_sheet_list_item_icon_size">22.0dip</item>
        <item name="qmui_bottom_sheet_list_item_mark_margin_left">12.0dip</item>
        <item name="qmui_bottom_sheet_list_item_padding_horizontal">?qmui_content_padding_horizontal</item>
        <item name="qmui_bottom_sheet_list_item_text_appearance">@style/QMUITextAppearance.ListItem</item>
        <item name="qmui_bottom_sheet_list_item_tip_point_margin_left">4.0dip</item>
        <item name="qmui_bottom_sheet_title_appearance">@style/QMUITextAppearance.Title.Gray</item>
        <item name="qmui_bottom_sheet_title_bg">?qmui_list_item_bg_with_border_bottom</item>
        <item name="qmui_bottom_sheet_title_height">56.0dip</item>
        <item name="qmui_common_list_item_accessory_margin_left">14.0dip</item>
        <item name="qmui_common_list_item_chevron">@drawable/qmui_icon_chevron</item>
        <item name="qmui_common_list_item_detail_h_text_size">14.0sp</item>
        <item name="qmui_common_list_item_detail_line_space">5.0dip</item>
        <item name="qmui_common_list_item_detail_v_text_size">13.0sp</item>
        <item name="qmui_common_list_item_h_space_min_width">14.0dip</item>
        <item name="qmui_common_list_item_icon_margin_right">12.0dip</item>
        <item name="qmui_common_list_item_switch">@drawable/qmui_s_icon_switch</item>
        <item name="qmui_common_list_item_title_h_text_size">16.0sp</item>
        <item name="qmui_common_list_item_title_v_text_size">15.0sp</item>
        <item name="qmui_config_color_background">@color/qmui_config_color_background</item>
        <item name="qmui_config_color_background_pressed">@color/qmui_config_color_background_pressed</item>
        <item name="qmui_config_color_black">@color/qmui_config_color_black</item>
        <item name="qmui_config_color_blue">@color/qmui_config_color_blue</item>
        <item name="qmui_config_color_gray_1">@color/qmui_config_color_gray_1</item>
        <item name="qmui_config_color_gray_2">@color/qmui_config_color_gray_2</item>
        <item name="qmui_config_color_gray_3">@color/qmui_config_color_gray_3</item>
        <item name="qmui_config_color_gray_4">@color/qmui_config_color_gray_4</item>
        <item name="qmui_config_color_gray_5">@color/qmui_config_color_gray_5</item>
        <item name="qmui_config_color_gray_6">@color/qmui_config_color_gray_6</item>
        <item name="qmui_config_color_gray_7">@color/qmui_config_color_gray_7</item>
        <item name="qmui_config_color_gray_8">@color/qmui_config_color_gray_8</item>
        <item name="qmui_config_color_gray_9">@color/qmui_config_color_gray_9</item>
        <item name="qmui_config_color_link">@color/qmui_config_color_link</item>
        <item name="qmui_config_color_pressed">@color/qmui_config_color_pressed</item>
        <item name="qmui_config_color_red">@color/qmui_config_color_red</item>
        <item name="qmui_config_color_separator">@color/qmui_config_color_separator</item>
        <item name="qmui_config_color_separator_darken">@color/qmui_config_color_separator_darken</item>
        <item name="qmui_content_padding_horizontal">@dimen/qmui_content_padding_horizontal</item>
        <item name="qmui_content_spacing_horizontal">@dimen/qmui_content_spacing_horizontal</item>
        <item name="qmui_dialog_action_container_style">@style/QMUI.Dialog.ActionContainer</item>
        <item name="qmui_dialog_action_style">@style/QMUI.Dialog.Action</item>
        <item name="qmui_dialog_background_dim_amount">0.6</item>
        <item name="qmui_dialog_bg">@drawable/qmui_dialog_bg</item>
        <item name="qmui_dialog_edit_content_style">@style/QMUI.Dialog.EditContent</item>
        <item name="qmui_dialog_margin_vertical">20.0dip</item>
        <item name="qmui_dialog_max_width">304.0dip</item>
        <item name="qmui_dialog_menu_container_style">@style/QMUI.Dialog.MenuContainer</item>
        <item name="qmui_dialog_menu_item_style">@style/QMUI.Dialog_MenuItem</item>
        <item name="qmui_dialog_message_content_style">@style/QMUI.Dialog.MessageContent</item>
        <item name="qmui_dialog_min_width">260.0dip</item>
        <item name="qmui_dialog_padding_horizontal">24.0dip</item>
        <item name="qmui_dialog_radius">@dimen/qmui_dialog_radius</item>
        <item name="qmui_dialog_title_style">@style/QMUI.Dialog.Title</item>
        <item name="qmui_dialog_wrapper_style">@style/QMUI.Dialog.Wrapper</item>
        <item name="qmui_general_shadow_alpha">0.25</item>
        <item name="qmui_general_shadow_elevation">14.0dip</item>
        <item name="qmui_icon_check_mark">@drawable/qmui_icon_checkmark</item>
        <item name="qmui_list_item_bg_with_border_bottom">@drawable/qmui_list_item_bg_with_border_bottom</item>
        <item name="qmui_list_item_bg_with_border_bottom_inset_left">@drawable/qmui_list_item_bg_with_border_bottom_inset_left</item>
        <item name="qmui_list_item_bg_with_border_bottom_inset_left_pressed">@drawable/qmui_list_item_bg_with_border_bottom_inset_left_pressed</item>
        <item name="qmui_list_item_bg_with_border_bottom_pressed">@drawable/qmui_list_item_bg_with_border_bottom_pressed</item>
        <item name="qmui_list_item_bg_with_border_double">@drawable/qmui_list_item_bg_with_double_border</item>
        <item name="qmui_list_item_bg_with_border_double_pressed">@drawable/qmui_list_item_bg_with_double_border_pressed</item>
        <item name="qmui_list_item_bg_with_border_top">@drawable/qmui_list_item_bg_with_border_top</item>
        <item name="qmui_list_item_bg_with_border_top_inset_left">@drawable/qmui_list_item_bg_with_border_top_inset_left</item>
        <item name="qmui_list_item_bg_with_border_top_inset_left_pressed">@drawable/qmui_list_item_bg_with_border_top_inset_left_pressed</item>
        <item name="qmui_list_item_bg_with_border_top_pressed">@drawable/qmui_list_item_bg_with_border_top_pressed</item>
        <item name="qmui_list_item_height">@dimen/qmui_list_item_height</item>
        <item name="qmui_list_item_height_higher">@dimen/qmui_list_item_height_higher</item>
        <item name="qmui_loading_color">@color/qmui_config_color_gray_5</item>
        <item name="qmui_loading_size">20.0dip</item>
        <item name="qmui_popup_arrow_down">@drawable/qmui_popup_arrow_down</item>
        <item name="qmui_popup_arrow_down_margin_bottom">13.0dip</item>
        <item name="qmui_popup_arrow_up">@drawable/qmui_popup_arrow_up</item>
        <item name="qmui_popup_arrow_up_margin_top">13.0dip</item>
        <item name="qmui_popup_bg">@drawable/qmui_popup_bg</item>
        <item name="qmui_round_btn_bg_color">@color/qmui_btn_blue_bg</item>
        <item name="qmui_round_btn_border_color">@color/qmui_btn_blue_border</item>
        <item name="qmui_round_btn_border_width">@dimen/qmui_btn_border_width</item>
        <item name="qmui_round_btn_text_color">@color/qmui_btn_blue_text</item>
        <item name="qmui_round_btn_text_size">@dimen/qmui_btn_text_size</item>
        <item name="qmui_s_checkbox">@drawable/qmui_s_checkbox</item>
        <item name="qmui_s_list_item_bg_with_border_bottom">@drawable/qmui_s_list_item_bg_with_border_bottom</item>
        <item name="qmui_s_list_item_bg_with_border_bottom_inset">@drawable/qmui_s_list_item_bg_with_border_bottom_inset</item>
        <item name="qmui_s_list_item_bg_with_border_bottom_inset_left">@drawable/qmui_s_list_item_bg_with_border_bottom_inset_left</item>
        <item name="qmui_s_list_item_bg_with_border_double">@drawable/qmui_s_list_item_bg_with_border_double</item>
        <item name="qmui_s_list_item_bg_with_border_none">@drawable/qmui_s_list_item_bg_with_border_none</item>
        <item name="qmui_s_list_item_bg_with_border_top">@drawable/qmui_s_list_item_bg_with_border_top</item>
        <item name="qmui_s_list_item_bg_with_border_top_inset_left">@drawable/qmui_s_list_item_bg_with_border_top_inset_left</item>
        <item name="qmui_tab_sign_count_view">@style/qmui_tab_sign_count_view</item>
        <item name="qmui_tab_sign_count_view_bg">@drawable/qmui_sign_count_view_bg</item>
        <item name="qmui_tab_sign_count_view_minSize">@dimen/qmui_tab_sign_count_view_minSize</item>
        <item name="qmui_tab_sign_count_view_minSize_with_text">@dimen/qmui_tab_sign_count_view_minSize_with_text</item>
        <item name="qmui_tab_sign_count_view_padding_horizontal">4.0dip</item>
        <item name="qmui_tip_dialog_bg">@drawable/qmui_tip_dialog_bg</item>
        <item name="qmui_tip_dialog_margin_horizontal">?qmui_content_spacing_horizontal</item>
        <item name="qmui_tip_dialog_min_height">56.0dip</item>
        <item name="qmui_tip_dialog_min_width">120.0dip</item>
        <item name="qmui_tip_dialog_padding_horizontal">?qmui_content_padding_horizontal</item>
        <item name="qmui_tip_dialog_padding_vertical">12.0dip</item>
        <item name="qmui_topbar_height">56.0dip</item>
    </style>
    <style name="QMUI.Compat.NoActionBar" parent="@style/QMUI.Compat">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="QMUI.Dialog" parent="@android:style/Theme.Dialog">
        <item name="android:backgroundDimAmount">?qmui_dialog_background_dim_amount</item>
        <item name="android:windowBackground">@color/qmui_config_color_transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
    </style>
    <style name="QMUI.Dialog.Action" parent="@style/QMUI.Dialog">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textColor">@color/qmui_config_color_blue</item>
        <item name="android:gravity">right|center</item>
        <item name="android:background">@null</item>
        <item name="android:minWidth">64.0dip</item>
        <item name="qmui_dialog_action_button_padding_horizontal">12.0dip</item>
        <item name="qmui_dialog_action_icon_space">6.0dip</item>
        <item name="qmui_dialog_negative_action_text_color">@color/qmui_config_color_red</item>
        <item name="qmui_dialog_positive_action_text_color">@color/qmui_config_color_blue</item>
    </style>
    <style name="QMUI.Dialog.ActionContainer" parent="@style/QMUI.Dialog">
        <item name="android:paddingLeft">12.0dip</item>
        <item name="android:paddingTop">0.0dip</item>
        <item name="android:paddingRight">12.0dip</item>
        <item name="android:paddingBottom">12.0dip</item>
        <item name="qmui_dialog_action_container_justify_content">end</item>
        <item name="qmui_dialog_action_height">36.0dip</item>
        <item name="qmui_dialog_action_space">8.0dip</item>
    </style>
    <style name="QMUI.Dialog.EditContent" parent="@style/QMUI.Dialog.MessageContent">
        <item name="android:textColor">?qmui_config_color_black</item>
        <item name="android:textColorHint">?qmui_config_color_gray_3</item>
        <item name="android:gravity">left|center</item>
        <item name="android:paddingTop">20.0dip</item>
        <item name="android:paddingBottom">31.0dip</item>
    </style>
    <style name="QMUI.Dialog.FullWidth" parent="@style/QMUI.Dialog">
        <item name="qmui_dialog_wrapper_style">@style/QMUI.Dialog.Wrapper.FullScreen</item>
    </style>
    <style name="QMUI.Dialog.FullWidth.NoAnimation" parent="@style/QMUI.Dialog.FullWidth">
        <item name="android:windowAnimationStyle">@null</item>
    </style>
    <style name="QMUI.Dialog.MenuContainer" parent="@style/QMUI.Dialog">
        <item name="android:paddingTop">8.0dip</item>
        <item name="android:paddingBottom">8.0dip</item>
        <item name="qmui_dialog_menu_container_padding_bottom_when_action_exist">27.0dip</item>
        <item name="qmui_dialog_menu_container_padding_top_when_title_exist">14.0dip</item>
        <item name="qmui_dialog_menu_container_single_padding_vertical">0.0dip</item>
        <item name="qmui_dialog_menu_item_height">48.0dip</item>
    </style>
    <style name="QMUI.Dialog.MessageContent" parent="@style/QMUI.Dialog">
        <item name="android:textSize">16.0sp</item>
        <item name="android:textColor">?qmui_config_color_gray_4</item>
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">left</item>
        <item name="android:paddingLeft">?qmui_dialog_padding_horizontal</item>
        <item name="android:paddingTop">14.0dip</item>
        <item name="android:paddingRight">?qmui_dialog_padding_horizontal</item>
        <item name="android:paddingBottom">28.0dip</item>
        <item name="android:drawablePadding">8.0dip</item>
        <item name="android:lineSpacingExtra">3.0dip</item>
        <item name="qmui_paddingTopWhenNotTitle">27.0dip</item>
    </style>
    <style name="QMUI.Dialog.Title" parent="@style/QMUI.Dialog">
        <item name="android:textSize">17.0sp</item>
        <item name="android:textColor">?qmui_config_color_black</item>
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">left</item>
        <item name="android:paddingLeft">?qmui_dialog_padding_horizontal</item>
        <item name="android:paddingTop">24.0dip</item>
        <item name="android:paddingRight">?qmui_dialog_padding_horizontal</item>
        <item name="android:paddingBottom">0.0dip</item>
        <item name="android:lineSpacingExtra">2.0dip</item>
        <item name="qmui_paddingBottomWhenNotContent">27.0dip</item>
    </style>
    <style name="QMUI.Dialog.Wrapper" parent="@style/QMUI.Dialog">
        <item name="android:paddingLeft">40.0dip</item>
        <item name="android:paddingRight">40.0dip</item>
    </style>
    <style name="QMUI.Dialog.Wrapper.FullScreen" parent="@style/QMUI.Dialog.Wrapper">
        <item name="android:paddingLeft">0.0dip</item>
        <item name="android:paddingRight">0.0dip</item>
    </style>
    <style name="QMUI.Dialog_MenuItem" parent="@style/QMUI">
        <item name="android:textSize">15.0sp</item>
        <item name="android:textColor">?qmui_config_color_black</item>
        <item name="android:gravity">left|center</item>
        <item name="android:background">?qmui_s_list_item_bg_with_border_none</item>
        <item name="android:paddingLeft">?qmui_dialog_padding_horizontal</item>
        <item name="android:paddingTop">0.0dip</item>
        <item name="android:paddingRight">?qmui_dialog_padding_horizontal</item>
        <item name="android:paddingBottom">0.0dip</item>
        <item name="qmui_dialog_menu_item_check_drawable">@drawable/qmui_s_checkbox</item>
        <item name="qmui_dialog_menu_item_check_mark_margin_hor">6.0dip</item>
        <item name="qmui_dialog_menu_item_mark_drawable">@drawable/qmui_s_dialog_check_mark</item>
    </style>
    <style name="QMUI.GroupListSectionView" parent="@style/QMUI">
        <item name="android:paddingLeft">?qmui_content_padding_horizontal</item>
        <item name="android:paddingTop">@dimen/qmui_group_list_section_header_footer_padding_vertical</item>
        <item name="android:paddingRight">?qmui_content_padding_horizontal</item>
        <item name="android:paddingBottom">@dimen/qmui_group_list_section_header_footer_padding_vertical</item>
    </style>
    <style name="QMUI.GroupListView" parent="@style/QMUI">
        <item name="separatorStyle">none</item>
    </style>
    <style name="QMUI.Loading" parent="@style/QMUI">
        <item name="android:color">?qmui_loading_color</item>
        <item name="qmui_loading_view_size">?qmui_loading_size</item>
    </style>
    <style name="QMUI.Loading.White" parent="@style/QMUI.Loading">
        <item name="android:color">@color/qmui_config_color_white</item>
        <item name="qmui_loading_view_size">?qmui_loading_size</item>
    </style>
    <style name="QMUI.NoActionBar" parent="@style/QMUI">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
    </style>
    <style name="QMUI.PullRefreshLayout" parent="@style/QMUI">
        <item name="qmui_auto_calculate_refresh_end_offset">true</item>
        <item name="qmui_auto_calculate_refresh_init_offset">true</item>
        <item name="qmui_target_init_offset">0.0dip</item>
        <item name="qmui_target_refresh_offset">72.0dip</item>
    </style>
    <style name="QMUI.QQFaceView" parent="@style/QMUI">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textColor">?qmui_config_color_black</item>
        <item name="qmui_special_drawable_padding">5.0dip</item>
    </style>
    <style name="QMUI.RadiusImageView" parent="@style/QMUI">
        <item name="qmui_border_color">?qmui_config_color_gray_4</item>
        <item name="qmui_border_width">1.0px</item>
        <item name="qmui_is_touch_select_mode_enabled">true</item>
    </style>
    <style name="QMUI.RoundButton" parent="@style/Button">
        <item name="android:textColor">?qmui_round_btn_text_color</item>
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingLeft">0.0dip</item>
        <item name="android:paddingTop">0.0dip</item>
        <item name="android:paddingRight">0.0dip</item>
        <item name="android:paddingBottom">0.0dip</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minWidth">0.0dip</item>
        <item name="android:minHeight">0.0dip</item>
        <item name="android:singleLine">true</item>
        <item name="qmui_backgroundColor">@color/qmui_s_transparent</item>
        <item name="qmui_borderColor">?qmui_round_btn_border_color</item>
        <item name="qmui_borderWidth">?qmui_round_btn_border_width</item>
        <item name="qmui_isRadiusAdjustBounds">true</item>
    </style>
    <style name="QMUI.TabSegment" parent="@style/QMUI">
        <item name="android:textSize">@dimen/qmui_tab_segment_text_size</item>
        <item name="qmui_tab_has_indicator">false</item>
        <item name="qmui_tab_icon_position">left</item>
        <item name="qmui_tab_indicator_height">@dimen/qmui_tab_segment_indicator_height</item>
        <item name="qmui_tab_indicator_top">false</item>
    </style>
    <style name="QMUI.TipDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="QMUI.TipNew" parent="@style/QMUI">
        <item name="android:background">@drawable/qmui_icon_tip_new</item>
        <item name="android:layout_width">37.0dip</item>
        <item name="android:layout_height">17.0dip</item>
    </style>
    <style name="QMUI.TipPoint" parent="@style/QMUI">
        <item name="android:background">@drawable/qmui_tips_point</item>
        <item name="android:layout_width">@dimen/qmui_tips_point_size</item>
        <item name="android:layout_height">@dimen/qmui_tips_point_size</item>
    </style>
    <style name="QMUI.TopBar" parent="@style/QMUI">
        <item name="android:paddingLeft">4.0dip</item>
        <item name="android:paddingRight">4.0dip</item>
        <item name="qmui_topbar_bg_color">@color/qmui_config_color_white</item>
        <item name="qmui_topbar_image_btn_height">48.0dip</item>
        <item name="qmui_topbar_image_btn_width">48.0dip</item>
        <item name="qmui_topbar_left_back_drawable_id">@drawable/qmui_icon_topbar_back</item>
        <item name="qmui_topbar_need_separator">true</item>
        <item name="qmui_topbar_separator_color">?qmui_config_color_separator</item>
        <item name="qmui_topbar_separator_height">1.0px</item>
        <item name="qmui_topbar_subtitle_color">@color/qmui_config_color_gray_1</item>
        <item name="qmui_topbar_subtitle_text_size">11.0sp</item>
        <item name="qmui_topbar_text_btn_color_state_list">@color/qmui_topbar_text_color</item>
        <item name="qmui_topbar_text_btn_padding_horizontal">12.0dip</item>
        <item name="qmui_topbar_text_btn_text_size">16.0sp</item>
        <item name="qmui_topbar_title_color">?qmui_config_color_gray_1</item>
        <item name="qmui_topbar_title_container_padding_horizontal">8.0dip</item>
        <item name="qmui_topbar_title_gravity">center</item>
        <item name="qmui_topbar_title_margin_horizontal_when_no_btn_aside">0.0dip</item>
        <item name="qmui_topbar_title_text_size">17.0sp</item>
        <item name="qmui_topbar_title_text_size_with_subtitle">16.0sp</item>
    </style>
    <style name="QMUITextAppearance" parent="@style/TextAppearanceBase">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textColor">?qmui_config_color_black</item>
        <item name="android:textColorHighlight">?qmui_config_color_background_pressed</item>
    </style>
    <style name="QMUITextAppearance.GridItem" parent="@style/QMUITextAppearance" />
    <style name="QMUITextAppearance.GridItem.Small" parent="@style/QMUITextAppearance.GridItem">
        <item name="android:textSize">11.0sp</item>
        <item name="android:textColor">@color/qmui_config_color_gray_3</item>
    </style>
    <style name="QMUITextAppearance.ListItem" parent="@style/QMUITextAppearance">
        <item name="android:textColor">@color/qmui_s_list_item_text_color</item>
    </style>
    <style name="QMUITextAppearance.Title" parent="@style/QMUITextAppearance" />
    <style name="QMUITextAppearance.Title.Gray" parent="@style/QMUITextAppearance.Title">
        <item name="android:textColor">?qmui_config_color_gray_3</item>
    </style>
    <style name="QMUITextAppearance.Title.Large" parent="@style/QMUITextAppearance.Title">
        <item name="android:textSize">16.0sp</item>
    </style>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="@style/Base.DialogWindowTitle.AppCompat" />
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="@android:style/Widget">
        <item name="android:layout_gravity">left|center</item>
        <item name="android:paddingRight">8.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="@android:style/Widget">
        <item name="android:layout_marginRight">8.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="@android:style/Widget">
        <item name="android:paddingRight">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="@android:style/Widget">
        <item name="android:layout_marginLeft">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="@android:style/Widget">
        <item name="android:layout_marginLeft">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="@android:style/Widget">
        <item name="android:layout_marginLeft">8.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="@android:style/Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="@android:style/Widget">
        <item name="android:layout_marginLeft">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="@android:style/Widget">
        <item name="android:paddingLeft">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingRight">4.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="@android:style/Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="@android:style/Widget">
        <item name="android:layout_toLeftOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="@android:style/Widget">
        <item name="android:layout_alignParentRight">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="@style/Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toLeftOf">@android:id/icon2</item>
        <item name="android:layout_toRightOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="@android:style/Widget">
        <item name="android:layout_marginLeft">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="@android:style/Widget">
        <item name="android:paddingLeft">12.0dip</item>
        <item name="android:paddingRight">12.0dip</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingLeft">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="@style/Base.TextAppearance.AppCompat" />
    <style name="TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat.Body1" />
    <style name="TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat.Body2" />
    <style name="TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat.Button" />
    <style name="TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat.Caption" />
    <style name="TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat.Display1" />
    <style name="TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat.Display2" />
    <style name="TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat.Display3" />
    <style name="TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat.Display4" />
    <style name="TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat.Headline" />
    <style name="TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat.Inverse" />
    <style name="TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat.Large" />
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large.Inverse" />
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="@style/TextAppearance.AppCompat.SearchResult.Subtitle" />
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="@style/TextAppearance.AppCompat.SearchResult.Title" />
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Large" />
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Small" />
    <style name="TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat.Medium" />
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium.Inverse" />
    <style name="TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat.Menu" />
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Subtitle" />
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Title" />
    <style name="TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat.Small" />
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small.Inverse" />
    <style name="TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat.Subhead" />
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead.Inverse" />
    <style name="TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat.Title" />
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title.Inverse" />
    <style name="TextAppearance.AppCompat.Tooltip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">14.0sp</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" />
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" />
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" />
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title" />
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" />
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" />
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle" />
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title" />
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Title" />
    <style name="TextAppearance.AppCompat.Widget.Button" parent="@style/Base.TextAppearance.AppCompat.Widget.Button" />
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" />
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Colored" />
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Inverse" />
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="@style/Base.TextAppearance.AppCompat.Widget.DropDownItem" />
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" />
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" />
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" />
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="@style/Base.TextAppearance.AppCompat.Widget.Switch" />
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" />
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent" />
    <style name="TextAppearance.Compat.Notification.Info" parent="@style/TextAppearance.Compat.Notification">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media" parent="@style/TextAppearance.Compat.Notification.Info" />
    <style name="TextAppearance.Compat.Notification.Line2" parent="@style/TextAppearance.Compat.Notification.Info" />
    <style name="TextAppearance.Compat.Notification.Line2.Media" parent="@style/TextAppearance.Compat.Notification.Info.Media" />
    <style name="TextAppearance.Compat.Notification.Media" parent="@style/TextAppearance.Compat.Notification" />
    <style name="TextAppearance.Compat.Notification.Time" parent="@style/TextAppearance.Compat.Notification">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time.Media" parent="@style/TextAppearance.Compat.Notification.Time" />
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title" />
    <style name="TextAppearance.Compat.Notification.Title.Media" parent="@style/TextAppearance.Compat.Notification.Title" />
    <style name="TextAppearance.Design.CollapsingToolbar.Expanded" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Counter" parent="@style/TextAppearance.AppCompat.Caption" />
    <style name="TextAppearance.Design.Counter.Overflow" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.Error" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.HelperText" parent="@style/TextAppearance.AppCompat.Caption" />
    <style name="TextAppearance.Design.Hint" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">?colorControlActivated</item>
    </style>
    <style name="TextAppearance.Design.Snackbar.Message" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/design_snackbar_text_size</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Tab" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/design_tab_text_size</item>
        <item name="android:textColor">@color/mtrl_tabs_legacy_text_color_selector</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body1" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:textSize">16.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body2" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Caption" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Chip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/mtrl_chip_text_size</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline1" parent="@style/TextAppearance.AppCompat.Display4">
        <item name="android:textSize">96.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline2" parent="@style/TextAppearance.AppCompat.Display3">
        <item name="android:textSize">60.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline3" parent="@style/TextAppearance.AppCompat.Display2">
        <item name="android:textSize">48.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline4" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">34.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline5" parent="@style/TextAppearance.AppCompat.Headline">
        <item name="android:textSize">24.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">20.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Overline" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">16.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Tab" parent="@style/TextAppearance.Design.Tab">
        <item name="android:textColor">@color/mtrl_tabs_icon_color_selector</item>
    </style>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" />
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" />
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title" />
    <style name="TextAppearanceBase" parent="@android:style/TextAppearance.Holo" />
    <style name="TextView" parent="@android:style/Widget.TextView">
        <item name="android:textAppearance">@style/QMUITextAppearance</item>
        <item name="android:textColorHighlight">?qmui_config_color_background_pressed</item>
    </style>
    <style name="TextView.Compat" parent="@android:style/Widget.TextView">
        <item name="android:textAppearance">@style/QMUITextAppearance</item>
        <item name="android:textColorHighlight">?qmui_config_color_background_pressed</item>
    </style>
    <style name="Theme.AppCompat" parent="@style/Base.Theme.AppCompat" />
    <style name="Theme.AppCompat.CompactMenu" parent="@style/Base.Theme.AppCompat.CompactMenu" />
    <style name="Theme.AppCompat.DayNight" parent="@style/Theme.AppCompat.Light" />
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="@style/Theme.AppCompat.Light.DarkActionBar" />
    <style name="Theme.AppCompat.DayNight.Dialog" parent="@style/Theme.AppCompat.Light.Dialog" />
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="@style/Theme.AppCompat.Light.Dialog.Alert" />
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="@style/Theme.AppCompat.Light.Dialog.MinWidth" />
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="@style/Theme.AppCompat.Light.DialogWhenLarge" />
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="@style/Theme.AppCompat.Light.NoActionBar" />
    <style name="Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat.Dialog" />
    <style name="Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog.Alert" />
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog.MinWidth" />
    <style name="Theme.AppCompat.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.DialogWhenLarge" />
    <style name="Theme.AppCompat.Light" parent="@style/Base.Theme.AppCompat.Light" />
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light.DarkActionBar" />
    <style name="Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light.Dialog" />
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog.Alert" />
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog.MinWidth" />
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.Light.DialogWhenLarge" />
    <style name="Theme.AppCompat.Light.NoActionBar" parent="@style/Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar" parent="@style/Theme.AppCompat">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design" parent="@style/Theme.AppCompat" />
    <style name="Theme.Design.BottomSheetDialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light" parent="@style/Theme.AppCompat.Light" />
    <style name="Theme.Design.Light.BottomSheetDialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light.NoActionBar" parent="@style/Theme.Design.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design.NoActionBar" parent="@style/Theme.Design">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents" parent="@style/Base.Theme.MaterialComponents" />
    <style name="Theme.MaterialComponents.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Bridge" parent="@style/Base.Theme.MaterialComponents.Bridge" />
    <style name="Theme.MaterialComponents.CompactMenu" parent="@style/Base.Theme.MaterialComponents.CompactMenu" />
    <style name="Theme.MaterialComponents.Dialog" parent="@style/Base.Theme.MaterialComponents.Dialog" />
    <style name="Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog.Alert" />
    <style name="Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog.MinWidth" />
    <style name="Theme.MaterialComponents.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.DialogWhenLarge" />
    <style name="Theme.MaterialComponents.Light" parent="@style/Base.Theme.MaterialComponents.Light" />
    <style name="Theme.MaterialComponents.Light.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Light.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Bridge" />
    <style name="Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar" />
    <style name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" />
    <style name="Theme.MaterialComponents.Light.Dialog" parent="@style/Base.Theme.MaterialComponents.Light.Dialog" />
    <style name="Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Alert" />
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth" />
    <style name="Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.Light.DialogWhenLarge" />
    <style name="Theme.MaterialComponents.Light.NoActionBar" parent="@style/Theme.MaterialComponents.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Light.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar" parent="@style/Theme.MaterialComponents">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="ThemeOverlay.AppCompat" parent="@style/Base.ThemeOverlay.AppCompat" />
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.ActionBar" />
    <style name="ThemeOverlay.AppCompat.Dark" parent="@style/Base.ThemeOverlay.AppCompat.Dark" />
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark.ActionBar" />
    <style name="ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat.Dialog" />
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog.Alert" />
    <style name="ThemeOverlay.AppCompat.Light" parent="@style/Base.ThemeOverlay.AppCompat.Light" />
    <style name="ThemeOverlay.MaterialComponents" parent="@style/ThemeOverlay.AppCompat" />
    <style name="ThemeOverlay.MaterialComponents.ActionBar" parent="@style/ThemeOverlay.AppCompat.ActionBar" />
    <style name="ThemeOverlay.MaterialComponents.Dark" parent="@style/ThemeOverlay.AppCompat.Dark" />
    <style name="ThemeOverlay.MaterialComponents.Dark.ActionBar" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />
    <style name="ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog" />
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert" />
    <style name="ThemeOverlay.MaterialComponents.Light" parent="@style/ThemeOverlay.AppCompat.Light" />
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText" parent="" />
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.AppCompat.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar" />
    <style name="Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar.Solid" />
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar" />
    <style name="Widget.AppCompat.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText" />
    <style name="Widget.AppCompat.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView" />
    <style name="Widget.AppCompat.ActionButton" parent="@style/Base.Widget.AppCompat.ActionButton" />
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton.CloseMode" />
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton.Overflow" />
    <style name="Widget.AppCompat.ActionMode" parent="@style/Base.Widget.AppCompat.ActionMode" />
    <style name="Widget.AppCompat.ActivityChooserView" parent="@style/Base.Widget.AppCompat.ActivityChooserView" />
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.Widget.AppCompat.AutoCompleteTextView" />
    <style name="Widget.AppCompat.Button" parent="@style/Base.Widget.AppCompat.Button" />
    <style name="Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button.Borderless" />
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless.Colored" />
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" />
    <style name="Widget.AppCompat.Button.Colored" parent="@style/Base.Widget.AppCompat.Button.Colored" />
    <style name="Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button.Small" />
    <style name="Widget.AppCompat.ButtonBar" parent="@style/Base.Widget.AppCompat.ButtonBar" />
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar.AlertDialog" />
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="@style/Base.Widget.AppCompat.CompoundButton.CheckBox" />
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="@style/Base.Widget.AppCompat.CompoundButton.RadioButton" />
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="@style/Base.Widget.AppCompat.CompoundButton.Switch" />
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text" />
    <style name="Widget.AppCompat.EditText" parent="@style/Base.Widget.AppCompat.EditText" />
    <style name="Widget.AppCompat.ImageButton" parent="@style/Base.Widget.AppCompat.ImageButton" />
    <style name="Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar" />
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar.Solid" />
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.Solid" />
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabBar" />
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabBar" />
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText" />
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" />
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabView" />
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabView" />
    <style name="Widget.AppCompat.Light.ActionButton" parent="@style/Widget.AppCompat.ActionButton" />
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="@style/Widget.AppCompat.ActionButton.CloseMode" />
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="@style/Widget.AppCompat.ActionButton.Overflow" />
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="@style/Widget.AppCompat.ActionMode" />
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="@style/Widget.AppCompat.ActivityChooserView" />
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="@style/Widget.AppCompat.AutoCompleteTextView" />
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="@style/Widget.AppCompat.DropDownItem.Spinner" />
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="@style/Widget.AppCompat.ListPopupWindow" />
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="@style/Widget.AppCompat.ListView.DropDown" />
    <style name="Widget.AppCompat.Light.PopupMenu" parent="@style/Base.Widget.AppCompat.Light.PopupMenu" />
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu.Overflow" />
    <style name="Widget.AppCompat.Light.SearchView" parent="@style/Widget.AppCompat.SearchView" />
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown.ActionBar" />
    <style name="Widget.AppCompat.ListMenuView" parent="@style/Base.Widget.AppCompat.ListMenuView" />
    <style name="Widget.AppCompat.ListPopupWindow" parent="@style/Base.Widget.AppCompat.ListPopupWindow" />
    <style name="Widget.AppCompat.ListView" parent="@style/Base.Widget.AppCompat.ListView" />
    <style name="Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView.DropDown" />
    <style name="Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView.Menu" />
    <style name="Widget.AppCompat.PopupMenu" parent="@style/Base.Widget.AppCompat.PopupMenu" />
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu.Overflow" />
    <style name="Widget.AppCompat.PopupWindow" parent="@style/Base.Widget.AppCompat.PopupWindow" />
    <style name="Widget.AppCompat.ProgressBar" parent="@style/Base.Widget.AppCompat.ProgressBar" />
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="@style/Base.Widget.AppCompat.ProgressBar.Horizontal" />
    <style name="Widget.AppCompat.RatingBar" parent="@style/Base.Widget.AppCompat.RatingBar" />
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="@style/Base.Widget.AppCompat.RatingBar.Indicator" />
    <style name="Widget.AppCompat.RatingBar.Small" parent="@style/Base.Widget.AppCompat.RatingBar.Small" />
    <style name="Widget.AppCompat.SearchView" parent="@style/Base.Widget.AppCompat.SearchView" />
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView.ActionBar" />
    <style name="Widget.AppCompat.SeekBar" parent="@style/Base.Widget.AppCompat.SeekBar" />
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar.Discrete" />
    <style name="Widget.AppCompat.Spinner" parent="@style/Base.Widget.AppCompat.Spinner" />
    <style name="Widget.AppCompat.Spinner.DropDown" parent="@style/Widget.AppCompat.Spinner" />
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown" />
    <style name="Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner.Underlined" />
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="@style/Base.Widget.AppCompat.TextView.SpinnerItem" />
    <style name="Widget.AppCompat.Toolbar" parent="@style/Base.Widget.AppCompat.Toolbar" />
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/Base.Widget.AppCompat.Toolbar.Button.Navigation" />
    <style name="Widget.Compat.NotificationActionContainer" parent="" />
    <style name="Widget.Compat.NotificationActionText" parent="" />
    <style name="Widget.Design.AppBarLayout" parent="@android:style/Widget">
        <item name="android:background">?colorPrimary</item>
    </style>
    <style name="Widget.Design.BottomNavigationView" parent="">
        <item name="elevation">@dimen/design_bottom_navigation_elevation</item>
        <item name="itemBackground">?selectableItemBackgroundBorderless</item>
        <item name="itemHorizontalTranslationEnabled">true</item>
        <item name="itemIconSize">@dimen/design_bottom_navigation_icon_size</item>
        <item name="labelVisibilityMode">auto</item>
    </style>
    <style name="Widget.Design.BottomSheet.Modal" parent="@android:style/Widget">
        <item name="android:background">?android:colorBackground</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_peekHeight">auto</item>
        <item name="behavior_skipCollapsed">false</item>
    </style>
    <style name="Widget.Design.CollapsingToolbar" parent="@android:style/Widget">
        <item name="expandedTitleMargin">32.0dip</item>
        <item name="statusBarScrim">?colorPrimaryDark</item>
    </style>
    <style name="Widget.Design.FloatingActionButton" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_fab_background</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="backgroundTint">?colorAccent</item>
        <item name="borderWidth">@dimen/design_fab_border_width</item>
        <item name="elevation">@dimen/design_fab_elevation</item>
        <item name="fabSize">auto</item>
        <item name="hideMotionSpec">@animator/design_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/design_fab_translation_z_hovered_focused</item>
        <item name="maxImageSize">@dimen/design_fab_image_size</item>
        <item name="pressedTranslationZ">@dimen/design_fab_translation_z_pressed</item>
        <item name="rippleColor">?colorControlHighlight</item>
        <item name="showMotionSpec">@animator/design_fab_show_motion_spec</item>
    </style>
    <style name="Widget.Design.NavigationView" parent="">
        <item name="android:background">?android:windowBackground</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:maxWidth">@dimen/design_navigation_max_width</item>
        <item name="elevation">@dimen/design_navigation_elevation</item>
        <item name="itemHorizontalPadding">@dimen/design_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/design_navigation_item_icon_padding</item>
    </style>
    <style name="Widget.Design.ScrimInsetsFrameLayout" parent="">
        <item name="insetForeground">#44000000</item>
    </style>
    <style name="Widget.Design.Snackbar" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_snackbar_background</item>
        <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:maxWidth">@dimen/design_snackbar_max_width</item>
        <item name="android:minWidth">@dimen/design_snackbar_min_width</item>
        <item name="elevation">@dimen/design_snackbar_elevation</item>
        <item name="maxActionInlineWidth">@dimen/design_snackbar_action_inline_max_width</item>
    </style>
    <style name="Widget.Design.TabLayout" parent="@style/Base.Widget.Design.TabLayout">
        <item name="tabGravity">fill</item>
        <item name="tabIndicatorFullWidth">true</item>
        <item name="tabMode">fixed</item>
    </style>
    <style name="Widget.Design.TextInputLayout" parent="@android:style/Widget">
        <item name="boxBackgroundMode">none</item>
        <item name="counterOverflowTextAppearance">@style/TextAppearance.Design.Counter.Overflow</item>
        <item name="counterTextAppearance">@style/TextAppearance.Design.Counter</item>
        <item name="errorTextAppearance">@style/TextAppearance.Design.Error</item>
        <item name="helperTextTextAppearance">@style/TextAppearance.Design.HelperText</item>
        <item name="hintTextAppearance">@style/TextAppearance.Design.Hint</item>
        <item name="passwordToggleContentDescription">@string/password_toggle_content_description</item>
        <item name="passwordToggleDrawable">@drawable/design_password_eye</item>
        <item name="passwordToggleTint">@color/design_tint_password_toggle</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="backgroundTint">@android:color/white</item>
        <item name="fabCradleMargin">@dimen/mtrl_bottomappbar_fab_cradle_margin</item>
        <item name="fabCradleRoundedCornerRadius">@dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius</item>
        <item name="fabCradleVerticalOffset">@dimen/mtrl_bottomappbar_fab_cradle_vertical_offset</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar.Colored" parent="@style/Widget.MaterialComponents.BottomAppBar">
        <item name="backgroundTint">?colorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView" parent="@style/Widget.Design.BottomNavigationView">
        <item name="android:background">@android:color/white</item>
        <item name="enforceTextAppearance">true</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="itemIconTint">@color/mtrl_bottom_nav_item_tint</item>
        <item name="itemTextAppearanceActive">?textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_bottom_nav_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.Colored" parent="@style/Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">?colorPrimary</item>
        <item name="itemIconTint">@color/mtrl_bottom_nav_colored_item_tint</item>
        <item name="itemTextAppearanceActive">?textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_bottom_nav_colored_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomSheet.Modal" parent="@style/Widget.Design.BottomSheet.Modal" />
    <style name="Widget.MaterialComponents.Button" parent="@style/Widget.AppCompat.Button">
        <item name="android:textAppearance">?textAppearanceButton</item>
        <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
        <item name="android:insetLeft">0.0dip</item>
        <item name="android:insetRight">0.0dip</item>
        <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
        <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
        <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
        <item name="cornerRadius">@dimen/mtrl_btn_corner_radius</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.Icon" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="strokeColor">@color/mtrl_btn_stroke_color_selector</item>
        <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton.Icon" parent="@style/Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textColor">@color/mtrl_text_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_text_btn_padding_right</item>
        <item name="backgroundTint">@color/mtrl_btn_transparent_bg_color</item>
        <item name="iconPadding">@dimen/mtrl_btn_text_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_text_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_text_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton.Dialog" />
    <style name="Widget.MaterialComponents.Button.TextButton.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton" />
    <style name="Widget.MaterialComponents.Button.UnelevatedButton" parent="@style/Widget.MaterialComponents.Button" />
    <style name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.CardView" parent="@style/CardView">
        <item name="cardBackgroundColor">?colorBackgroundFloating</item>
        <item name="cardElevation">@dimen/mtrl_card_elevation</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Action" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Choice" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Entry" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Filter" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.ChipGroup" parent="@android:style/Widget">
        <item name="chipSpacing">4.0dip</item>
        <item name="singleLine">false</item>
        <item name="singleSelection">false</item>
    </style>
    <style name="Widget.MaterialComponents.FloatingActionButton" parent="@style/Widget.Design.FloatingActionButton">
        <item name="elevation">@dimen/mtrl_fab_elevation</item>
        <item name="hideMotionSpec">@animator/mtrl_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/mtrl_fab_translation_z_hovered_focused</item>
        <item name="pressedTranslationZ">@dimen/mtrl_fab_translation_z_pressed</item>
        <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
        <item name="showMotionSpec">@animator/mtrl_fab_show_motion_spec</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationView" parent="@style/Widget.Design.NavigationView">
        <item name="elevation">@dimen/mtrl_navigation_elevation</item>
        <item name="itemHorizontalPadding">@dimen/mtrl_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/mtrl_navigation_item_icon_padding</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar" parent="@style/Widget.Design.Snackbar">
        <item name="android:background">@drawable/mtrl_snackbar_background</item>
        <item name="android:layout_margin">@dimen/mtrl_snackbar_margin</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar.FullWidth" parent="@style/Widget.Design.Snackbar" />
    <style name="Widget.MaterialComponents.TabLayout" parent="@style/Widget.Design.TabLayout">
        <item name="android:background">@android:color/white</item>
        <item name="enforceTextAppearance">true</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabIndicatorAnimationDuration">@integer/mtrl_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?colorAccent</item>
        <item name="tabRippleColor">@color/mtrl_tabs_ripple_color</item>
        <item name="tabTextAppearance">?textAppearanceButton</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabUnboundedRipple">true</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout.Colored" parent="@style/Widget.MaterialComponents.TabLayout">
        <item name="android:background">?colorAccent</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector_colored</item>
        <item name="tabIndicatorColor">@android:color/white</item>
        <item name="tabRippleColor">@color/mtrl_tabs_colored_ripple_color</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector_colored</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText">
        <item name="android:paddingTop">20.0dip</item>
        <item name="android:paddingBottom">16.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.FilledBox">
        <item name="android:paddingTop">16.0dip</item>
        <item name="android:paddingBottom">16.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText" />
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:paddingTop">12.0dip</item>
        <item name="android:paddingBottom">12.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox</item>
        <item name="boxBackgroundColor">@color/mtrl_textinput_filled_box_default_background_color</item>
        <item name="boxBackgroundMode">filled</item>
        <item name="boxCollapsedPaddingTop">12.0dip</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/mtrl_textinput_box_corner_radius_small</item>
        <item name="boxCornerRadiusBottomStart">@dimen/mtrl_textinput_box_corner_radius_small</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
        <item name="boxCollapsedPaddingTop">8.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox</item>
        <item name="boxCollapsedPaddingTop">0.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.Toolbar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextAppearance">?textAppearanceSubtitle1</item>
        <item name="subtitleTextColor">?android:textColorSecondary</item>
        <item name="titleTextAppearance">?textAppearanceHeadline6</item>
        <item name="titleTextColor">?android:textColorPrimary</item>
    </style>
    <style name="Widget.Support.CoordinatorLayout" parent="@android:style/Widget">
        <item name="statusBarBackground">#ff000000</item>
    </style>
    <style name="qmui_dialog_wrap">
        <item name="android:background">?qmui_dialog_bg</item>
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">?qmui_dialog_margin_vertical</item>
        <item name="android:layout_marginBottom">?qmui_dialog_margin_vertical</item>
        <item name="qmui_radius">?qmui_dialog_radius</item>
    </style>
    <style name="qmui_tab_sign_count_view">
        <item name="android:textSize">10.0sp</item>
        <item name="android:textColor">@color/qmui_config_color_white</item>
        <item name="android:gravity">center</item>
        <item name="android:background">?qmui_tab_sign_count_view_bg</item>
        <item name="android:paddingLeft">?qmui_tab_sign_count_view_padding_horizontal</item>
        <item name="android:paddingRight">?qmui_tab_sign_count_view_padding_horizontal</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">?qmui_tab_sign_count_view_minSize</item>
        <item name="android:minWidth">?qmui_tab_sign_count_view_minSize</item>
        <item name="android:minHeight">?qmui_tab_sign_count_view_minSize</item>
        <item name="android:singleLine">true</item>
    </style>
    <style name="qmui_tip_dialog_wrap">
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:background">?qmui_tip_dialog_bg</item>
        <item name="android:paddingLeft">?qmui_tip_dialog_padding_horizontal</item>
        <item name="android:paddingTop">?qmui_tip_dialog_padding_vertical</item>
        <item name="android:paddingRight">?qmui_tip_dialog_padding_horizontal</item>
        <item name="android:paddingBottom">?qmui_tip_dialog_padding_vertical</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">?qmui_content_spacing_horizontal</item>
        <item name="android:layout_marginRight">?qmui_content_spacing_horizontal</item>
        <item name="android:minWidth">?qmui_tip_dialog_min_width</item>
        <item name="android:minHeight">?qmui_tip_dialog_min_height</item>
    </style>
    <style name="video_popup_toast_anim" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>
    <style name="video_style_dialog_progress" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/video_popup_toast_anim</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="video_vertical_progressBar">
        <item name="android:maxWidth">12.0dip</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">@android:drawable/progress_indeterminate_horizontal</item>
        <item name="android:progressDrawable">@drawable/video_volume_progress_bg</item>
        <item name="android:indeterminateDuration">3500</item>
        <item name="android:indeterminateBehavior">repeat</item>
        <item name="android:minWidth">1.0dip</item>
    </style>
</resources>
