.class public Lcom/gxxushang/tv/helper/SPUtils;
.super Ljava/lang/Object;
.source "SPUtils.java"


# static fields
.field public static lastTimeUpdate:J


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 71
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static checkUpdate(Landroid/content/Context;Z)V
    .locals 3

    .line 612
    const-class v0, Lcom/gxxushang/tv/model/SPApp;

    const-string v1, "<EMAIL>"

    invoke-static {v0, v1}, Lcom/gxxushang/tv/helper/SPApi;->post(Ljava/lang/Class;Ljava/lang/String;)Lcom/gxxushang/tv/helper/SPApi;

    move-result-object v0

    .line 613
    invoke-virtual {p0}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object v1

    const-string v2, "filter[package]"

    invoke-virtual {v0, v2, v1}, Lcom/gxxushang/tv/helper/SPApi;->addParam(Ljava/lang/String;Ljava/lang/Object;)Lcom/gxxushang/tv/helper/SPApi;

    move-result-object v0

    const-string v1, "order[id]"

    const-string v2, "DESC"

    .line 614
    invoke-virtual {v0, v1, v2}, Lcom/gxxushang/tv/helper/SPApi;->addParam(Ljava/lang/String;Ljava/lang/Object;)Lcom/gxxushang/tv/helper/SPApi;

    move-result-object v0

    new-instance v1, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$PMZIOq1FCMvFl66zex-UMeXSg2s;

    invoke-direct {v1, p0, p1}, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$PMZIOq1FCMvFl66zex-UMeXSg2s;-><init>(Landroid/content/Context;Z)V

    .line 615
    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPApi;->onOne(Lcom/gxxushang/tv/helper/SPCallback;)Lcom/gxxushang/tv/helper/SPApi;

    move-result-object p0

    sget-object p1, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$vt5mAX9BjoOgTUWHGnQxB0U24CE;->INSTANCE:Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$vt5mAX9BjoOgTUWHGnQxB0U24CE;

    .line 637
    invoke-virtual {p0, p1}, Lcom/gxxushang/tv/helper/SPApi;->onComplete(Lcom/gxxushang/tv/helper/SPCompleteCallback;)V

    return-void
.end method

.method public static darkStatusBar()V
    .locals 1

    .line 444
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    invoke-static {v0}, Lcom/qmuiteam/qmui/util/QMUIStatusBarHelper;->setStatusBarLightMode(Landroid/app/Activity;)Z

    return-void
.end method

.method public static deleteLocalData(Ljava/lang/String;)V
    .locals 1

    .line 228
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 229
    :cond_0
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    invoke-interface {v0}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    .line 230
    invoke-interface {v0, p0}, Landroid/content/SharedPreferences$Editor;->remove(Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    .line 231
    invoke-interface {v0}, Landroid/content/SharedPreferences$Editor;->commit()Z

    return-void
.end method

.method public static downloadApp(Lcom/gxxushang/tv/model/SPApp;Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;)V
    .locals 5

    .line 643
    sget-wide v0, Lcom/gxxushang/tv/helper/SPUtils;->lastTimeUpdate:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    return-void

    .line 644
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "https://ali-static-cdn.tiyatir.com/"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p0, p0, Lcom/gxxushang/tv/model/SPApp;->filename:Ljava/lang/String;

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    const-string v0, "Apk"

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->storagePath(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "main.apk"

    invoke-static {p0, v0, v1}, Lcom/downloader/PRDownloader;->download(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lcom/downloader/request/DownloadRequestBuilder;

    move-result-object p0

    .line 645
    invoke-virtual {p0}, Lcom/downloader/request/DownloadRequestBuilder;->build()Lcom/downloader/request/DownloadRequest;

    move-result-object p0

    new-instance v0, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$KkFIZfZmzvEjJxrzWTeg9k21p78;

    invoke-direct {v0, p1}, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$KkFIZfZmzvEjJxrzWTeg9k21p78;-><init>(Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;)V

    .line 646
    invoke-virtual {p0, v0}, Lcom/downloader/request/DownloadRequest;->setOnProgressListener(Lcom/downloader/OnProgressListener;)Lcom/downloader/request/DownloadRequest;

    move-result-object p0

    new-instance v0, Lcom/gxxushang/tv/helper/SPUtils$2;

    invoke-direct {v0, p1}, Lcom/gxxushang/tv/helper/SPUtils$2;-><init>(Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;)V

    .line 654
    invoke-virtual {p0, v0}, Lcom/downloader/request/DownloadRequest;->start(Lcom/downloader/OnDownloadListener;)I

    return-void
.end method

.method public static dp2px(F)I
    .locals 2

    .line 86
    invoke-static {}, Landroid/content/res/Resources;->getSystem()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    const/4 v1, 0x1

    invoke-static {v1, p0, v0}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result p0

    float-to-int p0, p0

    return p0
.end method

.method public static font()Landroid/graphics/Typeface;
    .locals 2

    .line 346
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->lang()I

    move-result v0

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    sget-object v0, Landroid/graphics/Typeface;->DEFAULT:Landroid/graphics/Typeface;

    return-object v0

    .line 347
    :cond_0
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    const-string v1, "alkatip"

    invoke-static {v1, v0}, Lcom/gxxushang/tv/helper/SPFont;->getTypeface(Ljava/lang/String;Landroid/content/Context;)Landroid/graphics/Typeface;

    move-result-object v0

    return-object v0
.end method

.method public static getAppVersionCode(Landroid/content/Context;)J
    .locals 2

    .line 394
    :try_start_0
    invoke-virtual {p0}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    .line 395
    invoke-virtual {v0}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v0

    .line 396
    invoke-virtual {p0}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object p0

    const/4 v1, 0x0

    invoke-virtual {v0, p0, v1}, Landroid/content/pm/PackageManager;->getPackageInfo(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;

    move-result-object p0

    .line 397
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x1c

    if-lt v0, v1, :cond_0

    .line 398
    invoke-virtual {p0}, Landroid/content/pm/PackageInfo;->getLongVersionCode()J

    move-result-wide v0

    goto :goto_0

    .line 400
    :cond_0
    iget p0, p0, Landroid/content/pm/PackageInfo;->versionCode:I
    :try_end_0
    .catch Landroid/content/pm/PackageManager$NameNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    int-to-long v0, p0

    goto :goto_0

    :catch_0
    const-wide/16 v0, 0x0

    :goto_0
    return-wide v0
.end method

.method public static getClickableHtml(Ljava/lang/String;)Ljava/lang/CharSequence;
    .locals 4

    .line 565
    invoke-static {p0}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object p0

    .line 566
    new-instance v0, Landroid/text/SpannableStringBuilder;

    invoke-direct {v0, p0}, Landroid/text/SpannableStringBuilder;-><init>(Ljava/lang/CharSequence;)V

    .line 567
    invoke-interface {p0}, Landroid/text/Spanned;->length()I

    move-result p0

    const-class v1, Landroid/text/style/URLSpan;

    const/4 v2, 0x0

    invoke-virtual {v0, v2, p0, v1}, Landroid/text/SpannableStringBuilder;->getSpans(IILjava/lang/Class;)[Ljava/lang/Object;

    move-result-object p0

    check-cast p0, [Landroid/text/style/URLSpan;

    .line 568
    array-length v1, p0

    :goto_0
    if-ge v2, v1, :cond_0

    aget-object v3, p0, v2

    .line 569
    invoke-static {v0, v3}, Lcom/gxxushang/tv/helper/SPUtils;->setLinkClickable(Landroid/text/SpannableStringBuilder;Landroid/text/style/URLSpan;)V

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_0
    return-object v0
.end method

.method public static getClientId()I
    .locals 1

    const-string v0, "client_id"

    .line 190
    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->getLocalDataAsInt(Ljava/lang/String;)I

    move-result v0

    return v0
.end method

.method public static getContext()Landroid/content/Context;
    .locals 1

    .line 76
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 77
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    return-object v0

    .line 78
    :cond_0
    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->getApp()Landroid/app/Application;

    move-result-object v0

    if-eqz v0, :cond_1

    .line 79
    invoke-static {}, Lcom/blankj/utilcode/util/Utils;->getApp()Landroid/app/Application;

    move-result-object v0

    invoke-virtual {v0}, Landroid/app/Application;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    return-object v0

    :cond_1
    const/4 v0, 0x0

    return-object v0
.end method

.method public static getDrawable(I)Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 249
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p0}, Landroid/support/v4/content/ContextCompat;->getDrawable(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object p0

    return-object p0
.end method

.method public static getExtDownloadsPath()Ljava/lang/String;
    .locals 1

    .line 409
    sget-object v0, Landroid/os/Environment;->DIRECTORY_DOWNLOADS:Ljava/lang/String;

    invoke-static {v0}, Landroid/os/Environment;->getExternalStoragePublicDirectory(Ljava/lang/String;)Ljava/io/File;

    move-result-object v0

    invoke-virtual {v0}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public static varargs getFormatString(I[Ljava/lang/Object;)Ljava/lang/String;
    .locals 0

    .line 293
    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->getString(I)Ljava/lang/String;

    move-result-object p0

    invoke-static {p0, p1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getLocal(I)Ljava/util/Locale;
    .locals 2

    const/4 v0, 0x1

    if-eq p0, v0, :cond_0

    .line 361
    new-instance p0, Ljava/util/Locale;

    sget-object v0, Ljava/util/Locale;->CHINA:Ljava/util/Locale;

    invoke-virtual {v0}, Ljava/util/Locale;->getCountry()Ljava/lang/String;

    move-result-object v0

    const-string v1, "ug"

    invoke-direct {p0, v1, v0}, Ljava/util/Locale;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    return-object p0

    .line 358
    :cond_0
    sget-object p0, Ljava/util/Locale;->SIMPLIFIED_CHINESE:Ljava/util/Locale;

    return-object p0
.end method

.method public static getLocalData(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/String;",
            "Ljava/lang/Class<",
            "TT;>;)TT;"
        }
    .end annotation

    .line 151
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 p0, 0x0

    return-object p0

    :cond_0
    const-string v1, ""

    .line 153
    invoke-interface {v0, p0, v1}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    .line 154
    new-instance v0, Lcom/google/gson/Gson;

    invoke-direct {v0}, Lcom/google/gson/Gson;-><init>()V

    invoke-virtual {v0, p0, p1}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static getLocalData(Ljava/lang/String;)Ljava/lang/String;
    .locals 2

    .line 157
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    .line 159
    :cond_0
    invoke-interface {v0, p0, v1}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getLocalDataAsInt(Ljava/lang/String;)I
    .locals 3

    const-string v0, ""

    .line 195
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v1

    const/4 v2, 0x0

    if-nez v1, :cond_0

    return v2

    .line 199
    :cond_0
    :try_start_0
    invoke-interface {v1, p0, v0}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    nop

    .line 203
    :goto_0
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result p0

    if-nez p0, :cond_1

    return v2

    .line 204
    :cond_1
    invoke-static {v0}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p0

    return p0
.end method

.method public static getMallId()I
    .locals 4

    .line 180
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    const-string v2, "mall_id"

    const-string v3, ""

    .line 182
    invoke-interface {v0, v2, v3}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 183
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v2

    if-lez v2, :cond_1

    .line 184
    invoke-static {v0}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0

    return v0

    :cond_1
    return v1
.end method

.method public static getMd5String(Ljava/lang/String;)Ljava/lang/String;
    .locals 5

    :try_start_0
    const-string v0, "MD5"

    .line 236
    invoke-static {v0}, Ljava/security/MessageDigest;->getInstance(Ljava/lang/String;)Ljava/security/MessageDigest;

    move-result-object v0

    .line 237
    invoke-virtual {p0}, Ljava/lang/String;->getBytes()[B

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/security/MessageDigest;->digest([B)[B

    move-result-object p0

    .line 238
    new-instance v0, Ljava/lang/StringBuffer;

    invoke-direct {v0}, Ljava/lang/StringBuffer;-><init>()V

    const/4 v1, 0x0

    .line 239
    :goto_0
    array-length v2, p0

    if-ge v1, v2, :cond_0

    .line 240
    aget-byte v2, p0, v1

    and-int/lit16 v2, v2, 0xff

    or-int/lit16 v2, v2, 0x100

    invoke-static {v2}, Ljava/lang/Integer;->toHexString(I)Ljava/lang/String;

    move-result-object v2

    const/4 v3, 0x3

    const/4 v4, 0x1

    invoke-virtual {v2, v4, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    .line 242
    :cond_0
    invoke-virtual {v0}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    move-result-object p0
    :try_end_0
    .catch Ljava/security/NoSuchAlgorithmException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    const/4 p0, 0x0

    return-object p0
.end method

.method public static getRtlSpace()Ljava/lang/String;
    .locals 1

    .line 351
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->rtl()Z

    move-result v0

    if-eqz v0, :cond_0

    const-string v0, " "

    return-object v0

    :cond_0
    const-string v0, ""

    return-object v0
.end method

.method public static getScreenPxHeight()I
    .locals 1

    .line 113
    invoke-static {}, Landroid/content/res/Resources;->getSystem()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    iget v0, v0, Landroid/util/DisplayMetrics;->heightPixels:I

    return v0
.end method

.method public static getScreenPxWidth()F
    .locals 1

    .line 108
    invoke-static {}, Landroid/content/res/Resources;->getSystem()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    iget v0, v0, Landroid/util/DisplayMetrics;->widthPixels:I

    int-to-float v0, v0

    return v0
.end method

.method public static getSharedPreferences()Landroid/content/SharedPreferences;
    .locals 3

    .line 147
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return-object v0

    .line 148
    :cond_0
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    const/4 v1, 0x0

    const-string v2, "SubatMain"

    invoke-virtual {v0, v2, v1}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;

    move-result-object v0

    return-object v0
.end method

.method public static getStatusBarHeight(Landroid/content/Context;)I
    .locals 3

    .line 463
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p0

    const-string v0, "status_bar_height"

    const-string v1, "dimen"

    const-string v2, "android"

    .line 464
    invoke-virtual {p0, v0, v1, v2}, Landroid/content/res/Resources;->getIdentifier(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)I

    move-result v0

    .line 465
    invoke-virtual {p0, v0}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    move-result p0

    int-to-float p0, p0

    .line 466
    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->px2dp(F)F

    move-result p0

    float-to-int p0, p0

    return p0
.end method

.method public static getString(I)Ljava/lang/String;
    .locals 1

    .line 289
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0, p0}, Landroid/content/res/Resources;->getString(I)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static getThemeId(I)I
    .locals 3

    if-lez p0, :cond_0

    .line 253
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 254
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0, p0}, Landroid/content/res/Resources;->getResourceName(I)Ljava/lang/String;

    move-result-object v0

    .line 255
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "_night"

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const/4 v2, 0x0

    invoke-virtual {v1, v0, v2, v2}, Landroid/content/res/Resources;->getIdentifier(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)I

    move-result v0

    if-lez v0, :cond_0

    move p0, v0

    :cond_0
    return p0
.end method

.method public static getTimeFormatText(J)Ljava/lang/String;
    .locals 3

    const-wide v0, 0x77bbdb000L

    cmp-long v2, p0, v0

    if-lez v2, :cond_0

    .line 309
    div-long/2addr p0, v0

    .line 310
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0, p1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getRtlSpace()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const p0, 0x7f0d00d2

    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->getString(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_0
    const-wide v0, 0x9fa52400L

    cmp-long v2, p0, v0

    if-lez v2, :cond_1

    .line 313
    div-long/2addr p0, v0

    .line 314
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0, p1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getRtlSpace()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const p0, 0x7f0d0083

    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->getString(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_1
    const-wide/32 v0, 0x5265c00

    cmp-long v2, p0, v0

    if-lez v2, :cond_2

    .line 317
    div-long/2addr p0, v0

    .line 318
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0, p1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getRtlSpace()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const p0, 0x7f0d0041

    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->getString(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_2
    const-wide/32 v0, 0x36ee80

    cmp-long v2, p0, v0

    if-lez v2, :cond_3

    .line 321
    div-long/2addr p0, v0

    .line 322
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0, p1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getRtlSpace()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const p0, 0x7f0d0074

    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->getString(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_3
    const-wide/32 v0, 0xea60

    cmp-long v2, p0, v0

    if-lez v2, :cond_4

    .line 325
    div-long/2addr p0, v0

    .line 326
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0, p1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getRtlSpace()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const p0, 0x7f0d0082

    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->getString(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_4
    const-wide/16 v0, 0x3e8

    cmp-long v2, p0, v0

    if-lez v2, :cond_5

    .line 329
    div-long/2addr p0, v0

    .line 330
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v0, p0, p1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getRtlSpace()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const p0, 0x7f0d00b1

    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->getString(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_5
    const p0, 0x7f0d0079

    .line 332
    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->getString(I)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static final getUriToDrawable(Landroid/content/Context;I)Landroid/net/Uri;
    .locals 3

    .line 601
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "android.resource://"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 602
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    invoke-virtual {v1, p1}, Landroid/content/res/Resources;->getResourcePackageName(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x2f

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 603
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    invoke-virtual {v2, p1}, Landroid/content/res/Resources;->getResourceTypeName(I)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 604
    invoke-virtual {p0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object p0

    invoke-virtual {p0, p1}, Landroid/content/res/Resources;->getResourceEntryName(I)Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    .line 601
    invoke-static {p0}, Landroid/net/Uri;->parse(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object p0

    return-object p0
.end method

.method public static getUserId()I
    .locals 4

    .line 163
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    const-string/jumbo v2, "user_id"

    const-string v3, ""

    .line 165
    invoke-interface {v0, v2, v3}, Landroid/content/SharedPreferences;->getString(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 166
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    move-result v2

    if-lez v2, :cond_1

    .line 167
    invoke-static {v0}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0

    return v0

    :cond_1
    return v1
.end method

.method public static getWebLang()Ljava/lang/String;
    .locals 2

    const-string v0, "app_language"

    .line 367
    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->getLocalDataAsInt(Ljava/lang/String;)I

    move-result v0

    const/4 v1, 0x1

    if-eq v0, v1, :cond_0

    const-string v0, "ug"

    return-object v0

    :cond_0
    const-string/jumbo v0, "zh"

    return-object v0
.end method

.method public static handleUri(Landroid/net/Uri;)Z
    .locals 6

    const/4 v0, 0x0

    if-nez p0, :cond_0

    return v0

    .line 471
    :cond_0
    invoke-virtual {p0}, Landroid/net/Uri;->getHost()Ljava/lang/String;

    move-result-object v1

    if-nez v1, :cond_1

    return v0

    .line 472
    :cond_1
    invoke-virtual {p0}, Landroid/net/Uri;->getHost()Ljava/lang/String;

    move-result-object v1

    const-string v2, "tiyatir.gxxushang.com"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x1

    const-string/jumbo v3, "url"

    if-nez v1, :cond_2

    .line 473
    new-instance v0, Lcom/gxxushang/tv/fragment/SPWebViewFragment;

    invoke-direct {v0}, Lcom/gxxushang/tv/fragment/SPWebViewFragment;-><init>()V

    .line 474
    invoke-virtual {p0}, Landroid/net/Uri;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, v3, p0}, Lcom/gxxushang/tv/fragment/SPWebViewFragment;->setParam(Ljava/lang/String;Ljava/lang/String;)V

    .line 475
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object p0

    check-cast p0, Lcom/gxxushang/tv/base/SPBaseActivity;

    .line 476
    invoke-virtual {p0, v0}, Lcom/gxxushang/tv/base/SPBaseActivity;->navigateTo(Lcom/gxxushang/tv/base/SPBaseFragment;)V

    return v2

    .line 480
    :cond_2
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v1

    check-cast v1, Lcom/gxxushang/tv/base/SPBaseActivity;

    .line 482
    invoke-virtual {p0}, Landroid/net/Uri;->getPath()Ljava/lang/String;

    move-result-object v4

    if-nez v4, :cond_3

    const-string v4, ""

    :cond_3
    const-string v5, "id"

    .line 486
    invoke-virtual {p0, v5}, Landroid/net/Uri;->getQueryParameter(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    if-eqz v5, :cond_4

    .line 489
    invoke-static {v5}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0

    :cond_4
    const-string v5, "/video/channel"

    .line 491
    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_5

    goto/16 :goto_0

    :cond_5
    const-string v5, "/video/video"

    .line 493
    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_6

    goto/16 :goto_0

    :cond_6
    const-string v5, "/music/song"

    .line 495
    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_7

    goto/16 :goto_0

    :cond_7
    const-string v5, "/vbook/book"

    .line 497
    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_8

    goto/16 :goto_0

    :cond_8
    const-string v5, "/movie/movie"

    .line 499
    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_9

    .line 500
    new-instance p0, Lcom/gxxushang/tv/model/SPMovie;

    invoke-direct {p0}, Lcom/gxxushang/tv/model/SPMovie;-><init>()V

    .line 501
    iput v0, p0, Lcom/gxxushang/tv/model/SPMovie;->id:I

    .line 502
    new-instance v0, Lcom/gxxushang/tv/fragment/SPMovieDetailFragment;

    invoke-direct {v0}, Lcom/gxxushang/tv/fragment/SPMovieDetailFragment;-><init>()V

    const-string v3, "movie"

    .line 503
    invoke-virtual {v0, v3, p0}, Lcom/gxxushang/tv/fragment/SPMovieDetailFragment;->setParam(Ljava/lang/String;Lcom/gxxushang/tv/model/SPBaseModel;)V

    .line 504
    invoke-virtual {v1, v0}, Lcom/gxxushang/tv/base/SPBaseActivity;->navigateTo(Lcom/gxxushang/tv/base/SPBaseFragment;)V

    goto/16 :goto_0

    :cond_9
    const-string v5, "/movie/list"

    .line 505
    invoke-virtual {v4, v5}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v5

    if-eqz v5, :cond_a

    .line 506
    new-instance p0, Lcom/gxxushang/tv/model/SPMovieCollection;

    invoke-direct {p0}, Lcom/gxxushang/tv/model/SPMovieCollection;-><init>()V

    .line 507
    iput v0, p0, Lcom/gxxushang/tv/model/SPMovieCollection;->id:I

    .line 508
    new-instance v0, Lcom/gxxushang/tv/fragment/player/SPMoviePlayerFragment;

    invoke-direct {v0}, Lcom/gxxushang/tv/fragment/player/SPMoviePlayerFragment;-><init>()V

    const-string v3, "collection"

    .line 509
    invoke-virtual {v0, v3, p0}, Lcom/gxxushang/tv/fragment/player/SPMoviePlayerFragment;->setParam(Ljava/lang/String;Lcom/gxxushang/tv/model/SPBaseModel;)V

    .line 510
    invoke-virtual {v1, v0}, Lcom/gxxushang/tv/base/SPBaseActivity;->navigateTo(Lcom/gxxushang/tv/base/SPBaseFragment;)V

    goto :goto_0

    :cond_a
    const-string v0, "/music/album"

    .line 511
    invoke-virtual {v4, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_b

    goto :goto_0

    :cond_b
    const-string v0, "/common/more"

    .line 513
    invoke-virtual {v4, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_c

    .line 514
    invoke-virtual {p0}, Landroid/net/Uri;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Lcom/gxxushang/tv/model/SPMoreModel;->createFromUrl(Ljava/lang/String;)Lcom/gxxushang/tv/model/SPMoreModel;

    move-result-object p0

    .line 515
    new-instance v0, Lcom/gxxushang/tv/fragment/SPMoreFragment;

    invoke-direct {v0}, Lcom/gxxushang/tv/fragment/SPMoreFragment;-><init>()V

    const-string v3, "more"

    .line 516
    invoke-virtual {v0, v3, p0}, Lcom/gxxushang/tv/fragment/SPMoreFragment;->setParam(Ljava/lang/String;Lcom/gxxushang/tv/model/SPBaseModel;)V

    .line 517
    invoke-virtual {v1, v0}, Lcom/gxxushang/tv/base/SPBaseActivity;->navigateTo(Lcom/gxxushang/tv/base/SPBaseFragment;)V

    goto :goto_0

    :cond_c
    const-string v0, "/music/playlist"

    .line 518
    invoke-virtual {v4, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_d

    goto :goto_0

    :cond_d
    const-string v0, "/movie/category"

    .line 520
    invoke-virtual {v4, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_e

    .line 521
    new-instance p0, Lcom/gxxushang/tv/fragment/SPFilterFragment;

    invoke-direct {p0}, Lcom/gxxushang/tv/fragment/SPFilterFragment;-><init>()V

    .line 522
    invoke-virtual {v1, p0}, Lcom/gxxushang/tv/base/SPBaseActivity;->navigateTo(Lcom/gxxushang/tv/base/SPBaseFragment;)V

    goto :goto_0

    :cond_e
    const-string v0, "/movie/search"

    .line 523
    invoke-virtual {v4, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_f

    .line 524
    new-instance p0, Lcom/gxxushang/tv/fragment/SPSearchFragment;

    invoke-direct {p0}, Lcom/gxxushang/tv/fragment/SPSearchFragment;-><init>()V

    .line 525
    invoke-virtual {v1, p0}, Lcom/gxxushang/tv/base/SPBaseActivity;->navigateTo(Lcom/gxxushang/tv/base/SPBaseFragment;)V

    goto :goto_0

    .line 527
    :cond_f
    new-instance v0, Lcom/gxxushang/tv/fragment/SPWebViewFragment;

    invoke-direct {v0}, Lcom/gxxushang/tv/fragment/SPWebViewFragment;-><init>()V

    .line 528
    invoke-virtual {p0}, Landroid/net/Uri;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {v0, v3, p0}, Lcom/gxxushang/tv/fragment/SPWebViewFragment;->setParam(Ljava/lang/String;Ljava/lang/String;)V

    .line 529
    invoke-virtual {v1, v0}, Lcom/gxxushang/tv/base/SPBaseActivity;->navigateTo(Lcom/gxxushang/tv/base/SPBaseFragment;)V

    :goto_0
    return v2
.end method

.method public static hideStatusBar()V
    .locals 2

    .line 414
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 415
    :cond_0
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    invoke-virtual {v0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/Window;->getDecorView()Landroid/view/View;

    move-result-object v0

    const/16 v1, 0x1706

    .line 416
    invoke-virtual {v0, v1}, Landroid/view/View;->setSystemUiVisibility(I)V

    return-void
.end method

.method public static installApk(Ljava/lang/String;)V
    .locals 4

    .line 669
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 671
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object p0

    if-nez p0, :cond_0

    return-void

    .line 673
    :cond_0
    sget p0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x18

    if-lt p0, v1, :cond_1

    .line 674
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object p0

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v3

    invoke-virtual {v3}, Landroid/app/Activity;->getApplicationContext()Landroid/content/Context;

    move-result-object v3

    invoke-virtual {v3}, Landroid/content/Context;->getPackageName()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v3, ".provider"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {p0, v2, v0}, Landroid/support/v4/content/FileProvider;->getUriForFile(Landroid/content/Context;Ljava/lang/String;Ljava/io/File;)Landroid/net/Uri;

    move-result-object p0

    goto :goto_0

    .line 676
    :cond_1
    invoke-static {v0}, Landroid/net/Uri;->fromFile(Ljava/io/File;)Landroid/net/Uri;

    move-result-object p0

    .line 679
    :goto_0
    new-instance v0, Landroid/content/Intent;

    const-string v2, "android.intent.action.VIEW"

    invoke-direct {v0, v2}, Landroid/content/Intent;-><init>(Ljava/lang/String;)V

    const-string v2, "application/vnd.android.package-archive"

    .line 680
    invoke-virtual {v0, p0, v2}, Landroid/content/Intent;->setDataAndType(Landroid/net/Uri;Ljava/lang/String;)Landroid/content/Intent;

    const/high16 p0, 0x10000000

    .line 681
    invoke-virtual {v0, p0}, Landroid/content/Intent;->setFlags(I)Landroid/content/Intent;

    .line 682
    sget p0, Landroid/os/Build$VERSION;->SDK_INT:I

    if-lt p0, v1, :cond_2

    const/4 p0, 0x1

    .line 683
    invoke-virtual {v0, p0}, Landroid/content/Intent;->addFlags(I)Landroid/content/Intent;

    .line 685
    :cond_2
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object p0

    invoke-virtual {p0}, Landroid/app/Activity;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object p0

    invoke-virtual {v0, p0}, Landroid/content/Intent;->resolveActivity(Landroid/content/pm/PackageManager;)Landroid/content/ComponentName;

    move-result-object p0

    if-eqz p0, :cond_3

    .line 686
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object p0

    invoke-virtual {p0, v0}, Landroid/app/Activity;->startActivity(Landroid/content/Intent;)V

    goto :goto_1

    :cond_3
    const p0, 0x7f0d0077

    .line 688
    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->showInfo(I)V

    :goto_1
    return-void
.end method

.method public static isVip()Z
    .locals 1
    .line 297
    const/4 v0, 0x1
    return v0
.end method

.method static synthetic lambda$checkUpdate$0(Lcom/gxxushang/tv/model/SPApp;Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;)V
    .locals 0

    .line 625
    invoke-static {p0, p1}, Lcom/gxxushang/tv/helper/SPUtils;->downloadApp(Lcom/gxxushang/tv/model/SPApp;Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;)V

    return-void
.end method

.method static synthetic lambda$checkUpdate$1(Landroid/content/Context;)V
    .locals 2

    const v0, 0x7f0d00b7

    const/4 v1, 0x1

    .line 626
    invoke-static {p0, v0, v1}, Les/dmoral/toasty/Toasty;->error(Landroid/content/Context;II)Landroid/widget/Toast;

    move-result-object p0

    invoke-virtual {p0}, Landroid/widget/Toast;->show()V

    return-void
.end method

.method static synthetic lambda$checkUpdate$2(Lcom/gxxushang/tv/model/SPApp;Landroid/content/Context;Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;)V
    .locals 2

    .line 625
    invoke-static {}, Lcom/gxxushang/tv/helper/SPPermission;->getInstance()Lcom/gxxushang/tv/helper/SPPermission;

    move-result-object v0

    new-instance v1, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$NqTpM-4hc_TDewjlEHYEFbTbTZ0;

    invoke-direct {v1, p0, p2}, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$NqTpM-4hc_TDewjlEHYEFbTbTZ0;-><init>(Lcom/gxxushang/tv/model/SPApp;Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;)V

    invoke-virtual {v0, v1}, Lcom/gxxushang/tv/helper/SPPermission;->onAccept(Lcom/gxxushang/tv/helper/SPPermission$AcceptListener;)Lcom/gxxushang/tv/helper/SPPermission;

    move-result-object p0

    new-instance p2, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$ceb_yQ8DOZujNzapG9s3pApF0bw;

    invoke-direct {p2, p1}, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$ceb_yQ8DOZujNzapG9s3pApF0bw;-><init>(Landroid/content/Context;)V

    invoke-virtual {p0, p2}, Lcom/gxxushang/tv/helper/SPPermission;->onReject(Lcom/gxxushang/tv/helper/SPPermission$RejectListener;)Lcom/gxxushang/tv/helper/SPPermission;

    move-result-object p0

    const/4 p1, 0x1

    new-array p1, p1, [Ljava/lang/String;

    const/4 p2, 0x0

    const-string v0, "android.permission.WRITE_EXTERNAL_STORAGE"

    aput-object v0, p1, p2

    .line 627
    invoke-virtual {p0, p1}, Lcom/gxxushang/tv/helper/SPPermission;->request([Ljava/lang/String;)Lcom/gxxushang/tv/helper/SPPermission;

    return-void
.end method

.method static synthetic lambda$checkUpdate$3(Landroid/content/Context;ZLcom/gxxushang/tv/model/SPApp;ILjava/lang/String;)V
    .locals 3

    const/4 p4, 0x1

    if-ne p3, p4, :cond_1

    .line 617
    iget p3, p2, Lcom/gxxushang/tv/model/SPApp;->version_code:I

    int-to-long p3, p3

    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->getAppVersionCode(Landroid/content/Context;)J

    move-result-wide v0

    cmp-long v2, p3, v0

    if-lez v2, :cond_0

    .line 618
    invoke-static {}, Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;->create()Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;

    move-result-object p1

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const p4, 0x7f0d006e

    .line 619
    invoke-static {p4}, Lcom/gxxushang/tv/helper/SPUtils;->getString(I)Ljava/lang/String;

    move-result-object p4

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p4, " : "

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p4, p2, Lcom/gxxushang/tv/model/SPApp;->version:Ljava/lang/String;

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p1, p3}, Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;->setTitle(Ljava/lang/String;)Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;

    move-result-object p1

    iget-object p3, p2, Lcom/gxxushang/tv/model/SPApp;->description:Ljava/lang/String;

    .line 620
    invoke-virtual {p1, p3}, Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;->setDetail(Ljava/lang/String;)Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;

    move-result-object p1

    const p3, 0x7f0d00c4

    .line 621
    invoke-virtual {p1, p3}, Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;->setConfirmText(I)Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;

    move-result-object p1

    const p3, 0x7f0d00c5

    .line 622
    invoke-virtual {p1, p3}, Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;->setTipViewText(I)Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;

    move-result-object p1

    .line 623
    invoke-virtual {p1}, Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;->setConfirmFocus()Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;

    move-result-object p1

    new-instance p3, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$bKNiTLLizB_BcdWD8fHUtTdbjkk;

    invoke-direct {p3, p2, p0}, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$bKNiTLLizB_BcdWD8fHUtTdbjkk;-><init>(Lcom/gxxushang/tv/model/SPApp;Landroid/content/Context;)V

    .line 624
    invoke-virtual {p1, p3}, Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;->onConfirm(Lcom/gxxushang/tv/view/dialog/SPConfirmDialog$OnConfirm;)Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;

    move-result-object p0

    .line 630
    invoke-virtual {p0}, Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;->show()V

    goto :goto_0

    :cond_0
    if-eqz p1, :cond_1

    const p0, 0x7f0d008a

    .line 633
    invoke-static {p0}, Lcom/gxxushang/tv/helper/SPUtils;->showInfo(I)V

    :cond_1
    :goto_0
    return-void
.end method

.method static synthetic lambda$checkUpdate$4(Lcom/gxxushang/tv/model/SPResponse;)V
    .locals 4

    .line 638
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    const-wide/16 v2, 0x3e8

    div-long/2addr v0, v2

    long-to-int p0, v0

    const-string/jumbo v0, "update_time"

    invoke-static {v0, p0}, Lcom/gxxushang/tv/helper/SPUtils;->setLocalData(Ljava/lang/String;I)V

    return-void
.end method

.method static synthetic lambda$downloadApp$5(Lcom/downloader/Progress;Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;)V
    .locals 3

    .line 650
    iget-wide v0, p0, Lcom/downloader/Progress;->currentBytes:J

    long-to-float v0, v0

    iget-wide v1, p0, Lcom/downloader/Progress;->totalBytes:J

    long-to-float p0, v1

    div-float/2addr v0, p0

    .line 651
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v1, 0x1

    new-array v1, v1, [Ljava/lang/Object;

    const/high16 v2, 0x42c80000    # 100.0f

    mul-float v0, v0, v2

    invoke-static {v0}, Ljava/lang/Float;->valueOf(F)Ljava/lang/Float;

    move-result-object v0

    const/4 v2, 0x0

    aput-object v0, v1, v2

    const-string v0, "%.2f"

    invoke-static {v0, v1}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "%"

    invoke-virtual {p0, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    invoke-virtual {p1, p0}, Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;->setConfirmText(Ljava/lang/String;)Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;

    return-void
.end method

.method static synthetic lambda$downloadApp$6(Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;Lcom/downloader/Progress;)V
    .locals 5

    .line 647
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    sget-wide v2, Lcom/gxxushang/tv/helper/SPUtils;->lastTimeUpdate:J

    sub-long/2addr v0, v2

    const-wide/16 v2, 0x32

    cmp-long v4, v0, v2

    if-gez v4, :cond_0

    return-void

    .line 648
    :cond_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    sput-wide v0, Lcom/gxxushang/tv/helper/SPUtils;->lastTimeUpdate:J

    .line 649
    new-instance v0, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$wKnCbjh9jmgNjmlUY92Z2q3K-7o;

    invoke-direct {v0, p1, p0}, Lcom/gxxushang/tv/helper/-$$Lambda$SPUtils$wKnCbjh9jmgNjmlUY92Z2q3K-7o;-><init>(Lcom/downloader/Progress;Lcom/gxxushang/tv/view/dialog/SPConfirmDialog;)V

    invoke-static {v0}, Lcom/blankj/utilcode/util/ThreadUtils;->runOnUiThread(Ljava/lang/Runnable;)V

    return-void
.end method

.method public static lang()I
    .locals 1

    const-string v0, "app_language"

    .line 337
    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->getLocalDataAsInt(Ljava/lang/String;)I

    move-result v0

    return v0
.end method

.method public static lightStatusBar()V
    .locals 1

    .line 440
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    invoke-static {v0}, Lcom/qmuiteam/qmui/util/QMUIStatusBarHelper;->setStatusBarDarkMode(Landroid/app/Activity;)Z

    return-void
.end method

.method public static linkTreeMapToParam(Lcom/google/gson/internal/LinkedTreeMap;)Ljava/util/HashMap;
    .locals 8

    .line 265
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    if-nez p0, :cond_0

    return-object v0

    .line 267
    :cond_0
    invoke-virtual {p0}, Lcom/google/gson/internal/LinkedTreeMap;->keySet()Ljava/util/Set;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v1

    :cond_1
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v2

    if-eqz v2, :cond_5

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v2

    .line 268
    invoke-virtual {p0, v2}, Lcom/google/gson/internal/LinkedTreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    instance-of v3, v3, Lcom/google/gson/internal/LinkedTreeMap;

    if-eqz v3, :cond_3

    .line 269
    invoke-virtual {p0, v2}, Lcom/google/gson/internal/LinkedTreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/google/gson/internal/LinkedTreeMap;

    .line 270
    invoke-virtual {v3}, Lcom/google/gson/internal/LinkedTreeMap;->keySet()Ljava/util/Set;

    move-result-object v4

    invoke-interface {v4}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object v4

    :goto_1
    invoke-interface {v4}, Ljava/util/Iterator;->hasNext()Z

    move-result v5

    if-eqz v5, :cond_1

    invoke-interface {v4}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v5

    .line 271
    invoke-virtual {v3, v5}, Lcom/google/gson/internal/LinkedTreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 272
    invoke-virtual {v3, v5}, Lcom/google/gson/internal/LinkedTreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    instance-of v6, v6, Ljava/lang/Double;

    if-eqz v6, :cond_2

    .line 273
    invoke-virtual {v3, v5}, Lcom/google/gson/internal/LinkedTreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v6

    check-cast v6, Ljava/lang/Double;

    invoke-virtual {v6}, Ljava/lang/Double;->intValue()I

    move-result v6

    invoke-static {v6}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 275
    :cond_2
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v7, "["

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v7, "]"

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v3, v5}, Lcom/google/gson/internal/LinkedTreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v5

    invoke-virtual {v0, v6, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto :goto_1

    .line 278
    :cond_3
    invoke-virtual {p0, v2}, Lcom/google/gson/internal/LinkedTreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    .line 279
    invoke-virtual {p0, v2}, Lcom/google/gson/internal/LinkedTreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    instance-of v4, v4, Ljava/lang/Double;

    if-eqz v4, :cond_4

    .line 280
    invoke-virtual {p0, v2}, Lcom/google/gson/internal/LinkedTreeMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Double;

    invoke-virtual {v3}, Ljava/lang/Double;->intValue()I

    move-result v3

    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v3

    .line 282
    :cond_4
    invoke-virtual {v0, v2, v3}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    goto/16 :goto_0

    :cond_5
    return-object v0
.end method

.method public static px2dp(F)F
    .locals 1

    .line 90
    invoke-static {}, Landroid/content/res/Resources;->getSystem()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    iget v0, v0, Landroid/util/DisplayMetrics;->density:F

    div-float/2addr p0, v0

    return p0
.end method

.method public static requireLogin(Lcom/gxxushang/tv/base/SPBaseFragment$Navigator;)Z
    .locals 1
    .line 172
    const/4 v0, 0x0
    return v0
.end method

.method public static restoreStatusBar(Lcom/gxxushang/tv/general/SPConstant$StatusBarState;)V
    .locals 2

    .line 425
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 426
    :cond_0
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    invoke-virtual {v0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/Window;->getDecorView()Landroid/view/View;

    move-result-object v0

    const/16 v1, 0x500

    .line 427
    invoke-virtual {v0, v1}, Landroid/view/View;->setSystemUiVisibility(I)V

    .line 429
    sget-object v0, Lcom/gxxushang/tv/helper/SPUtils$3;->$SwitchMap$com$gxxushang$tv$general$SPConstant$StatusBarState:[I

    invoke-virtual {p0}, Lcom/gxxushang/tv/general/SPConstant$StatusBarState;->ordinal()I

    move-result p0

    aget p0, v0, p0

    const/4 v0, 0x1

    if-eq p0, v0, :cond_2

    const/4 v0, 0x2

    if-eq p0, v0, :cond_1

    goto :goto_0

    .line 434
    :cond_1
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->lightStatusBar()V

    goto :goto_0

    .line 431
    :cond_2
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->darkStatusBar()V

    :goto_0
    return-void
.end method

.method public static rtl()Z
    .locals 1

    .line 341
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->lang()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    return v0

    :cond_0
    const/4 v0, 0x0

    return v0
.end method

.method public static screenHeightPx()I
    .locals 1

    .line 104
    invoke-static {}, Landroid/content/res/Resources;->getSystem()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    iget v0, v0, Landroid/util/DisplayMetrics;->heightPixels:I

    return v0
.end method

.method public static screenWidth()F
    .locals 1

    .line 101
    invoke-static {}, Landroid/content/res/Resources;->getSystem()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    iget v0, v0, Landroid/util/DisplayMetrics;->widthPixels:I

    int-to-float v0, v0

    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->px2dp(F)F

    move-result v0

    return v0
.end method

.method public static screenWidthPx()I
    .locals 1

    .line 98
    invoke-static {}, Landroid/content/res/Resources;->getSystem()Landroid/content/res/Resources;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v0

    iget v0, v0, Landroid/util/DisplayMetrics;->widthPixels:I

    return v0
.end method

.method public static setLinkClickable(Landroid/text/SpannableStringBuilder;Landroid/text/style/URLSpan;)V
    .locals 4

    .line 576
    invoke-virtual {p0, p1}, Landroid/text/SpannableStringBuilder;->getSpanStart(Ljava/lang/Object;)I

    move-result v0

    .line 577
    invoke-virtual {p0, p1}, Landroid/text/SpannableStringBuilder;->getSpanEnd(Ljava/lang/Object;)I

    move-result v1

    .line 578
    invoke-virtual {p0, p1}, Landroid/text/SpannableStringBuilder;->getSpanFlags(Ljava/lang/Object;)I

    move-result v2

    .line 579
    new-instance v3, Lcom/gxxushang/tv/helper/SPUtils$1;

    invoke-direct {v3, p1}, Lcom/gxxushang/tv/helper/SPUtils$1;-><init>(Landroid/text/style/URLSpan;)V

    .line 590
    invoke-virtual {p0, v3, v0, v1, v2}, Landroid/text/SpannableStringBuilder;->setSpan(Ljava/lang/Object;III)V

    return-void
.end method

.method public static setLocalData(Ljava/lang/String;I)V
    .locals 2

    .line 222
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 223
    :cond_0
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    invoke-interface {v0}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    .line 224
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string p1, ""

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p0, p1}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    .line 225
    invoke-interface {v0}, Landroid/content/SharedPreferences$Editor;->commit()Z

    return-void
.end method

.method public static setLocalData(Ljava/lang/String;Lcom/gxxushang/tv/model/SPBaseModel;)V
    .locals 1

    .line 208
    invoke-virtual {p1}, Lcom/gxxushang/tv/model/SPBaseModel;->toJson()Ljava/lang/String;

    move-result-object p1

    .line 209
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 210
    :cond_0
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    invoke-interface {v0}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    .line 211
    invoke-interface {v0, p0, p1}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    .line 212
    invoke-interface {v0}, Landroid/content/SharedPreferences$Editor;->commit()Z

    return-void
.end method

.method public static setLocalData(Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 216
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    .line 217
    :cond_0
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getSharedPreferences()Landroid/content/SharedPreferences;

    move-result-object v0

    invoke-interface {v0}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v0

    .line 218
    invoke-interface {v0, p0, p1}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    .line 219
    invoke-interface {v0}, Landroid/content/SharedPreferences$Editor;->commit()Z

    return-void
.end method

.method public static setStatusBarFullTransparent()V
    .locals 3

    .line 450
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/high16 v1, 0x4000000

    const/16 v2, 0x15

    if-lt v0, v2, :cond_0

    .line 451
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    invoke-virtual {v0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    move-result-object v0

    .line 452
    invoke-virtual {v0, v1}, Landroid/view/Window;->clearFlags(I)V

    .line 453
    invoke-virtual {v0}, Landroid/view/Window;->getDecorView()Landroid/view/View;

    move-result-object v1

    const/16 v2, 0x500

    invoke-virtual {v1, v2}, Landroid/view/View;->setSystemUiVisibility(I)V

    const/high16 v1, -0x80000000

    .line 455
    invoke-virtual {v0, v1}, Landroid/view/Window;->addFlags(I)V

    const/4 v1, 0x0

    .line 456
    invoke-virtual {v0, v1}, Landroid/view/Window;->setStatusBarColor(I)V

    goto :goto_0

    .line 457
    :cond_0
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x13

    if-lt v0, v2, :cond_1

    .line 458
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    invoke-virtual {v0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    move-result-object v0

    invoke-virtual {v0, v1}, Landroid/view/Window;->addFlags(I)V

    :cond_1
    :goto_0
    return-void
.end method

.method public static showError(I)V
    .locals 1

    .line 141
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 142
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p0}, Les/dmoral/toasty/Toasty;->error(Landroid/content/Context;I)Landroid/widget/Toast;

    move-result-object p0

    invoke-virtual {p0}, Landroid/widget/Toast;->show()V

    :cond_0
    return-void
.end method

.method public static showError(Ljava/lang/String;)V
    .locals 1

    .line 136
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 137
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p0}, Les/dmoral/toasty/Toasty;->error(Landroid/content/Context;Ljava/lang/CharSequence;)Landroid/widget/Toast;

    move-result-object p0

    invoke-virtual {p0}, Landroid/widget/Toast;->show()V

    :cond_0
    return-void
.end method

.method public static showInfo(I)V
    .locals 1

    .line 126
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 127
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p0}, Les/dmoral/toasty/Toasty;->info(Landroid/content/Context;I)Landroid/widget/Toast;

    move-result-object p0

    invoke-virtual {p0}, Landroid/widget/Toast;->show()V

    :cond_0
    return-void
.end method

.method public static showInfo(Ljava/lang/String;)V
    .locals 1

    .line 131
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 132
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p0}, Les/dmoral/toasty/Toasty;->info(Landroid/content/Context;Ljava/lang/CharSequence;)Landroid/widget/Toast;

    move-result-object p0

    invoke-virtual {p0}, Landroid/widget/Toast;->show()V

    :cond_0
    return-void
.end method

.method public static showSuccess(I)V
    .locals 1

    .line 119
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    if-eqz v0, :cond_0

    .line 120
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    invoke-static {v0, p0}, Les/dmoral/toasty/Toasty;->success(Landroid/content/Context;I)Landroid/widget/Toast;

    move-result-object p0

    invoke-virtual {p0}, Landroid/widget/Toast;->show()V

    :cond_0
    return-void
.end method

.method public static sp2px(F)I
    .locals 1

    const/high16 v0, 0x40000000    # 2.0f

    mul-float p0, p0, v0

    float-to-int p0, p0

    return p0
.end method

.method public static startActivity(Ljava/lang/Class;Ljava/util/HashMap;)V
    .locals 3

    .line 552
    new-instance v0, Landroid/content/Intent;

    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v1

    invoke-direct {v0, v1, p0}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    .line 553
    invoke-virtual {p1}, Ljava/util/HashMap;->keySet()Ljava/util/Set;

    move-result-object p0

    invoke-interface {p0}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    move-result-object p0

    :cond_0
    :goto_0
    invoke-interface {p0}, Ljava/util/Iterator;->hasNext()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-interface {p0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v1

    .line 554
    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    instance-of v2, v2, Ljava/lang/String;

    if-eqz v2, :cond_1

    .line 555
    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v2, v1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    goto :goto_0

    .line 556
    :cond_1
    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    instance-of v2, v2, Lcom/gxxushang/tv/model/SPBaseModel;

    if-eqz v2, :cond_0

    .line 557
    move-object v2, v1

    check-cast v2, Ljava/lang/String;

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Lcom/gxxushang/tv/model/SPBaseModel;

    invoke-virtual {v1}, Lcom/gxxushang/tv/model/SPBaseModel;->toJson()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v2, v1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    goto :goto_0

    .line 560
    :cond_2
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object p0

    invoke-virtual {p0, v0}, Landroid/app/Activity;->startActivity(Landroid/content/Intent;)V

    .line 561
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object p0

    const p1, 0x7f01001b

    const v0, 0x7f01001a

    invoke-virtual {p0, p1, v0}, Landroid/app/Activity;->overridePendingTransition(II)V

    return-void
.end method

.method public static storagePath(Ljava/lang/String;)Ljava/lang/String;
    .locals 3

    .line 695
    invoke-static {}, Lcom/gxxushang/tv/general/SPApplication;->app()Lcom/gxxushang/tv/general/SPApplication;

    move-result-object v0

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 698
    :try_start_0
    invoke-virtual {v0}, Lcom/gxxushang/tv/general/SPApplication;->getApplicationContext()Landroid/content/Context;

    move-result-object v2

    invoke-virtual {v2, v1}, Landroid/content/Context;->getExternalFilesDir(Ljava/lang/String;)Ljava/io/File;

    move-result-object v1
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v2

    .line 700
    invoke-virtual {v2}, Ljava/lang/Exception;->printStackTrace()V

    goto :goto_0

    .line 703
    :cond_0
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v2

    if-eqz v2, :cond_1

    .line 704
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v2

    invoke-virtual {v2, v1}, Landroid/app/Activity;->getExternalFilesDir(Ljava/lang/String;)Ljava/io/File;

    move-result-object v1

    :cond_1
    :goto_0
    if-nez v1, :cond_3

    if-eqz v0, :cond_2

    .line 710
    :try_start_1
    invoke-virtual {v0}, Lcom/gxxushang/tv/general/SPApplication;->getApplicationContext()Landroid/content/Context;

    move-result-object v0

    invoke-virtual {v0}, Landroid/content/Context;->getFilesDir()Ljava/io/File;

    move-result-object v1
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_1

    :catch_1
    move-exception v0

    .line 712
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    goto :goto_1

    .line 715
    :cond_2
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    if-eqz v0, :cond_3

    .line 716
    invoke-static {}, Lcom/blankj/utilcode/util/ActivityUtils;->getTopActivity()Landroid/app/Activity;

    move-result-object v0

    invoke-virtual {v0}, Landroid/app/Activity;->getFilesDir()Ljava/io/File;

    move-result-object v1

    :cond_3
    :goto_1
    if-nez v1, :cond_4

    const-string p0, ""

    return-object p0

    .line 723
    :cond_4
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1}, Ljava/io/File;->getPath()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "/"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    .line 724
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 725
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v1

    if-nez v1, :cond_5

    invoke-virtual {v0}, Ljava/io/File;->mkdirs()Z

    :cond_5
    return-object p0
.end method

.method public static stringForTime(I)Ljava/lang/String;
    .locals 7

    if-lez p0, :cond_2

    const v0, 0x5265c00

    if-lt p0, v0, :cond_0

    goto :goto_0

    .line 538
    :cond_0
    div-int/lit16 p0, p0, 0x3e8

    .line 539
    rem-int/lit8 v0, p0, 0x3c

    .line 540
    div-int/lit8 v1, p0, 0x3c

    rem-int/lit8 v1, v1, 0x3c

    .line 541
    div-int/lit16 p0, p0, 0xe10

    .line 542
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 543
    new-instance v3, Ljava/util/Formatter;

    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    move-result-object v4

    invoke-direct {v3, v2, v4}, Ljava/util/Formatter;-><init>(Ljava/lang/Appendable;Ljava/util/Locale;)V

    const/4 v2, 0x1

    const/4 v4, 0x0

    const/4 v5, 0x2

    if-lez p0, :cond_1

    const/4 v6, 0x3

    new-array v6, v6, [Ljava/lang/Object;

    .line 545
    invoke-static {p0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    aput-object p0, v6, v4

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    aput-object p0, v6, v2

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p0

    aput-object p0, v6, v5

    const-string p0, "%d:%02d:%02d"

    invoke-virtual {v3, p0, v6}, Ljava/util/Formatter;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/util/Formatter;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/Formatter;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_1
    new-array p0, v5, [Ljava/lang/Object;

    .line 547
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    aput-object v1, p0, v4

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    aput-object v0, p0, v2

    const-string v0, "%02d:%02d"

    invoke-virtual {v3, v0, p0}, Ljava/util/Formatter;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/util/Formatter;

    move-result-object p0

    invoke-virtual {p0}, Ljava/util/Formatter;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0

    :cond_2
    :goto_0
    const-string p0, "00:00"

    return-object p0
.end method

.method public static updateLocal(Landroid/app/Activity;)V
    .locals 5

    .line 379
    invoke-static {}, Lcom/gxxushang/tv/helper/SPUtils;->getContext()Landroid/content/Context;

    move-result-object v0

    if-nez v0, :cond_0

    return-void

    :cond_0
    const-string v0, "app_language"

    .line 380
    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->getLocalDataAsInt(Ljava/lang/String;)I

    move-result v0

    .line 381
    invoke-static {v0}, Lcom/gxxushang/tv/helper/SPUtils;->getLocal(I)Ljava/util/Locale;

    move-result-object v0

    .line 382
    invoke-virtual {p0}, Landroid/app/Activity;->getResources()Landroid/content/res/Resources;

    move-result-object v1

    invoke-virtual {v1}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v1

    .line 383
    invoke-virtual {p0}, Landroid/app/Activity;->getResources()Landroid/content/res/Resources;

    move-result-object v2

    invoke-virtual {v2}, Landroid/content/res/Resources;->getConfiguration()Landroid/content/res/Configuration;

    move-result-object v2

    .line 384
    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v4, 0x11

    if-lt v3, v4, :cond_1

    .line 385
    invoke-virtual {v2, v0}, Landroid/content/res/Configuration;->setLocale(Ljava/util/Locale;)V

    .line 387
    :cond_1
    invoke-virtual {p0}, Landroid/app/Activity;->getResources()Landroid/content/res/Resources;

    move-result-object p0

    invoke-virtual {p0, v2, v1}, Landroid/content/res/Resources;->updateConfiguration(Landroid/content/res/Configuration;Landroid/util/DisplayMetrics;)V

    return-void
.end method
