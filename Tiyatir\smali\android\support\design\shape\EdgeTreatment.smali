.class public Landroid/support/design/shape/EdgeTreatment;
.super Ljava/lang/Object;
.source "EdgeTreatment.java"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public getEdgePath(FFLandroid/support/design/shape/ShapePath;)V
    .locals 0

    const/4 p2, 0x0

    .line 43
    invoke-virtual {p3, p1, p2}, Landroid/support/design/shape/ShapePath;->lineTo(FF)V

    return-void
.end method
