.class public final Landroid/support/asynclayoutinflater/R$dimen;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/asynclayoutinflater/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "dimen"
.end annotation


# static fields
.field public static final compat_button_inset_horizontal_material:I = 0x7f06004f

.field public static final compat_button_inset_vertical_material:I = 0x7f060050

.field public static final compat_button_padding_horizontal_material:I = 0x7f060051

.field public static final compat_button_padding_vertical_material:I = 0x7f060052

.field public static final compat_control_corner_material:I = 0x7f060053

.field public static final compat_notification_large_icon_max_height:I = 0x7f060054

.field public static final compat_notification_large_icon_max_width:I = 0x7f060055

.field public static final notification_action_icon_size:I = 0x7f0600c3

.field public static final notification_action_text_size:I = 0x7f0600c4

.field public static final notification_big_circle_margin:I = 0x7f0600c5

.field public static final notification_content_margin_start:I = 0x7f0600c6

.field public static final notification_large_icon_height:I = 0x7f0600c7

.field public static final notification_large_icon_width:I = 0x7f0600c8

.field public static final notification_main_column_padding_top:I = 0x7f0600c9

.field public static final notification_media_narrow_margin:I = 0x7f0600ca

.field public static final notification_right_icon_size:I = 0x7f0600cb

.field public static final notification_right_side_padding_top:I = 0x7f0600cc

.field public static final notification_small_icon_background_padding:I = 0x7f0600cd

.field public static final notification_small_icon_size_as_large:I = 0x7f0600ce

.field public static final notification_subtext_size:I = 0x7f0600cf

.field public static final notification_top_pad:I = 0x7f0600d0

.field public static final notification_top_pad_large_text:I = 0x7f0600d1


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
