.class interface abstract Landroid/arch/lifecycle/FullLifecycleObserver;
.super Ljava/lang/Object;
.source "FullLifecycleObserver.java"

# interfaces
.implements Landroid/arch/lifecycle/LifecycleObserver;


# virtual methods
.method public abstract onCreate(Landroid/arch/lifecycle/LifecycleOwner;)V
.end method

.method public abstract onDestroy(Landroid/arch/lifecycle/LifecycleOwner;)V
.end method

.method public abstract onPause(Landroid/arch/lifecycle/LifecycleOwner;)V
.end method

.method public abstract onResume(Landroid/arch/lifecycle/LifecycleOwner;)V
.end method

.method public abstract onStart(Landroid/arch/lifecycle/LifecycleOwner;)V
.end method

.method public abstract onStop(Landroid/arch/lifecycle/LifecycleOwner;)V
.end method
