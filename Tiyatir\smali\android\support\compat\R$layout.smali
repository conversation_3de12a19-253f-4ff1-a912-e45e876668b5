.class public final Landroid/support/compat/R$layout;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/compat/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "layout"
.end annotation


# static fields
.field public static final notification_action:I = 0x7f0b0033

.field public static final notification_action_tombstone:I = 0x7f0b0034

.field public static final notification_template_custom_big:I = 0x7f0b003b

.field public static final notification_template_icon_group:I = 0x7f0b003c

.field public static final notification_template_part_chronometer:I = 0x7f0b0040

.field public static final notification_template_part_time:I = 0x7f0b0041


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
