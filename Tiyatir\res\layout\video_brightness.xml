<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/app_video_brightness_box" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:orientation="vertical" android:id="@id/content" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <ImageView android:id="@id/app_video_brightness_icon" android:layout_width="40.0dip" android:layout_height="40.0dip" android:layout_marginRight="15.0dip" android:src="@drawable/video_brightness_6_white_36dp" android:layout_alignParentRight="true" android:layout_centerHorizontal="true" />
        <TextView android:textSize="16.0dip" android:textColor="@android:color/white" android:gravity="center" android:id="@id/app_video_brightness" android:paddingTop="8.0dip" android:layout_width="70.0dip" android:layout_height="wrap_content" android:text="50%" android:layout_below="@id/app_video_brightness_icon" android:layout_alignParentRight="true" android:layout_centerHorizontal="true" />
    </RelativeLayout>
</LinearLayout>
