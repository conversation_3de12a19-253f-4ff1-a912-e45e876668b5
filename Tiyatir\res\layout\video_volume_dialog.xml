<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:gravity="center" android:layout_width="90.0dip" android:layout_height="160.0dip">
        <LinearLayout android:orientation="vertical" android:id="@id/content" android:background="@drawable/video_dialog_progress_bg" android:layout_width="40.0dip" android:layout_height="wrap_content">
            <ProgressBar android:layout_gravity="center_horizontal" android:id="@id/volume_progressbar" android:layout_width="4.0dip" android:layout_height="81.0dip" android:layout_marginTop="16.0dip" android:max="100" style="@style/video_vertical_progressBar" />
            <ImageView android:layout_gravity="center_horizontal" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginTop="16.0dip" android:layout_marginBottom="16.0dip" android:src="@drawable/video_volume_icon" />
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>
