<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="?qmui_bottom_sheet_grid_bg" android:paddingTop="?qmui_bottom_sheet_grid_padding_vertical" android:paddingBottom="?qmui_bottom_sheet_grid_padding_vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <HorizontalScrollView android:scrollbars="none" android:clipToPadding="false" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/bottom_sheet_first_linear_layout" android:paddingLeft="?qmui_bottom_sheet_grid_line_padding_horizontal" android:paddingRight="?qmui_bottom_sheet_grid_line_padding_horizontal" android:paddingBottom="?qmui_bottom_sheet_grid_line_vertical_space" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    </HorizontalScrollView>
    <HorizontalScrollView android:scrollbars="none" android:clipToPadding="false" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/bottom_sheet_second_linear_layout" android:paddingLeft="?qmui_bottom_sheet_grid_line_padding_horizontal" android:paddingRight="?qmui_bottom_sheet_grid_line_padding_horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    </HorizontalScrollView>
    <LinearLayout android:orientation="horizontal" android:id="@id/bottom_sheet_button_container" android:background="?qmui_bottom_sheet_button_background" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="?qmui_bottom_sheet_grid_padding_vertical">
        <com.qmuiteam.qmui.alpha.QMUIAlphaTextView android:textSize="?qmui_bottom_sheet_button_text_size" android:textColor="?qmui_bottom_sheet_button_text_color" android:gravity="center" android:id="@id/bottom_sheet_close_button" android:layout_width="fill_parent" android:layout_height="?qmui_bottom_sheet_button_height" android:text="关 闭" />
    </LinearLayout>
</LinearLayout>
