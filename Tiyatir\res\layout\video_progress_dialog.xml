<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:gravity="center" android:layout_width="152.0dip" android:layout_height="152.0dip">
        <LinearLayout android:orientation="vertical" android:id="@id/content" android:background="@drawable/video_dialog_progress_bg" android:layout_width="152.0dip" android:layout_height="wrap_content">
            <ImageView android:layout_gravity="center_horizontal" android:id="@id/duration_image_tip" android:layout_width="36.0dip" android:layout_height="27.0dip" android:layout_marginTop="16.0dip" />
            <LinearLayout android:gravity="center_horizontal" android:layout_gravity="center_horizontal" android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="20.0dip">
                <TextView android:textSize="14.0sp" android:textColor="@color/style_color" android:id="@id/tv_current" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                <TextView android:textSize="14.0sp" android:textColor="#ffffffff" android:id="@id/tv_duration" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            </LinearLayout>
            <ProgressBar android:layout_gravity="center_horizontal" android:id="@id/duration_progressbar" android:layout_width="fill_parent" android:layout_height="4.0dip" android:layout_marginLeft="16.0dip" android:layout_marginTop="8.0dip" android:layout_marginRight="16.0dip" android:layout_marginBottom="16.0dip" android:max="100" android:progressDrawable="@drawable/video_dialog_progress" style="@android:style/Widget.ProgressBar.Horizontal" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
