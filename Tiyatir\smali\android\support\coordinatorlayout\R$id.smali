.class public final Landroid/support/coordinatorlayout/R$id;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/coordinatorlayout/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "id"
.end annotation


# static fields
.field public static final action_container:I = 0x7f08000e

.field public static final action_divider:I = 0x7f080010

.field public static final action_image:I = 0x7f080011

.field public static final action_text:I = 0x7f080017

.field public static final actions:I = 0x7f080018

.field public static final async:I = 0x7f080028

.field public static final blocking:I = 0x7f08002e

.field public static final bottom:I = 0x7f08002f

.field public static final chronometer:I = 0x7f080043

.field public static final end:I = 0x7f080066

.field public static final forever:I = 0x7f08008d

.field public static final icon:I = 0x7f0800a4

.field public static final icon_group:I = 0x7f0800a5

.field public static final info:I = 0x7f0800ab

.field public static final italic:I = 0x7f0800ae

.field public static final left:I = 0x7f0800b5

.field public static final line1:I = 0x7f0800b7

.field public static final line3:I = 0x7f0800b8

.field public static final none:I = 0x7f0800cc

.field public static final normal:I = 0x7f0800cd

.field public static final notification_background:I = 0x7f0800ce

.field public static final notification_main_column:I = 0x7f0800cf

.field public static final notification_main_column_container:I = 0x7f0800d0

.field public static final right:I = 0x7f0800eb

.field public static final right_icon:I = 0x7f0800ec

.field public static final right_side:I = 0x7f0800ed

.field public static final start:I = 0x7f08011e

.field public static final tag_transition_group:I = 0x7f080128

.field public static final tag_unhandled_key_event_manager:I = 0x7f080129

.field public static final tag_unhandled_key_listeners:I = 0x7f08012a

.field public static final text:I = 0x7f08012b

.field public static final text2:I = 0x7f08012c

.field public static final time:I = 0x7f080136

.field public static final title:I = 0x7f080137

.field public static final top:I = 0x7f08013e


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
