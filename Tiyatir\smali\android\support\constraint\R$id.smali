.class public final Landroid/support/constraint/R$id;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/constraint/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "id"
.end annotation


# static fields
.field public static final bottom:I = 0x7f08002f

.field public static final end:I = 0x7f080066

.field public static final gone:I = 0x7f080091

.field public static final invisible:I = 0x7f0800ac

.field public static final left:I = 0x7f0800b5

.field public static final packed:I = 0x7f0800d6

.field public static final parent:I = 0x7f0800d8

.field public static final percent:I = 0x7f0800dc

.field public static final right:I = 0x7f0800eb

.field public static final spread:I = 0x7f080117

.field public static final spread_inside:I = 0x7f080118

.field public static final start:I = 0x7f08011e

.field public static final top:I = 0x7f08013e

.field public static final wrap:I = 0x7f08015e


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
