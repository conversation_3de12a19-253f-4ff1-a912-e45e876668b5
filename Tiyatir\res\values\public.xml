<?xml version="1.0" encoding="utf-8"?>
<resources>
    <public type="anim" name="abc_fade_in" id="0x7f010000" />
    <public type="anim" name="abc_fade_out" id="0x7f010001" />
    <public type="anim" name="abc_grow_fade_in_from_bottom" id="0x7f010002" />
    <public type="anim" name="abc_popup_enter" id="0x7f010003" />
    <public type="anim" name="abc_popup_exit" id="0x7f010004" />
    <public type="anim" name="abc_shrink_fade_out_from_bottom" id="0x7f010005" />
    <public type="anim" name="abc_slide_in_bottom" id="0x7f010006" />
    <public type="anim" name="abc_slide_in_top" id="0x7f010007" />
    <public type="anim" name="abc_slide_out_bottom" id="0x7f010008" />
    <public type="anim" name="abc_slide_out_top" id="0x7f010009" />
    <public type="anim" name="abc_tooltip_enter" id="0x7f01000a" />
    <public type="anim" name="abc_tooltip_exit" id="0x7f01000b" />
    <public type="anim" name="decelerate_factor_interpolator" id="0x7f01000c" />
    <public type="anim" name="decelerate_low_factor_interpolator" id="0x7f01000d" />
    <public type="anim" name="design_bottom_sheet_slide_in" id="0x7f01000e" />
    <public type="anim" name="design_bottom_sheet_slide_out" id="0x7f01000f" />
    <public type="anim" name="design_snackbar_in" id="0x7f010010" />
    <public type="anim" name="design_snackbar_out" id="0x7f010011" />
    <public type="anim" name="fade_in" id="0x7f010012" />
    <public type="anim" name="fade_out" id="0x7f010013" />
    <public type="anim" name="grow_from_bottom" id="0x7f010014" />
    <public type="anim" name="grow_from_bottomleft_to_topright" id="0x7f010015" />
    <public type="anim" name="grow_from_bottomright_to_topleft" id="0x7f010016" />
    <public type="anim" name="grow_from_top" id="0x7f010017" />
    <public type="anim" name="grow_from_topleft_to_bottomright" id="0x7f010018" />
    <public type="anim" name="grow_from_topright_to_bottomleft" id="0x7f010019" />
    <public type="anim" name="nothing" id="0x7f01001a" />
    <public type="anim" name="push_left_in" id="0x7f01001b" />
    <public type="anim" name="push_right_in" id="0x7f01001c" />
    <public type="anim" name="shrink_from_bottom" id="0x7f01001d" />
    <public type="anim" name="shrink_from_bottomleft_to_topright" id="0x7f01001e" />
    <public type="anim" name="shrink_from_bottomright_to_topleft" id="0x7f01001f" />
    <public type="anim" name="shrink_from_top" id="0x7f010020" />
    <public type="anim" name="shrink_from_topleft_to_bottomright" id="0x7f010021" />
    <public type="anim" name="shrink_from_topright_to_bottomleft" id="0x7f010022" />
    <public type="animator" name="design_appbar_state_list_animator" id="0x7f020000" />
    <public type="animator" name="design_fab_hide_motion_spec" id="0x7f020001" />
    <public type="animator" name="design_fab_show_motion_spec" id="0x7f020002" />
    <public type="animator" name="mtrl_btn_state_list_anim" id="0x7f020003" />
    <public type="animator" name="mtrl_btn_unelevated_state_list_anim" id="0x7f020004" />
    <public type="animator" name="mtrl_chip_state_list_anim" id="0x7f020005" />
    <public type="animator" name="mtrl_fab_hide_motion_spec" id="0x7f020006" />
    <public type="animator" name="mtrl_fab_show_motion_spec" id="0x7f020007" />
    <public type="animator" name="mtrl_fab_transformation_sheet_collapse_spec" id="0x7f020008" />
    <public type="animator" name="mtrl_fab_transformation_sheet_expand_spec" id="0x7f020009" />
    <public type="attr" name="QMUIButtonStyle" id="0x7f030000" />
    <public type="attr" name="QMUICommonListItemViewStyle" id="0x7f030001" />
    <public type="attr" name="QMUIGroupListSectionViewStyle" id="0x7f030002" />
    <public type="attr" name="QMUIGroupListViewStyle" id="0x7f030003" />
    <public type="attr" name="QMUILoadingStyle" id="0x7f030004" />
    <public type="attr" name="QMUIPullRefreshLayoutStyle" id="0x7f030005" />
    <public type="attr" name="QMUIQQFaceStyle" id="0x7f030006" />
    <public type="attr" name="QMUIRadiusImageViewStyle" id="0x7f030007" />
    <public type="attr" name="QMUITabSegmentStyle" id="0x7f030008" />
    <public type="attr" name="QMUITipNewStyle" id="0x7f030009" />
    <public type="attr" name="QMUITipPointStyle" id="0x7f03000a" />
    <public type="attr" name="QMUITopBarStyle" id="0x7f03000b" />
    <public type="attr" name="actionBarDivider" id="0x7f03000c" />
    <public type="attr" name="actionBarItemBackground" id="0x7f03000d" />
    <public type="attr" name="actionBarPopupTheme" id="0x7f03000e" />
    <public type="attr" name="actionBarSize" id="0x7f03000f" />
    <public type="attr" name="actionBarSplitStyle" id="0x7f030010" />
    <public type="attr" name="actionBarStyle" id="0x7f030011" />
    <public type="attr" name="actionBarTabBarStyle" id="0x7f030012" />
    <public type="attr" name="actionBarTabStyle" id="0x7f030013" />
    <public type="attr" name="actionBarTabTextStyle" id="0x7f030014" />
    <public type="attr" name="actionBarTheme" id="0x7f030015" />
    <public type="attr" name="actionBarWidgetTheme" id="0x7f030016" />
    <public type="attr" name="actionButtonStyle" id="0x7f030017" />
    <public type="attr" name="actionDropDownStyle" id="0x7f030018" />
    <public type="attr" name="actionLayout" id="0x7f030019" />
    <public type="attr" name="actionMenuTextAppearance" id="0x7f03001a" />
    <public type="attr" name="actionMenuTextColor" id="0x7f03001b" />
    <public type="attr" name="actionModeBackground" id="0x7f03001c" />
    <public type="attr" name="actionModeCloseButtonStyle" id="0x7f03001d" />
    <public type="attr" name="actionModeCloseDrawable" id="0x7f03001e" />
    <public type="attr" name="actionModeCopyDrawable" id="0x7f03001f" />
    <public type="attr" name="actionModeCutDrawable" id="0x7f030020" />
    <public type="attr" name="actionModeFindDrawable" id="0x7f030021" />
    <public type="attr" name="actionModePasteDrawable" id="0x7f030022" />
    <public type="attr" name="actionModePopupWindowStyle" id="0x7f030023" />
    <public type="attr" name="actionModeSelectAllDrawable" id="0x7f030024" />
    <public type="attr" name="actionModeShareDrawable" id="0x7f030025" />
    <public type="attr" name="actionModeSplitBackground" id="0x7f030026" />
    <public type="attr" name="actionModeStyle" id="0x7f030027" />
    <public type="attr" name="actionModeWebSearchDrawable" id="0x7f030028" />
    <public type="attr" name="actionOverflowButtonStyle" id="0x7f030029" />
    <public type="attr" name="actionOverflowMenuStyle" id="0x7f03002a" />
    <public type="attr" name="actionProviderClass" id="0x7f03002b" />
    <public type="attr" name="actionViewClass" id="0x7f03002c" />
    <public type="attr" name="activityChooserViewStyle" id="0x7f03002d" />
    <public type="attr" name="ad_marker_color" id="0x7f03002e" />
    <public type="attr" name="ad_marker_width" id="0x7f03002f" />
    <public type="attr" name="alertDialogButtonGroupStyle" id="0x7f030030" />
    <public type="attr" name="alertDialogCenterButtons" id="0x7f030031" />
    <public type="attr" name="alertDialogStyle" id="0x7f030032" />
    <public type="attr" name="alertDialogTheme" id="0x7f030033" />
    <public type="attr" name="allowStacking" id="0x7f030034" />
    <public type="attr" name="alpha" id="0x7f030035" />
    <public type="attr" name="alphabeticModifiers" id="0x7f030036" />
    <public type="attr" name="arrowHeadLength" id="0x7f030037" />
    <public type="attr" name="arrowShaftLength" id="0x7f030038" />
    <public type="attr" name="autoCompleteTextViewStyle" id="0x7f030039" />
    <public type="attr" name="autoSizeMaxTextSize" id="0x7f03003a" />
    <public type="attr" name="autoSizeMinTextSize" id="0x7f03003b" />
    <public type="attr" name="autoSizePresetSizes" id="0x7f03003c" />
    <public type="attr" name="autoSizeStepGranularity" id="0x7f03003d" />
    <public type="attr" name="autoSizeTextType" id="0x7f03003e" />
    <public type="attr" name="auto_show" id="0x7f03003f" />
    <public type="attr" name="background" id="0x7f030040" />
    <public type="attr" name="backgroundSplit" id="0x7f030041" />
    <public type="attr" name="backgroundStacked" id="0x7f030042" />
    <public type="attr" name="backgroundTint" id="0x7f030043" />
    <public type="attr" name="backgroundTintMode" id="0x7f030044" />
    <public type="attr" name="barLength" id="0x7f030045" />
    <public type="attr" name="bar_height" id="0x7f030046" />
    <public type="attr" name="barrierAllowsGoneWidgets" id="0x7f030047" />
    <public type="attr" name="barrierDirection" id="0x7f030048" />
    <public type="attr" name="behavior_autoHide" id="0x7f030049" />
    <public type="attr" name="behavior_fitToContents" id="0x7f03004a" />
    <public type="attr" name="behavior_hideable" id="0x7f03004b" />
    <public type="attr" name="behavior_overlapTop" id="0x7f03004c" />
    <public type="attr" name="behavior_peekHeight" id="0x7f03004d" />
    <public type="attr" name="behavior_skipCollapsed" id="0x7f03004e" />
    <public type="attr" name="borderWidth" id="0x7f03004f" />
    <public type="attr" name="borderlessButtonStyle" id="0x7f030050" />
    <public type="attr" name="bottomAppBarStyle" id="0x7f030051" />
    <public type="attr" name="bottomNavigationStyle" id="0x7f030052" />
    <public type="attr" name="bottomSheetDialogTheme" id="0x7f030053" />
    <public type="attr" name="bottomSheetStyle" id="0x7f030054" />
    <public type="attr" name="boxBackgroundColor" id="0x7f030055" />
    <public type="attr" name="boxBackgroundMode" id="0x7f030056" />
    <public type="attr" name="boxCollapsedPaddingTop" id="0x7f030057" />
    <public type="attr" name="boxCornerRadiusBottomEnd" id="0x7f030058" />
    <public type="attr" name="boxCornerRadiusBottomStart" id="0x7f030059" />
    <public type="attr" name="boxCornerRadiusTopEnd" id="0x7f03005a" />
    <public type="attr" name="boxCornerRadiusTopStart" id="0x7f03005b" />
    <public type="attr" name="boxStrokeColor" id="0x7f03005c" />
    <public type="attr" name="boxStrokeWidth" id="0x7f03005d" />
    <public type="attr" name="buffered_color" id="0x7f03005e" />
    <public type="attr" name="buttonBarButtonStyle" id="0x7f03005f" />
    <public type="attr" name="buttonBarNegativeButtonStyle" id="0x7f030060" />
    <public type="attr" name="buttonBarNeutralButtonStyle" id="0x7f030061" />
    <public type="attr" name="buttonBarPositiveButtonStyle" id="0x7f030062" />
    <public type="attr" name="buttonBarStyle" id="0x7f030063" />
    <public type="attr" name="buttonGravity" id="0x7f030064" />
    <public type="attr" name="buttonIconDimen" id="0x7f030065" />
    <public type="attr" name="buttonPanelSideLayout" id="0x7f030066" />
    <public type="attr" name="buttonStyle" id="0x7f030067" />
    <public type="attr" name="buttonStyleSmall" id="0x7f030068" />
    <public type="attr" name="buttonTint" id="0x7f030069" />
    <public type="attr" name="buttonTintMode" id="0x7f03006a" />
    <public type="attr" name="cardBackgroundColor" id="0x7f03006b" />
    <public type="attr" name="cardCornerRadius" id="0x7f03006c" />
    <public type="attr" name="cardElevation" id="0x7f03006d" />
    <public type="attr" name="cardMaxElevation" id="0x7f03006e" />
    <public type="attr" name="cardPreventCornerOverlap" id="0x7f03006f" />
    <public type="attr" name="cardUseCompatPadding" id="0x7f030070" />
    <public type="attr" name="cardViewStyle" id="0x7f030071" />
    <public type="attr" name="chainUseRtl" id="0x7f030072" />
    <public type="attr" name="checkboxStyle" id="0x7f030073" />
    <public type="attr" name="checkedChip" id="0x7f030074" />
    <public type="attr" name="checkedIcon" id="0x7f030075" />
    <public type="attr" name="checkedIconEnabled" id="0x7f030076" />
    <public type="attr" name="checkedIconVisible" id="0x7f030077" />
    <public type="attr" name="checkedTextViewStyle" id="0x7f030078" />
    <public type="attr" name="chipBackgroundColor" id="0x7f030079" />
    <public type="attr" name="chipCornerRadius" id="0x7f03007a" />
    <public type="attr" name="chipEndPadding" id="0x7f03007b" />
    <public type="attr" name="chipGroupStyle" id="0x7f03007c" />
    <public type="attr" name="chipIcon" id="0x7f03007d" />
    <public type="attr" name="chipIconEnabled" id="0x7f03007e" />
    <public type="attr" name="chipIconSize" id="0x7f03007f" />
    <public type="attr" name="chipIconTint" id="0x7f030080" />
    <public type="attr" name="chipIconVisible" id="0x7f030081" />
    <public type="attr" name="chipMinHeight" id="0x7f030082" />
    <public type="attr" name="chipSpacing" id="0x7f030083" />
    <public type="attr" name="chipSpacingHorizontal" id="0x7f030084" />
    <public type="attr" name="chipSpacingVertical" id="0x7f030085" />
    <public type="attr" name="chipStandaloneStyle" id="0x7f030086" />
    <public type="attr" name="chipStartPadding" id="0x7f030087" />
    <public type="attr" name="chipStrokeColor" id="0x7f030088" />
    <public type="attr" name="chipStrokeWidth" id="0x7f030089" />
    <public type="attr" name="chipStyle" id="0x7f03008a" />
    <public type="attr" name="closeIcon" id="0x7f03008b" />
    <public type="attr" name="closeIconEnabled" id="0x7f03008c" />
    <public type="attr" name="closeIconEndPadding" id="0x7f03008d" />
    <public type="attr" name="closeIconSize" id="0x7f03008e" />
    <public type="attr" name="closeIconStartPadding" id="0x7f03008f" />
    <public type="attr" name="closeIconTint" id="0x7f030090" />
    <public type="attr" name="closeIconVisible" id="0x7f030091" />
    <public type="attr" name="closeItemLayout" id="0x7f030092" />
    <public type="attr" name="collapseContentDescription" id="0x7f030093" />
    <public type="attr" name="collapseIcon" id="0x7f030094" />
    <public type="attr" name="collapsedTitleGravity" id="0x7f030095" />
    <public type="attr" name="collapsedTitleTextAppearance" id="0x7f030096" />
    <public type="attr" name="color" id="0x7f030097" />
    <public type="attr" name="colorAccent" id="0x7f030098" />
    <public type="attr" name="colorBackgroundFloating" id="0x7f030099" />
    <public type="attr" name="colorButtonNormal" id="0x7f03009a" />
    <public type="attr" name="colorControlActivated" id="0x7f03009b" />
    <public type="attr" name="colorControlHighlight" id="0x7f03009c" />
    <public type="attr" name="colorControlNormal" id="0x7f03009d" />
    <public type="attr" name="colorError" id="0x7f03009e" />
    <public type="attr" name="colorPrimary" id="0x7f03009f" />
    <public type="attr" name="colorPrimaryDark" id="0x7f0300a0" />
    <public type="attr" name="colorSecondary" id="0x7f0300a1" />
    <public type="attr" name="colorSwitchThumbNormal" id="0x7f0300a2" />
    <public type="attr" name="commitIcon" id="0x7f0300a3" />
    <public type="attr" name="constraintSet" id="0x7f0300a4" />
    <public type="attr" name="constraint_referenced_ids" id="0x7f0300a5" />
    <public type="attr" name="content" id="0x7f0300a6" />
    <public type="attr" name="contentDescription" id="0x7f0300a7" />
    <public type="attr" name="contentInsetEnd" id="0x7f0300a8" />
    <public type="attr" name="contentInsetEndWithActions" id="0x7f0300a9" />
    <public type="attr" name="contentInsetLeft" id="0x7f0300aa" />
    <public type="attr" name="contentInsetRight" id="0x7f0300ab" />
    <public type="attr" name="contentInsetStart" id="0x7f0300ac" />
    <public type="attr" name="contentInsetStartWithNavigation" id="0x7f0300ad" />
    <public type="attr" name="contentPadding" id="0x7f0300ae" />
    <public type="attr" name="contentPaddingBottom" id="0x7f0300af" />
    <public type="attr" name="contentPaddingLeft" id="0x7f0300b0" />
    <public type="attr" name="contentPaddingRight" id="0x7f0300b1" />
    <public type="attr" name="contentPaddingTop" id="0x7f0300b2" />
    <public type="attr" name="contentScrim" id="0x7f0300b3" />
    <public type="attr" name="controlBackground" id="0x7f0300b4" />
    <public type="attr" name="controller_layout_id" id="0x7f0300b5" />
    <public type="attr" name="coordinatorLayoutStyle" id="0x7f0300b6" />
    <public type="attr" name="cornerRadius" id="0x7f0300b7" />
    <public type="attr" name="counterEnabled" id="0x7f0300b8" />
    <public type="attr" name="counterMaxLength" id="0x7f0300b9" />
    <public type="attr" name="counterOverflowTextAppearance" id="0x7f0300ba" />
    <public type="attr" name="counterTextAppearance" id="0x7f0300bb" />
    <public type="attr" name="customNavigationLayout" id="0x7f0300bc" />
    <public type="attr" name="defaultQueryHint" id="0x7f0300bd" />
    <public type="attr" name="default_artwork" id="0x7f0300be" />
    <public type="attr" name="dialogCornerRadius" id="0x7f0300bf" />
    <public type="attr" name="dialogPreferredPadding" id="0x7f0300c0" />
    <public type="attr" name="dialogTheme" id="0x7f0300c1" />
    <public type="attr" name="disappearedScale" id="0x7f0300c2" />
    <public type="attr" name="displayOptions" id="0x7f0300c3" />
    <public type="attr" name="divider" id="0x7f0300c4" />
    <public type="attr" name="dividerHorizontal" id="0x7f0300c5" />
    <public type="attr" name="dividerPadding" id="0x7f0300c6" />
    <public type="attr" name="dividerVertical" id="0x7f0300c7" />
    <public type="attr" name="download_bg_line_color" id="0x7f0300c8" />
    <public type="attr" name="download_bg_line_width" id="0x7f0300c9" />
    <public type="attr" name="download_line_color" id="0x7f0300ca" />
    <public type="attr" name="download_line_width" id="0x7f0300cb" />
    <public type="attr" name="download_text_color" id="0x7f0300cc" />
    <public type="attr" name="download_text_size" id="0x7f0300cd" />
    <public type="attr" name="drawableSize" id="0x7f0300ce" />
    <public type="attr" name="drawerArrowStyle" id="0x7f0300cf" />
    <public type="attr" name="dropDownListViewStyle" id="0x7f0300d0" />
    <public type="attr" name="dropdownListPreferredItemHeight" id="0x7f0300d1" />
    <public type="attr" name="duration" id="0x7f0300d2" />
    <public type="attr" name="editTextBackground" id="0x7f0300d3" />
    <public type="attr" name="editTextColor" id="0x7f0300d4" />
    <public type="attr" name="editTextStyle" id="0x7f0300d5" />
    <public type="attr" name="elevation" id="0x7f0300d6" />
    <public type="attr" name="emptyVisibility" id="0x7f0300d7" />
    <public type="attr" name="enforceMaterialTheme" id="0x7f0300d8" />
    <public type="attr" name="enforceTextAppearance" id="0x7f0300d9" />
    <public type="attr" name="errorEnabled" id="0x7f0300da" />
    <public type="attr" name="errorTextAppearance" id="0x7f0300db" />
    <public type="attr" name="excludeClass" id="0x7f0300dc" />
    <public type="attr" name="excludeId" id="0x7f0300dd" />
    <public type="attr" name="excludeName" id="0x7f0300de" />
    <public type="attr" name="expandActivityOverflowButtonDrawable" id="0x7f0300df" />
    <public type="attr" name="expanded" id="0x7f0300e0" />
    <public type="attr" name="expandedTitleGravity" id="0x7f0300e1" />
    <public type="attr" name="expandedTitleMargin" id="0x7f0300e2" />
    <public type="attr" name="expandedTitleMarginBottom" id="0x7f0300e3" />
    <public type="attr" name="expandedTitleMarginEnd" id="0x7f0300e4" />
    <public type="attr" name="expandedTitleMarginStart" id="0x7f0300e5" />
    <public type="attr" name="expandedTitleMarginTop" id="0x7f0300e6" />
    <public type="attr" name="expandedTitleTextAppearance" id="0x7f0300e7" />
    <public type="attr" name="fabAlignmentMode" id="0x7f0300e8" />
    <public type="attr" name="fabCradleMargin" id="0x7f0300e9" />
    <public type="attr" name="fabCradleRoundedCornerRadius" id="0x7f0300ea" />
    <public type="attr" name="fabCradleVerticalOffset" id="0x7f0300eb" />
    <public type="attr" name="fabCustomSize" id="0x7f0300ec" />
    <public type="attr" name="fabSize" id="0x7f0300ed" />
    <public type="attr" name="fadingMode" id="0x7f0300ee" />
    <public type="attr" name="fastScrollEnabled" id="0x7f0300ef" />
    <public type="attr" name="fastScrollHorizontalThumbDrawable" id="0x7f0300f0" />
    <public type="attr" name="fastScrollHorizontalTrackDrawable" id="0x7f0300f1" />
    <public type="attr" name="fastScrollVerticalThumbDrawable" id="0x7f0300f2" />
    <public type="attr" name="fastScrollVerticalTrackDrawable" id="0x7f0300f3" />
    <public type="attr" name="fastforward_increment" id="0x7f0300f4" />
    <public type="attr" name="firstBaselineToTopHeight" id="0x7f0300f5" />
    <public type="attr" name="floatingActionButtonStyle" id="0x7f0300f6" />
    <public type="attr" name="font" id="0x7f0300f7" />
    <public type="attr" name="fontFamily" id="0x7f0300f8" />
    <public type="attr" name="fontProviderAuthority" id="0x7f0300f9" />
    <public type="attr" name="fontProviderCerts" id="0x7f0300fa" />
    <public type="attr" name="fontProviderFetchStrategy" id="0x7f0300fb" />
    <public type="attr" name="fontProviderFetchTimeout" id="0x7f0300fc" />
    <public type="attr" name="fontProviderPackage" id="0x7f0300fd" />
    <public type="attr" name="fontProviderQuery" id="0x7f0300fe" />
    <public type="attr" name="fontStyle" id="0x7f0300ff" />
    <public type="attr" name="fontVariationSettings" id="0x7f030100" />
    <public type="attr" name="fontWeight" id="0x7f030101" />
    <public type="attr" name="foregroundInsidePadding" id="0x7f030102" />
    <public type="attr" name="fromScene" id="0x7f030103" />
    <public type="attr" name="gapBetweenBars" id="0x7f030104" />
    <public type="attr" name="goIcon" id="0x7f030105" />
    <public type="attr" name="headerLayout" id="0x7f030106" />
    <public type="attr" name="height" id="0x7f030107" />
    <public type="attr" name="helperText" id="0x7f030108" />
    <public type="attr" name="helperTextEnabled" id="0x7f030109" />
    <public type="attr" name="helperTextTextAppearance" id="0x7f03010a" />
    <public type="attr" name="hideMotionSpec" id="0x7f03010b" />
    <public type="attr" name="hideOnContentScroll" id="0x7f03010c" />
    <public type="attr" name="hideOnScroll" id="0x7f03010d" />
    <public type="attr" name="hide_during_ads" id="0x7f03010e" />
    <public type="attr" name="hide_on_touch" id="0x7f03010f" />
    <public type="attr" name="hintAnimationEnabled" id="0x7f030110" />
    <public type="attr" name="hintEnabled" id="0x7f030111" />
    <public type="attr" name="hintTextAppearance" id="0x7f030112" />
    <public type="attr" name="homeAsUpIndicator" id="0x7f030113" />
    <public type="attr" name="homeLayout" id="0x7f030114" />
    <public type="attr" name="hoveredFocusedTranslationZ" id="0x7f030115" />
    <public type="attr" name="icon" id="0x7f030116" />
    <public type="attr" name="iconEndPadding" id="0x7f030117" />
    <public type="attr" name="iconGravity" id="0x7f030118" />
    <public type="attr" name="iconPadding" id="0x7f030119" />
    <public type="attr" name="iconSize" id="0x7f03011a" />
    <public type="attr" name="iconStartPadding" id="0x7f03011b" />
    <public type="attr" name="iconTint" id="0x7f03011c" />
    <public type="attr" name="iconTintMode" id="0x7f03011d" />
    <public type="attr" name="iconifiedByDefault" id="0x7f03011e" />
    <public type="attr" name="imageButtonStyle" id="0x7f03011f" />
    <public type="attr" name="indeterminateProgressStyle" id="0x7f030120" />
    <public type="attr" name="indicatorColor" id="0x7f030121" />
    <public type="attr" name="indicatorName" id="0x7f030122" />
    <public type="attr" name="initialActivityCount" id="0x7f030123" />
    <public type="attr" name="insetForeground" id="0x7f030124" />
    <public type="attr" name="interpolator" id="0x7f030125" />
    <public type="attr" name="isLightTheme" id="0x7f030126" />
    <public type="attr" name="isb_clear_default_padding" id="0x7f030127" />
    <public type="attr" name="isb_indicator_color" id="0x7f030128" />
    <public type="attr" name="isb_indicator_content_layout" id="0x7f030129" />
    <public type="attr" name="isb_indicator_text_color" id="0x7f03012a" />
    <public type="attr" name="isb_indicator_text_size" id="0x7f03012b" />
    <public type="attr" name="isb_indicator_top_content_layout" id="0x7f03012c" />
    <public type="attr" name="isb_max" id="0x7f03012d" />
    <public type="attr" name="isb_min" id="0x7f03012e" />
    <public type="attr" name="isb_only_thumb_draggable" id="0x7f03012f" />
    <public type="attr" name="isb_progress" id="0x7f030130" />
    <public type="attr" name="isb_progress_value_float" id="0x7f030131" />
    <public type="attr" name="isb_r2l" id="0x7f030132" />
    <public type="attr" name="isb_seek_smoothly" id="0x7f030133" />
    <public type="attr" name="isb_show_indicator" id="0x7f030134" />
    <public type="attr" name="isb_show_thumb_text" id="0x7f030135" />
    <public type="attr" name="isb_show_tick_marks_type" id="0x7f030136" />
    <public type="attr" name="isb_show_tick_texts" id="0x7f030137" />
    <public type="attr" name="isb_thumb_adjust_auto" id="0x7f030138" />
    <public type="attr" name="isb_thumb_color" id="0x7f030139" />
    <public type="attr" name="isb_thumb_drawable" id="0x7f03013a" />
    <public type="attr" name="isb_thumb_size" id="0x7f03013b" />
    <public type="attr" name="isb_thumb_text_color" id="0x7f03013c" />
    <public type="attr" name="isb_tick_marks_color" id="0x7f03013d" />
    <public type="attr" name="isb_tick_marks_drawable" id="0x7f03013e" />
    <public type="attr" name="isb_tick_marks_ends_hide" id="0x7f03013f" />
    <public type="attr" name="isb_tick_marks_size" id="0x7f030140" />
    <public type="attr" name="isb_tick_marks_swept_hide" id="0x7f030141" />
    <public type="attr" name="isb_tick_texts_array" id="0x7f030142" />
    <public type="attr" name="isb_tick_texts_color" id="0x7f030143" />
    <public type="attr" name="isb_tick_texts_size" id="0x7f030144" />
    <public type="attr" name="isb_tick_texts_typeface" id="0x7f030145" />
    <public type="attr" name="isb_ticks_count" id="0x7f030146" />
    <public type="attr" name="isb_track_background_color" id="0x7f030147" />
    <public type="attr" name="isb_track_background_size" id="0x7f030148" />
    <public type="attr" name="isb_track_progress_color" id="0x7f030149" />
    <public type="attr" name="isb_track_progress_size" id="0x7f03014a" />
    <public type="attr" name="isb_track_rounded_corners" id="0x7f03014b" />
    <public type="attr" name="isb_user_seekable" id="0x7f03014c" />
    <public type="attr" name="itemBackground" id="0x7f03014d" />
    <public type="attr" name="itemHorizontalPadding" id="0x7f03014e" />
    <public type="attr" name="itemHorizontalTranslationEnabled" id="0x7f03014f" />
    <public type="attr" name="itemIconPadding" id="0x7f030150" />
    <public type="attr" name="itemIconSize" id="0x7f030151" />
    <public type="attr" name="itemIconTint" id="0x7f030152" />
    <public type="attr" name="itemPadding" id="0x7f030153" />
    <public type="attr" name="itemSpacing" id="0x7f030154" />
    <public type="attr" name="itemTextAppearance" id="0x7f030155" />
    <public type="attr" name="itemTextAppearanceActive" id="0x7f030156" />
    <public type="attr" name="itemTextAppearanceInactive" id="0x7f030157" />
    <public type="attr" name="itemTextColor" id="0x7f030158" />
    <public type="attr" name="keep_content_on_player_reset" id="0x7f030159" />
    <public type="attr" name="keylines" id="0x7f03015a" />
    <public type="attr" name="labelVisibilityMode" id="0x7f03015b" />
    <public type="attr" name="lastBaselineToBottomHeight" id="0x7f03015c" />
    <public type="attr" name="layout" id="0x7f03015d" />
    <public type="attr" name="layoutManager" id="0x7f03015e" />
    <public type="attr" name="layout_anchor" id="0x7f03015f" />
    <public type="attr" name="layout_anchorGravity" id="0x7f030160" />
    <public type="attr" name="layout_behavior" id="0x7f030161" />
    <public type="attr" name="layout_collapseMode" id="0x7f030162" />
    <public type="attr" name="layout_collapseParallaxMultiplier" id="0x7f030163" />
    <public type="attr" name="layout_constrainedHeight" id="0x7f030164" />
    <public type="attr" name="layout_constrainedWidth" id="0x7f030165" />
    <public type="attr" name="layout_constraintBaseline_creator" id="0x7f030166" />
    <public type="attr" name="layout_constraintBaseline_toBaselineOf" id="0x7f030167" />
    <public type="attr" name="layout_constraintBottom_creator" id="0x7f030168" />
    <public type="attr" name="layout_constraintBottom_toBottomOf" id="0x7f030169" />
    <public type="attr" name="layout_constraintBottom_toTopOf" id="0x7f03016a" />
    <public type="attr" name="layout_constraintCircle" id="0x7f03016b" />
    <public type="attr" name="layout_constraintCircleAngle" id="0x7f03016c" />
    <public type="attr" name="layout_constraintCircleRadius" id="0x7f03016d" />
    <public type="attr" name="layout_constraintDimensionRatio" id="0x7f03016e" />
    <public type="attr" name="layout_constraintEnd_toEndOf" id="0x7f03016f" />
    <public type="attr" name="layout_constraintEnd_toStartOf" id="0x7f030170" />
    <public type="attr" name="layout_constraintGuide_begin" id="0x7f030171" />
    <public type="attr" name="layout_constraintGuide_end" id="0x7f030172" />
    <public type="attr" name="layout_constraintGuide_percent" id="0x7f030173" />
    <public type="attr" name="layout_constraintHeight_default" id="0x7f030174" />
    <public type="attr" name="layout_constraintHeight_max" id="0x7f030175" />
    <public type="attr" name="layout_constraintHeight_min" id="0x7f030176" />
    <public type="attr" name="layout_constraintHeight_percent" id="0x7f030177" />
    <public type="attr" name="layout_constraintHorizontal_bias" id="0x7f030178" />
    <public type="attr" name="layout_constraintHorizontal_chainStyle" id="0x7f030179" />
    <public type="attr" name="layout_constraintHorizontal_weight" id="0x7f03017a" />
    <public type="attr" name="layout_constraintLeft_creator" id="0x7f03017b" />
    <public type="attr" name="layout_constraintLeft_toLeftOf" id="0x7f03017c" />
    <public type="attr" name="layout_constraintLeft_toRightOf" id="0x7f03017d" />
    <public type="attr" name="layout_constraintRight_creator" id="0x7f03017e" />
    <public type="attr" name="layout_constraintRight_toLeftOf" id="0x7f03017f" />
    <public type="attr" name="layout_constraintRight_toRightOf" id="0x7f030180" />
    <public type="attr" name="layout_constraintStart_toEndOf" id="0x7f030181" />
    <public type="attr" name="layout_constraintStart_toStartOf" id="0x7f030182" />
    <public type="attr" name="layout_constraintTop_creator" id="0x7f030183" />
    <public type="attr" name="layout_constraintTop_toBottomOf" id="0x7f030184" />
    <public type="attr" name="layout_constraintTop_toTopOf" id="0x7f030185" />
    <public type="attr" name="layout_constraintVertical_bias" id="0x7f030186" />
    <public type="attr" name="layout_constraintVertical_chainStyle" id="0x7f030187" />
    <public type="attr" name="layout_constraintVertical_weight" id="0x7f030188" />
    <public type="attr" name="layout_constraintWidth_default" id="0x7f030189" />
    <public type="attr" name="layout_constraintWidth_max" id="0x7f03018a" />
    <public type="attr" name="layout_constraintWidth_min" id="0x7f03018b" />
    <public type="attr" name="layout_constraintWidth_percent" id="0x7f03018c" />
    <public type="attr" name="layout_dodgeInsetEdges" id="0x7f03018d" />
    <public type="attr" name="layout_editor_absoluteX" id="0x7f03018e" />
    <public type="attr" name="layout_editor_absoluteY" id="0x7f03018f" />
    <public type="attr" name="layout_goneMarginBottom" id="0x7f030190" />
    <public type="attr" name="layout_goneMarginEnd" id="0x7f030191" />
    <public type="attr" name="layout_goneMarginLeft" id="0x7f030192" />
    <public type="attr" name="layout_goneMarginRight" id="0x7f030193" />
    <public type="attr" name="layout_goneMarginStart" id="0x7f030194" />
    <public type="attr" name="layout_goneMarginTop" id="0x7f030195" />
    <public type="attr" name="layout_insetEdge" id="0x7f030196" />
    <public type="attr" name="layout_keyline" id="0x7f030197" />
    <public type="attr" name="layout_optimizationLevel" id="0x7f030198" />
    <public type="attr" name="layout_scrollFlags" id="0x7f030199" />
    <public type="attr" name="layout_scrollInterpolator" id="0x7f03019a" />
    <public type="attr" name="liftOnScroll" id="0x7f03019b" />
    <public type="attr" name="lineHeight" id="0x7f03019c" />
    <public type="attr" name="lineSpacing" id="0x7f03019d" />
    <public type="attr" name="listChoiceBackgroundIndicator" id="0x7f03019e" />
    <public type="attr" name="listDividerAlertDialog" id="0x7f03019f" />
    <public type="attr" name="listItemLayout" id="0x7f0301a0" />
    <public type="attr" name="listLayout" id="0x7f0301a1" />
    <public type="attr" name="listMenuViewStyle" id="0x7f0301a2" />
    <public type="attr" name="listPopupWindowStyle" id="0x7f0301a3" />
    <public type="attr" name="listPreferredItemHeight" id="0x7f0301a4" />
    <public type="attr" name="listPreferredItemHeightLarge" id="0x7f0301a5" />
    <public type="attr" name="listPreferredItemHeightSmall" id="0x7f0301a6" />
    <public type="attr" name="listPreferredItemPaddingLeft" id="0x7f0301a7" />
    <public type="attr" name="listPreferredItemPaddingRight" id="0x7f0301a8" />
    <public type="attr" name="logo" id="0x7f0301a9" />
    <public type="attr" name="logoDescription" id="0x7f0301aa" />
    <public type="attr" name="matchOrder" id="0x7f0301ab" />
    <public type="attr" name="materialButtonStyle" id="0x7f0301ac" />
    <public type="attr" name="materialCardViewStyle" id="0x7f0301ad" />
    <public type="attr" name="maxActionInlineWidth" id="0x7f0301ae" />
    <public type="attr" name="maxButtonHeight" id="0x7f0301af" />
    <public type="attr" name="maxHeight" id="0x7f0301b0" />
    <public type="attr" name="maxImageSize" id="0x7f0301b1" />
    <public type="attr" name="maxWidth" id="0x7f0301b2" />
    <public type="attr" name="maximumAngle" id="0x7f0301b3" />
    <public type="attr" name="measureWithLargestChild" id="0x7f0301b4" />
    <public type="attr" name="menu" id="0x7f0301b5" />
    <public type="attr" name="minHeight" id="0x7f0301b6" />
    <public type="attr" name="minWidth" id="0x7f0301b7" />
    <public type="attr" name="minimumHorizontalAngle" id="0x7f0301b8" />
    <public type="attr" name="minimumVerticalAngle" id="0x7f0301b9" />
    <public type="attr" name="multiChoiceItemLayout" id="0x7f0301ba" />
    <public type="attr" name="navigationContentDescription" id="0x7f0301bb" />
    <public type="attr" name="navigationIcon" id="0x7f0301bc" />
    <public type="attr" name="navigationMode" id="0x7f0301bd" />
    <public type="attr" name="navigationViewStyle" id="0x7f0301be" />
    <public type="attr" name="numericModifiers" id="0x7f0301bf" />
    <public type="attr" name="overlapAnchor" id="0x7f0301c0" />
    <public type="attr" name="paddingBottomNoButtons" id="0x7f0301c1" />
    <public type="attr" name="paddingEnd" id="0x7f0301c2" />
    <public type="attr" name="paddingStart" id="0x7f0301c3" />
    <public type="attr" name="paddingTopNoTitle" id="0x7f0301c4" />
    <public type="attr" name="panelBackground" id="0x7f0301c5" />
    <public type="attr" name="panelMenuListTheme" id="0x7f0301c6" />
    <public type="attr" name="panelMenuListWidth" id="0x7f0301c7" />
    <public type="attr" name="passwordToggleContentDescription" id="0x7f0301c8" />
    <public type="attr" name="passwordToggleDrawable" id="0x7f0301c9" />
    <public type="attr" name="passwordToggleEnabled" id="0x7f0301ca" />
    <public type="attr" name="passwordToggleTint" id="0x7f0301cb" />
    <public type="attr" name="passwordToggleTintMode" id="0x7f0301cc" />
    <public type="attr" name="patternPathData" id="0x7f0301cd" />
    <public type="attr" name="play_bg_line_color" id="0x7f0301ce" />
    <public type="attr" name="play_bg_line_width" id="0x7f0301cf" />
    <public type="attr" name="play_line_color" id="0x7f0301d0" />
    <public type="attr" name="play_line_width" id="0x7f0301d1" />
    <public type="attr" name="played_ad_marker_color" id="0x7f0301d2" />
    <public type="attr" name="played_color" id="0x7f0301d3" />
    <public type="attr" name="player_layout_id" id="0x7f0301d4" />
    <public type="attr" name="popupMenuStyle" id="0x7f0301d5" />
    <public type="attr" name="popupTheme" id="0x7f0301d6" />
    <public type="attr" name="popupWindowStyle" id="0x7f0301d7" />
    <public type="attr" name="preserveIconSpacing" id="0x7f0301d8" />
    <public type="attr" name="pressedTranslationZ" id="0x7f0301d9" />
    <public type="attr" name="progressBarPadding" id="0x7f0301da" />
    <public type="attr" name="progressBarStyle" id="0x7f0301db" />
    <public type="attr" name="qmui_accessory_type" id="0x7f0301dc" />
    <public type="attr" name="qmui_alpha_disabled" id="0x7f0301dd" />
    <public type="attr" name="qmui_alpha_pressed" id="0x7f0301de" />
    <public type="attr" name="qmui_auto_calculate_refresh_end_offset" id="0x7f0301df" />
    <public type="attr" name="qmui_auto_calculate_refresh_init_offset" id="0x7f0301e0" />
    <public type="attr" name="qmui_backgroundColor" id="0x7f0301e1" />
    <public type="attr" name="qmui_background_color" id="0x7f0301e2" />
    <public type="attr" name="qmui_borderColor" id="0x7f0301e3" />
    <public type="attr" name="qmui_borderWidth" id="0x7f0301e4" />
    <public type="attr" name="qmui_border_color" id="0x7f0301e5" />
    <public type="attr" name="qmui_border_width" id="0x7f0301e6" />
    <public type="attr" name="qmui_bottomDividerColor" id="0x7f0301e7" />
    <public type="attr" name="qmui_bottomDividerHeight" id="0x7f0301e8" />
    <public type="attr" name="qmui_bottomDividerInsetLeft" id="0x7f0301e9" />
    <public type="attr" name="qmui_bottomDividerInsetRight" id="0x7f0301ea" />
    <public type="attr" name="qmui_bottom_sheet_button_background" id="0x7f0301eb" />
    <public type="attr" name="qmui_bottom_sheet_button_height" id="0x7f0301ec" />
    <public type="attr" name="qmui_bottom_sheet_button_text_color" id="0x7f0301ed" />
    <public type="attr" name="qmui_bottom_sheet_button_text_size" id="0x7f0301ee" />
    <public type="attr" name="qmui_bottom_sheet_grid_bg" id="0x7f0301ef" />
    <public type="attr" name="qmui_bottom_sheet_grid_item_icon_marginBottom" id="0x7f0301f0" />
    <public type="attr" name="qmui_bottom_sheet_grid_item_icon_marginTop" id="0x7f0301f1" />
    <public type="attr" name="qmui_bottom_sheet_grid_item_icon_size" id="0x7f0301f2" />
    <public type="attr" name="qmui_bottom_sheet_grid_item_mini_width" id="0x7f0301f3" />
    <public type="attr" name="qmui_bottom_sheet_grid_item_paddingBottom" id="0x7f0301f4" />
    <public type="attr" name="qmui_bottom_sheet_grid_item_paddingTop" id="0x7f0301f5" />
    <public type="attr" name="qmui_bottom_sheet_grid_item_text_appearance" id="0x7f0301f6" />
    <public type="attr" name="qmui_bottom_sheet_grid_line_padding_horizontal" id="0x7f0301f7" />
    <public type="attr" name="qmui_bottom_sheet_grid_line_vertical_space" id="0x7f0301f8" />
    <public type="attr" name="qmui_bottom_sheet_grid_padding_vertical" id="0x7f0301f9" />
    <public type="attr" name="qmui_bottom_sheet_list_item_bg" id="0x7f0301fa" />
    <public type="attr" name="qmui_bottom_sheet_list_item_height" id="0x7f0301fb" />
    <public type="attr" name="qmui_bottom_sheet_list_item_icon_margin_right" id="0x7f0301fc" />
    <public type="attr" name="qmui_bottom_sheet_list_item_icon_size" id="0x7f0301fd" />
    <public type="attr" name="qmui_bottom_sheet_list_item_mark_margin_left" id="0x7f0301fe" />
    <public type="attr" name="qmui_bottom_sheet_list_item_padding_horizontal" id="0x7f0301ff" />
    <public type="attr" name="qmui_bottom_sheet_list_item_text_appearance" id="0x7f030200" />
    <public type="attr" name="qmui_bottom_sheet_list_item_tip_point_margin_left" id="0x7f030201" />
    <public type="attr" name="qmui_bottom_sheet_title_appearance" id="0x7f030202" />
    <public type="attr" name="qmui_bottom_sheet_title_bg" id="0x7f030203" />
    <public type="attr" name="qmui_bottom_sheet_title_height" id="0x7f030204" />
    <public type="attr" name="qmui_btn_text" id="0x7f030205" />
    <public type="attr" name="qmui_childHorizontalSpacing" id="0x7f030206" />
    <public type="attr" name="qmui_childVerticalSpacing" id="0x7f030207" />
    <public type="attr" name="qmui_collapsedTitleGravity" id="0x7f030208" />
    <public type="attr" name="qmui_collapsedTitleTextAppearance" id="0x7f030209" />
    <public type="attr" name="qmui_commonList_detailColor" id="0x7f03020a" />
    <public type="attr" name="qmui_commonList_titleColor" id="0x7f03020b" />
    <public type="attr" name="qmui_common_list_item_accessory_margin_left" id="0x7f03020c" />
    <public type="attr" name="qmui_common_list_item_chevron" id="0x7f03020d" />
    <public type="attr" name="qmui_common_list_item_detail_h_text_size" id="0x7f03020e" />
    <public type="attr" name="qmui_common_list_item_detail_line_space" id="0x7f03020f" />
    <public type="attr" name="qmui_common_list_item_detail_v_text_size" id="0x7f030210" />
    <public type="attr" name="qmui_common_list_item_h_space_min_width" id="0x7f030211" />
    <public type="attr" name="qmui_common_list_item_icon_margin_right" id="0x7f030212" />
    <public type="attr" name="qmui_common_list_item_switch" id="0x7f030213" />
    <public type="attr" name="qmui_common_list_item_title_h_text_size" id="0x7f030214" />
    <public type="attr" name="qmui_common_list_item_title_v_text_size" id="0x7f030215" />
    <public type="attr" name="qmui_config_color_background" id="0x7f030216" />
    <public type="attr" name="qmui_config_color_background_pressed" id="0x7f030217" />
    <public type="attr" name="qmui_config_color_black" id="0x7f030218" />
    <public type="attr" name="qmui_config_color_blue" id="0x7f030219" />
    <public type="attr" name="qmui_config_color_gray_1" id="0x7f03021a" />
    <public type="attr" name="qmui_config_color_gray_2" id="0x7f03021b" />
    <public type="attr" name="qmui_config_color_gray_3" id="0x7f03021c" />
    <public type="attr" name="qmui_config_color_gray_4" id="0x7f03021d" />
    <public type="attr" name="qmui_config_color_gray_5" id="0x7f03021e" />
    <public type="attr" name="qmui_config_color_gray_6" id="0x7f03021f" />
    <public type="attr" name="qmui_config_color_gray_7" id="0x7f030220" />
    <public type="attr" name="qmui_config_color_gray_8" id="0x7f030221" />
    <public type="attr" name="qmui_config_color_gray_9" id="0x7f030222" />
    <public type="attr" name="qmui_config_color_link" id="0x7f030223" />
    <public type="attr" name="qmui_config_color_pressed" id="0x7f030224" />
    <public type="attr" name="qmui_config_color_red" id="0x7f030225" />
    <public type="attr" name="qmui_config_color_separator" id="0x7f030226" />
    <public type="attr" name="qmui_config_color_separator_darken" id="0x7f030227" />
    <public type="attr" name="qmui_contentScrim" id="0x7f030228" />
    <public type="attr" name="qmui_content_padding_horizontal" id="0x7f030229" />
    <public type="attr" name="qmui_content_spacing_horizontal" id="0x7f03022a" />
    <public type="attr" name="qmui_corner_radius" id="0x7f03022b" />
    <public type="attr" name="qmui_detail_text" id="0x7f03022c" />
    <public type="attr" name="qmui_dialog_action_button_padding_horizontal" id="0x7f03022d" />
    <public type="attr" name="qmui_dialog_action_container_custom_space_index" id="0x7f03022e" />
    <public type="attr" name="qmui_dialog_action_container_justify_content" id="0x7f03022f" />
    <public type="attr" name="qmui_dialog_action_container_style" id="0x7f030230" />
    <public type="attr" name="qmui_dialog_action_height" id="0x7f030231" />
    <public type="attr" name="qmui_dialog_action_icon_space" id="0x7f030232" />
    <public type="attr" name="qmui_dialog_action_space" id="0x7f030233" />
    <public type="attr" name="qmui_dialog_action_style" id="0x7f030234" />
    <public type="attr" name="qmui_dialog_background_dim_amount" id="0x7f030235" />
    <public type="attr" name="qmui_dialog_bg" id="0x7f030236" />
    <public type="attr" name="qmui_dialog_edit_content_style" id="0x7f030237" />
    <public type="attr" name="qmui_dialog_margin_vertical" id="0x7f030238" />
    <public type="attr" name="qmui_dialog_max_width" id="0x7f030239" />
    <public type="attr" name="qmui_dialog_menu_container_padding_bottom_when_action_exist" id="0x7f03023a" />
    <public type="attr" name="qmui_dialog_menu_container_padding_top_when_title_exist" id="0x7f03023b" />
    <public type="attr" name="qmui_dialog_menu_container_single_padding_vertical" id="0x7f03023c" />
    <public type="attr" name="qmui_dialog_menu_container_style" id="0x7f03023d" />
    <public type="attr" name="qmui_dialog_menu_item_check_drawable" id="0x7f03023e" />
    <public type="attr" name="qmui_dialog_menu_item_check_mark_margin_hor" id="0x7f03023f" />
    <public type="attr" name="qmui_dialog_menu_item_height" id="0x7f030240" />
    <public type="attr" name="qmui_dialog_menu_item_mark_drawable" id="0x7f030241" />
    <public type="attr" name="qmui_dialog_menu_item_style" id="0x7f030242" />
    <public type="attr" name="qmui_dialog_message_content_style" id="0x7f030243" />
    <public type="attr" name="qmui_dialog_min_width" id="0x7f030244" />
    <public type="attr" name="qmui_dialog_negative_action_text_color" id="0x7f030245" />
    <public type="attr" name="qmui_dialog_padding_horizontal" id="0x7f030246" />
    <public type="attr" name="qmui_dialog_positive_action_text_color" id="0x7f030247" />
    <public type="attr" name="qmui_dialog_radius" id="0x7f030248" />
    <public type="attr" name="qmui_dialog_title_style" id="0x7f030249" />
    <public type="attr" name="qmui_dialog_wrapper_style" id="0x7f03024a" />
    <public type="attr" name="qmui_equal_target_refresh_offset_to_refresh_view_height" id="0x7f03024b" />
    <public type="attr" name="qmui_expandedTitleGravity" id="0x7f03024c" />
    <public type="attr" name="qmui_expandedTitleMargin" id="0x7f03024d" />
    <public type="attr" name="qmui_expandedTitleMarginBottom" id="0x7f03024e" />
    <public type="attr" name="qmui_expandedTitleMarginEnd" id="0x7f03024f" />
    <public type="attr" name="qmui_expandedTitleMarginStart" id="0x7f030250" />
    <public type="attr" name="qmui_expandedTitleMarginTop" id="0x7f030251" />
    <public type="attr" name="qmui_expandedTitleTextAppearance" id="0x7f030252" />
    <public type="attr" name="qmui_general_shadow_alpha" id="0x7f030253" />
    <public type="attr" name="qmui_general_shadow_elevation" id="0x7f030254" />
    <public type="attr" name="qmui_hideRadiusSide" id="0x7f030255" />
    <public type="attr" name="qmui_icon_check_mark" id="0x7f030256" />
    <public type="attr" name="qmui_isRadiusAdjustBounds" id="0x7f030257" />
    <public type="attr" name="qmui_is_circle" id="0x7f030258" />
    <public type="attr" name="qmui_is_oval" id="0x7f030259" />
    <public type="attr" name="qmui_is_touch_select_mode_enabled" id="0x7f03025a" />
    <public type="attr" name="qmui_layout_collapseMode" id="0x7f03025b" />
    <public type="attr" name="qmui_layout_collapseParallaxMultiplier" id="0x7f03025c" />
    <public type="attr" name="qmui_layout_miniContentProtectionSize" id="0x7f03025d" />
    <public type="attr" name="qmui_layout_priority" id="0x7f03025e" />
    <public type="attr" name="qmui_leftDividerColor" id="0x7f03025f" />
    <public type="attr" name="qmui_leftDividerInsetBottom" id="0x7f030260" />
    <public type="attr" name="qmui_leftDividerInsetTop" id="0x7f030261" />
    <public type="attr" name="qmui_leftDividerWidth" id="0x7f030262" />
    <public type="attr" name="qmui_linkBackgroundColor" id="0x7f030263" />
    <public type="attr" name="qmui_linkColor" id="0x7f030264" />
    <public type="attr" name="qmui_linkTextColor" id="0x7f030265" />
    <public type="attr" name="qmui_list_item_bg_with_border_bottom" id="0x7f030266" />
    <public type="attr" name="qmui_list_item_bg_with_border_bottom_inset_left" id="0x7f030267" />
    <public type="attr" name="qmui_list_item_bg_with_border_bottom_inset_left_pressed" id="0x7f030268" />
    <public type="attr" name="qmui_list_item_bg_with_border_bottom_pressed" id="0x7f030269" />
    <public type="attr" name="qmui_list_item_bg_with_border_double" id="0x7f03026a" />
    <public type="attr" name="qmui_list_item_bg_with_border_double_pressed" id="0x7f03026b" />
    <public type="attr" name="qmui_list_item_bg_with_border_top" id="0x7f03026c" />
    <public type="attr" name="qmui_list_item_bg_with_border_top_inset_left" id="0x7f03026d" />
    <public type="attr" name="qmui_list_item_bg_with_border_top_inset_left_pressed" id="0x7f03026e" />
    <public type="attr" name="qmui_list_item_bg_with_border_top_pressed" id="0x7f03026f" />
    <public type="attr" name="qmui_list_item_height" id="0x7f030270" />
    <public type="attr" name="qmui_list_item_height_higher" id="0x7f030271" />
    <public type="attr" name="qmui_loading_color" id="0x7f030272" />
    <public type="attr" name="qmui_loading_size" id="0x7f030273" />
    <public type="attr" name="qmui_loading_view_size" id="0x7f030274" />
    <public type="attr" name="qmui_maxNumber" id="0x7f030275" />
    <public type="attr" name="qmui_maxTextSize" id="0x7f030276" />
    <public type="attr" name="qmui_max_value" id="0x7f030277" />
    <public type="attr" name="qmui_minTextSize" id="0x7f030278" />
    <public type="attr" name="qmui_more_action_color" id="0x7f030279" />
    <public type="attr" name="qmui_more_action_text" id="0x7f03027a" />
    <public type="attr" name="qmui_orientation" id="0x7f03027b" />
    <public type="attr" name="qmui_outerNormalColor" id="0x7f03027c" />
    <public type="attr" name="qmui_outlineExcludePadding" id="0x7f03027d" />
    <public type="attr" name="qmui_outlineInsetBottom" id="0x7f03027e" />
    <public type="attr" name="qmui_outlineInsetLeft" id="0x7f03027f" />
    <public type="attr" name="qmui_outlineInsetRight" id="0x7f030280" />
    <public type="attr" name="qmui_outlineInsetTop" id="0x7f030281" />
    <public type="attr" name="qmui_paddingBottomWhenNotContent" id="0x7f030282" />
    <public type="attr" name="qmui_paddingTopWhenNotTitle" id="0x7f030283" />
    <public type="attr" name="qmui_popup_arrow_down" id="0x7f030284" />
    <public type="attr" name="qmui_popup_arrow_down_margin_bottom" id="0x7f030285" />
    <public type="attr" name="qmui_popup_arrow_up" id="0x7f030286" />
    <public type="attr" name="qmui_popup_arrow_up_margin_top" id="0x7f030287" />
    <public type="attr" name="qmui_popup_bg" id="0x7f030288" />
    <public type="attr" name="qmui_progress_color" id="0x7f030289" />
    <public type="attr" name="qmui_radius" id="0x7f03028a" />
    <public type="attr" name="qmui_radiusBottomLeft" id="0x7f03028b" />
    <public type="attr" name="qmui_radiusBottomRight" id="0x7f03028c" />
    <public type="attr" name="qmui_radiusTopLeft" id="0x7f03028d" />
    <public type="attr" name="qmui_radiusTopRight" id="0x7f03028e" />
    <public type="attr" name="qmui_refresh_end_offset" id="0x7f03028f" />
    <public type="attr" name="qmui_refresh_init_offset" id="0x7f030290" />
    <public type="attr" name="qmui_rightDividerColor" id="0x7f030291" />
    <public type="attr" name="qmui_rightDividerInsetBottom" id="0x7f030292" />
    <public type="attr" name="qmui_rightDividerInsetTop" id="0x7f030293" />
    <public type="attr" name="qmui_rightDividerWidth" id="0x7f030294" />
    <public type="attr" name="qmui_round_btn_bg_color" id="0x7f030295" />
    <public type="attr" name="qmui_round_btn_border_color" id="0x7f030296" />
    <public type="attr" name="qmui_round_btn_border_width" id="0x7f030297" />
    <public type="attr" name="qmui_round_btn_text_color" id="0x7f030298" />
    <public type="attr" name="qmui_round_btn_text_size" id="0x7f030299" />
    <public type="attr" name="qmui_s_checkbox" id="0x7f03029a" />
    <public type="attr" name="qmui_s_list_item_bg_with_border_bottom" id="0x7f03029b" />
    <public type="attr" name="qmui_s_list_item_bg_with_border_bottom_inset" id="0x7f03029c" />
    <public type="attr" name="qmui_s_list_item_bg_with_border_bottom_inset_left" id="0x7f03029d" />
    <public type="attr" name="qmui_s_list_item_bg_with_border_double" id="0x7f03029e" />
    <public type="attr" name="qmui_s_list_item_bg_with_border_none" id="0x7f03029f" />
    <public type="attr" name="qmui_s_list_item_bg_with_border_top" id="0x7f0302a0" />
    <public type="attr" name="qmui_s_list_item_bg_with_border_top_inset_left" id="0x7f0302a1" />
    <public type="attr" name="qmui_scrimAnimationDuration" id="0x7f0302a2" />
    <public type="attr" name="qmui_scrimVisibleHeightTrigger" id="0x7f0302a3" />
    <public type="attr" name="qmui_selected_border_color" id="0x7f0302a4" />
    <public type="attr" name="qmui_selected_border_width" id="0x7f0302a5" />
    <public type="attr" name="qmui_selected_mask_color" id="0x7f0302a6" />
    <public type="attr" name="qmui_shadowAlpha" id="0x7f0302a7" />
    <public type="attr" name="qmui_shadowElevation" id="0x7f0302a8" />
    <public type="attr" name="qmui_showBorderOnlyBeforeL" id="0x7f0302a9" />
    <public type="attr" name="qmui_show_loading" id="0x7f0302aa" />
    <public type="attr" name="qmui_special_drawable_padding" id="0x7f0302ab" />
    <public type="attr" name="qmui_statusBarScrim" id="0x7f0302ac" />
    <public type="attr" name="qmui_stroke_round_cap" id="0x7f0302ad" />
    <public type="attr" name="qmui_stroke_width" id="0x7f0302ae" />
    <public type="attr" name="qmui_tab_has_indicator" id="0x7f0302af" />
    <public type="attr" name="qmui_tab_icon_position" id="0x7f0302b0" />
    <public type="attr" name="qmui_tab_indicator_height" id="0x7f0302b1" />
    <public type="attr" name="qmui_tab_indicator_top" id="0x7f0302b2" />
    <public type="attr" name="qmui_tab_mode" id="0x7f0302b3" />
    <public type="attr" name="qmui_tab_sign_count_view" id="0x7f0302b4" />
    <public type="attr" name="qmui_tab_sign_count_view_bg" id="0x7f0302b5" />
    <public type="attr" name="qmui_tab_sign_count_view_minSize" id="0x7f0302b6" />
    <public type="attr" name="qmui_tab_sign_count_view_minSize_with_text" id="0x7f0302b7" />
    <public type="attr" name="qmui_tab_sign_count_view_padding_horizontal" id="0x7f0302b8" />
    <public type="attr" name="qmui_tab_space" id="0x7f0302b9" />
    <public type="attr" name="qmui_tab_typeface_provider" id="0x7f0302ba" />
    <public type="attr" name="qmui_target_init_offset" id="0x7f0302bb" />
    <public type="attr" name="qmui_target_refresh_offset" id="0x7f0302bc" />
    <public type="attr" name="qmui_tip_dialog_bg" id="0x7f0302bd" />
    <public type="attr" name="qmui_tip_dialog_margin_horizontal" id="0x7f0302be" />
    <public type="attr" name="qmui_tip_dialog_min_height" id="0x7f0302bf" />
    <public type="attr" name="qmui_tip_dialog_min_width" id="0x7f0302c0" />
    <public type="attr" name="qmui_tip_dialog_padding_horizontal" id="0x7f0302c1" />
    <public type="attr" name="qmui_tip_dialog_padding_vertical" id="0x7f0302c2" />
    <public type="attr" name="qmui_title" id="0x7f0302c3" />
    <public type="attr" name="qmui_titleEnabled" id="0x7f0302c4" />
    <public type="attr" name="qmui_title_text" id="0x7f0302c5" />
    <public type="attr" name="qmui_topBarId" id="0x7f0302c6" />
    <public type="attr" name="qmui_topDividerColor" id="0x7f0302c7" />
    <public type="attr" name="qmui_topDividerHeight" id="0x7f0302c8" />
    <public type="attr" name="qmui_topDividerInsetLeft" id="0x7f0302c9" />
    <public type="attr" name="qmui_topDividerInsetRight" id="0x7f0302ca" />
    <public type="attr" name="qmui_topbar_bg_color" id="0x7f0302cb" />
    <public type="attr" name="qmui_topbar_height" id="0x7f0302cc" />
    <public type="attr" name="qmui_topbar_image_btn_height" id="0x7f0302cd" />
    <public type="attr" name="qmui_topbar_image_btn_width" id="0x7f0302ce" />
    <public type="attr" name="qmui_topbar_left_back_drawable_id" id="0x7f0302cf" />
    <public type="attr" name="qmui_topbar_need_separator" id="0x7f0302d0" />
    <public type="attr" name="qmui_topbar_separator_color" id="0x7f0302d1" />
    <public type="attr" name="qmui_topbar_separator_height" id="0x7f0302d2" />
    <public type="attr" name="qmui_topbar_subtitle_color" id="0x7f0302d3" />
    <public type="attr" name="qmui_topbar_subtitle_text_size" id="0x7f0302d4" />
    <public type="attr" name="qmui_topbar_text_btn_color_state_list" id="0x7f0302d5" />
    <public type="attr" name="qmui_topbar_text_btn_padding_horizontal" id="0x7f0302d6" />
    <public type="attr" name="qmui_topbar_text_btn_text_size" id="0x7f0302d7" />
    <public type="attr" name="qmui_topbar_title_color" id="0x7f0302d8" />
    <public type="attr" name="qmui_topbar_title_container_padding_horizontal" id="0x7f0302d9" />
    <public type="attr" name="qmui_topbar_title_gravity" id="0x7f0302da" />
    <public type="attr" name="qmui_topbar_title_margin_horizontal_when_no_btn_aside" id="0x7f0302db" />
    <public type="attr" name="qmui_topbar_title_text_size" id="0x7f0302dc" />
    <public type="attr" name="qmui_topbar_title_text_size_with_subtitle" id="0x7f0302dd" />
    <public type="attr" name="qmui_type" id="0x7f0302de" />
    <public type="attr" name="qmui_useThemeGeneralShadowElevation" id="0x7f0302df" />
    <public type="attr" name="qmui_value" id="0x7f0302e0" />
    <public type="attr" name="queryBackground" id="0x7f0302e1" />
    <public type="attr" name="queryHint" id="0x7f0302e2" />
    <public type="attr" name="radioButtonStyle" id="0x7f0302e3" />
    <public type="attr" name="ratingBarStyle" id="0x7f0302e4" />
    <public type="attr" name="ratingBarStyleIndicator" id="0x7f0302e5" />
    <public type="attr" name="ratingBarStyleSmall" id="0x7f0302e6" />
    <public type="attr" name="realtimeBlurRadius" id="0x7f0302e7" />
    <public type="attr" name="realtimeDownsampleFactor" id="0x7f0302e8" />
    <public type="attr" name="realtimeOverlayColor" id="0x7f0302e9" />
    <public type="attr" name="reparent" id="0x7f0302ea" />
    <public type="attr" name="reparentWithOverlay" id="0x7f0302eb" />
    <public type="attr" name="repeat_toggle_modes" id="0x7f0302ec" />
    <public type="attr" name="resizeClip" id="0x7f0302ed" />
    <public type="attr" name="resize_mode" id="0x7f0302ee" />
    <public type="attr" name="reverseLayout" id="0x7f0302ef" />
    <public type="attr" name="rewind_increment" id="0x7f0302f0" />
    <public type="attr" name="rippleColor" id="0x7f0302f1" />
    <public type="attr" name="scrimAnimationDuration" id="0x7f0302f2" />
    <public type="attr" name="scrimBackground" id="0x7f0302f3" />
    <public type="attr" name="scrimVisibleHeightTrigger" id="0x7f0302f4" />
    <public type="attr" name="scrubber_color" id="0x7f0302f5" />
    <public type="attr" name="scrubber_disabled_size" id="0x7f0302f6" />
    <public type="attr" name="scrubber_dragged_size" id="0x7f0302f7" />
    <public type="attr" name="scrubber_drawable" id="0x7f0302f8" />
    <public type="attr" name="scrubber_enabled_size" id="0x7f0302f9" />
    <public type="attr" name="searchHintIcon" id="0x7f0302fa" />
    <public type="attr" name="searchIcon" id="0x7f0302fb" />
    <public type="attr" name="searchViewStyle" id="0x7f0302fc" />
    <public type="attr" name="seekBarStyle" id="0x7f0302fd" />
    <public type="attr" name="selectableItemBackground" id="0x7f0302fe" />
    <public type="attr" name="selectableItemBackgroundBorderless" id="0x7f0302ff" />
    <public type="attr" name="separatorStyle" id="0x7f030300" />
    <public type="attr" name="showAsAction" id="0x7f030301" />
    <public type="attr" name="showDividers" id="0x7f030302" />
    <public type="attr" name="showMotionSpec" id="0x7f030303" />
    <public type="attr" name="showText" id="0x7f030304" />
    <public type="attr" name="showTitle" id="0x7f030305" />
    <public type="attr" name="show_buffering" id="0x7f030306" />
    <public type="attr" name="show_shuffle_button" id="0x7f030307" />
    <public type="attr" name="show_timeout" id="0x7f030308" />
    <public type="attr" name="shutter_background_color" id="0x7f030309" />
    <public type="attr" name="singleChoiceItemLayout" id="0x7f03030a" />
    <public type="attr" name="singleLine" id="0x7f03030b" />
    <public type="attr" name="singleSelection" id="0x7f03030c" />
    <public type="attr" name="slideEdge" id="0x7f03030d" />
    <public type="attr" name="snackbarButtonStyle" id="0x7f03030e" />
    <public type="attr" name="snackbarStyle" id="0x7f03030f" />
    <public type="attr" name="spanCount" id="0x7f030310" />
    <public type="attr" name="spinBars" id="0x7f030311" />
    <public type="attr" name="spinnerDropDownItemStyle" id="0x7f030312" />
    <public type="attr" name="spinnerStyle" id="0x7f030313" />
    <public type="attr" name="splitTrack" id="0x7f030314" />
    <public type="attr" name="srcCompat" id="0x7f030315" />
    <public type="attr" name="stackFromEnd" id="0x7f030316" />
    <public type="attr" name="startDelay" id="0x7f030317" />
    <public type="attr" name="state_above_anchor" id="0x7f030318" />
    <public type="attr" name="state_collapsed" id="0x7f030319" />
    <public type="attr" name="state_collapsible" id="0x7f03031a" />
    <public type="attr" name="state_liftable" id="0x7f03031b" />
    <public type="attr" name="state_lifted" id="0x7f03031c" />
    <public type="attr" name="statusBarBackground" id="0x7f03031d" />
    <public type="attr" name="statusBarScrim" id="0x7f03031e" />
    <public type="attr" name="strokeColor" id="0x7f03031f" />
    <public type="attr" name="strokeWidth" id="0x7f030320" />
    <public type="attr" name="subMenuArrow" id="0x7f030321" />
    <public type="attr" name="submitBackground" id="0x7f030322" />
    <public type="attr" name="subtitle" id="0x7f030323" />
    <public type="attr" name="subtitleTextAppearance" id="0x7f030324" />
    <public type="attr" name="subtitleTextColor" id="0x7f030325" />
    <public type="attr" name="subtitleTextStyle" id="0x7f030326" />
    <public type="attr" name="suggestionRowLayout" id="0x7f030327" />
    <public type="attr" name="surface_type" id="0x7f030328" />
    <public type="attr" name="switchMinWidth" id="0x7f030329" />
    <public type="attr" name="switchPadding" id="0x7f03032a" />
    <public type="attr" name="switchStyle" id="0x7f03032b" />
    <public type="attr" name="switchTextAppearance" id="0x7f03032c" />
    <public type="attr" name="tabBackground" id="0x7f03032d" />
    <public type="attr" name="tabContentStart" id="0x7f03032e" />
    <public type="attr" name="tabGravity" id="0x7f03032f" />
    <public type="attr" name="tabIconTint" id="0x7f030330" />
    <public type="attr" name="tabIconTintMode" id="0x7f030331" />
    <public type="attr" name="tabIndicator" id="0x7f030332" />
    <public type="attr" name="tabIndicatorAnimationDuration" id="0x7f030333" />
    <public type="attr" name="tabIndicatorColor" id="0x7f030334" />
    <public type="attr" name="tabIndicatorFullWidth" id="0x7f030335" />
    <public type="attr" name="tabIndicatorGravity" id="0x7f030336" />
    <public type="attr" name="tabIndicatorHeight" id="0x7f030337" />
    <public type="attr" name="tabInlineLabel" id="0x7f030338" />
    <public type="attr" name="tabMaxWidth" id="0x7f030339" />
    <public type="attr" name="tabMinWidth" id="0x7f03033a" />
    <public type="attr" name="tabMode" id="0x7f03033b" />
    <public type="attr" name="tabPadding" id="0x7f03033c" />
    <public type="attr" name="tabPaddingBottom" id="0x7f03033d" />
    <public type="attr" name="tabPaddingEnd" id="0x7f03033e" />
    <public type="attr" name="tabPaddingStart" id="0x7f03033f" />
    <public type="attr" name="tabPaddingTop" id="0x7f030340" />
    <public type="attr" name="tabRippleColor" id="0x7f030341" />
    <public type="attr" name="tabSelectedTextColor" id="0x7f030342" />
    <public type="attr" name="tabStyle" id="0x7f030343" />
    <public type="attr" name="tabTextAppearance" id="0x7f030344" />
    <public type="attr" name="tabTextColor" id="0x7f030345" />
    <public type="attr" name="tabUnboundedRipple" id="0x7f030346" />
    <public type="attr" name="targetClass" id="0x7f030347" />
    <public type="attr" name="targetId" id="0x7f030348" />
    <public type="attr" name="targetName" id="0x7f030349" />
    <public type="attr" name="textAllCaps" id="0x7f03034a" />
    <public type="attr" name="textAppearanceBody1" id="0x7f03034b" />
    <public type="attr" name="textAppearanceBody2" id="0x7f03034c" />
    <public type="attr" name="textAppearanceButton" id="0x7f03034d" />
    <public type="attr" name="textAppearanceCaption" id="0x7f03034e" />
    <public type="attr" name="textAppearanceHeadline1" id="0x7f03034f" />
    <public type="attr" name="textAppearanceHeadline2" id="0x7f030350" />
    <public type="attr" name="textAppearanceHeadline3" id="0x7f030351" />
    <public type="attr" name="textAppearanceHeadline4" id="0x7f030352" />
    <public type="attr" name="textAppearanceHeadline5" id="0x7f030353" />
    <public type="attr" name="textAppearanceHeadline6" id="0x7f030354" />
    <public type="attr" name="textAppearanceLargePopupMenu" id="0x7f030355" />
    <public type="attr" name="textAppearanceListItem" id="0x7f030356" />
    <public type="attr" name="textAppearanceListItemSecondary" id="0x7f030357" />
    <public type="attr" name="textAppearanceListItemSmall" id="0x7f030358" />
    <public type="attr" name="textAppearanceOverline" id="0x7f030359" />
    <public type="attr" name="textAppearancePopupMenuHeader" id="0x7f03035a" />
    <public type="attr" name="textAppearanceSearchResultSubtitle" id="0x7f03035b" />
    <public type="attr" name="textAppearanceSearchResultTitle" id="0x7f03035c" />
    <public type="attr" name="textAppearanceSmallPopupMenu" id="0x7f03035d" />
    <public type="attr" name="textAppearanceSubtitle1" id="0x7f03035e" />
    <public type="attr" name="textAppearanceSubtitle2" id="0x7f03035f" />
    <public type="attr" name="textColorAlertDialogListItem" id="0x7f030360" />
    <public type="attr" name="textColorSearchUrl" id="0x7f030361" />
    <public type="attr" name="textEndPadding" id="0x7f030362" />
    <public type="attr" name="textInputStyle" id="0x7f030363" />
    <public type="attr" name="textStartPadding" id="0x7f030364" />
    <public type="attr" name="theme" id="0x7f030365" />
    <public type="attr" name="thickness" id="0x7f030366" />
    <public type="attr" name="thumbTextPadding" id="0x7f030367" />
    <public type="attr" name="thumbTint" id="0x7f030368" />
    <public type="attr" name="thumbTintMode" id="0x7f030369" />
    <public type="attr" name="tickMark" id="0x7f03036a" />
    <public type="attr" name="tickMarkTint" id="0x7f03036b" />
    <public type="attr" name="tickMarkTintMode" id="0x7f03036c" />
    <public type="attr" name="tint" id="0x7f03036d" />
    <public type="attr" name="tintMode" id="0x7f03036e" />
    <public type="attr" name="title" id="0x7f03036f" />
    <public type="attr" name="titleEnabled" id="0x7f030370" />
    <public type="attr" name="titleMargin" id="0x7f030371" />
    <public type="attr" name="titleMarginBottom" id="0x7f030372" />
    <public type="attr" name="titleMarginEnd" id="0x7f030373" />
    <public type="attr" name="titleMarginStart" id="0x7f030374" />
    <public type="attr" name="titleMarginTop" id="0x7f030375" />
    <public type="attr" name="titleMargins" id="0x7f030376" />
    <public type="attr" name="titleTextAppearance" id="0x7f030377" />
    <public type="attr" name="titleTextColor" id="0x7f030378" />
    <public type="attr" name="titleTextStyle" id="0x7f030379" />
    <public type="attr" name="toScene" id="0x7f03037a" />
    <public type="attr" name="toolbarId" id="0x7f03037b" />
    <public type="attr" name="toolbarNavigationButtonStyle" id="0x7f03037c" />
    <public type="attr" name="toolbarStyle" id="0x7f03037d" />
    <public type="attr" name="tooltipForegroundColor" id="0x7f03037e" />
    <public type="attr" name="tooltipFrameBackground" id="0x7f03037f" />
    <public type="attr" name="tooltipText" id="0x7f030380" />
    <public type="attr" name="touch_target_height" id="0x7f030381" />
    <public type="attr" name="track" id="0x7f030382" />
    <public type="attr" name="trackTint" id="0x7f030383" />
    <public type="attr" name="trackTintMode" id="0x7f030384" />
    <public type="attr" name="transition" id="0x7f030385" />
    <public type="attr" name="transitionOrdering" id="0x7f030386" />
    <public type="attr" name="transitionVisibilityMode" id="0x7f030387" />
    <public type="attr" name="ttcIndex" id="0x7f030388" />
    <public type="attr" name="tv_colSpan" id="0x7f030389" />
    <public type="attr" name="tv_horizontalDivider" id="0x7f03038a" />
    <public type="attr" name="tv_horizontalSpacingWithMargins" id="0x7f03038b" />
    <public type="attr" name="tv_isIntelligentScroll" id="0x7f03038c" />
    <public type="attr" name="tv_isMemoryFocus" id="0x7f03038d" />
    <public type="attr" name="tv_isMenu" id="0x7f03038e" />
    <public type="attr" name="tv_laneCountsStr" id="0x7f03038f" />
    <public type="attr" name="tv_layoutManager" id="0x7f030390" />
    <public type="attr" name="tv_loadMoreBeforehandCount" id="0x7f030391" />
    <public type="attr" name="tv_numColumns" id="0x7f030392" />
    <public type="attr" name="tv_numRows" id="0x7f030393" />
    <public type="attr" name="tv_optimizeLayout" id="0x7f030394" />
    <public type="attr" name="tv_rowSpan" id="0x7f030395" />
    <public type="attr" name="tv_selectedItemIsCentered" id="0x7f030396" />
    <public type="attr" name="tv_selectedItemOffsetEnd" id="0x7f030397" />
    <public type="attr" name="tv_selectedItemOffsetStart" id="0x7f030398" />
    <public type="attr" name="tv_span" id="0x7f030399" />
    <public type="attr" name="tv_verticalDivider" id="0x7f03039a" />
    <public type="attr" name="tv_verticalSpacingWithMargins" id="0x7f03039b" />
    <public type="attr" name="unplayed_color" id="0x7f03039c" />
    <public type="attr" name="useCompatPadding" id="0x7f03039d" />
    <public type="attr" name="use_artwork" id="0x7f03039e" />
    <public type="attr" name="use_controller" id="0x7f03039f" />
    <public type="attr" name="viewInflaterClass" id="0x7f0303a0" />
    <public type="attr" name="voiceIcon" id="0x7f0303a1" />
    <public type="attr" name="windowActionBar" id="0x7f0303a2" />
    <public type="attr" name="windowActionBarOverlay" id="0x7f0303a3" />
    <public type="attr" name="windowActionModeOverlay" id="0x7f0303a4" />
    <public type="attr" name="windowFixedHeightMajor" id="0x7f0303a5" />
    <public type="attr" name="windowFixedHeightMinor" id="0x7f0303a6" />
    <public type="attr" name="windowFixedWidthMajor" id="0x7f0303a7" />
    <public type="attr" name="windowFixedWidthMinor" id="0x7f0303a8" />
    <public type="attr" name="windowMinWidthMajor" id="0x7f0303a9" />
    <public type="attr" name="windowMinWidthMinor" id="0x7f0303aa" />
    <public type="attr" name="windowNoTitle" id="0x7f0303ab" />
    <public type="bool" name="abc_action_bar_embed_tabs" id="0x7f040000" />
    <public type="bool" name="abc_allow_stacked_button_bar" id="0x7f040001" />
    <public type="bool" name="abc_config_actionMenuItemAllCaps" id="0x7f040002" />
    <public type="bool" name="mtrl_btn_textappearance_all_caps" id="0x7f040003" />
    <public type="color" name="abc_background_cache_hint_selector_material_dark" id="0x7f050000" />
    <public type="color" name="abc_background_cache_hint_selector_material_light" id="0x7f050001" />
    <public type="color" name="abc_btn_colored_borderless_text_material" id="0x7f050002" />
    <public type="color" name="abc_btn_colored_text_material" id="0x7f050003" />
    <public type="color" name="abc_color_highlight_material" id="0x7f050004" />
    <public type="color" name="abc_hint_foreground_material_dark" id="0x7f050005" />
    <public type="color" name="abc_hint_foreground_material_light" id="0x7f050006" />
    <public type="color" name="abc_input_method_navigation_guard" id="0x7f050007" />
    <public type="color" name="abc_primary_text_disable_only_material_dark" id="0x7f050008" />
    <public type="color" name="abc_primary_text_disable_only_material_light" id="0x7f050009" />
    <public type="color" name="abc_primary_text_material_dark" id="0x7f05000a" />
    <public type="color" name="abc_primary_text_material_light" id="0x7f05000b" />
    <public type="color" name="abc_search_url_text" id="0x7f05000c" />
    <public type="color" name="abc_search_url_text_normal" id="0x7f05000d" />
    <public type="color" name="abc_search_url_text_pressed" id="0x7f05000e" />
    <public type="color" name="abc_search_url_text_selected" id="0x7f05000f" />
    <public type="color" name="abc_secondary_text_material_dark" id="0x7f050010" />
    <public type="color" name="abc_secondary_text_material_light" id="0x7f050011" />
    <public type="color" name="abc_tint_btn_checkable" id="0x7f050012" />
    <public type="color" name="abc_tint_default" id="0x7f050013" />
    <public type="color" name="abc_tint_edittext" id="0x7f050014" />
    <public type="color" name="abc_tint_seek_thumb" id="0x7f050015" />
    <public type="color" name="abc_tint_spinner" id="0x7f050016" />
    <public type="color" name="abc_tint_switch_track" id="0x7f050017" />
    <public type="color" name="accent_material_dark" id="0x7f050018" />
    <public type="color" name="accent_material_light" id="0x7f050019" />
    <public type="color" name="airplay_control_bg" id="0x7f05001a" />
    <public type="color" name="airplay_slider_color" id="0x7f05001b" />
    <public type="color" name="alipay" id="0x7f05001c" />
    <public type="color" name="background" id="0x7f05001d" />
    <public type="color" name="background2" id="0x7f05001e" />
    <public type="color" name="background2_night" id="0x7f05001f" />
    <public type="color" name="background3" id="0x7f050020" />
    <public type="color" name="background3_night" id="0x7f050021" />
    <public type="color" name="background4" id="0x7f050022" />
    <public type="color" name="background5" id="0x7f050023" />
    <public type="color" name="background6" id="0x7f050024" />
    <public type="color" name="background7" id="0x7f050025" />
    <public type="color" name="background7_night" id="0x7f050026" />
    <public type="color" name="background_floating_material_dark" id="0x7f050027" />
    <public type="color" name="background_floating_material_light" id="0x7f050028" />
    <public type="color" name="background_material_dark" id="0x7f050029" />
    <public type="color" name="background_material_light" id="0x7f05002a" />
    <public type="color" name="background_night" id="0x7f05002b" />
    <public type="color" name="black" id="0x7f05002c" />
    <public type="color" name="bottom_container_bg" id="0x7f05002d" />
    <public type="color" name="bright_foreground_disabled_material_dark" id="0x7f05002e" />
    <public type="color" name="bright_foreground_disabled_material_light" id="0x7f05002f" />
    <public type="color" name="bright_foreground_inverse_material_dark" id="0x7f050030" />
    <public type="color" name="bright_foreground_inverse_material_light" id="0x7f050031" />
    <public type="color" name="bright_foreground_material_dark" id="0x7f050032" />
    <public type="color" name="bright_foreground_material_light" id="0x7f050033" />
    <public type="color" name="btn_filled_blue_bg_disabled" id="0x7f050034" />
    <public type="color" name="btn_filled_blue_bg_normal" id="0x7f050035" />
    <public type="color" name="btn_filled_blue_bg_pressed" id="0x7f050036" />
    <public type="color" name="btn_ghost_blue_border_disabled" id="0x7f050037" />
    <public type="color" name="btn_ghost_blue_border_normal" id="0x7f050038" />
    <public type="color" name="btn_ghost_blue_border_pressed" id="0x7f050039" />
    <public type="color" name="btn_ghost_blue_text_disabled" id="0x7f05003a" />
    <public type="color" name="btn_ghost_blue_text_normal" id="0x7f05003b" />
    <public type="color" name="btn_ghost_blue_text_pressed" id="0x7f05003c" />
    <public type="color" name="button_material_dark" id="0x7f05003d" />
    <public type="color" name="button_material_light" id="0x7f05003e" />
    <public type="color" name="cardview_dark_background" id="0x7f05003f" />
    <public type="color" name="cardview_light_background" id="0x7f050040" />
    <public type="color" name="cardview_shadow_end_color" id="0x7f050041" />
    <public type="color" name="cardview_shadow_start_color" id="0x7f050042" />
    <public type="color" name="colorPrimary" id="0x7f050043" />
    <public type="color" name="colorPrimary2" id="0x7f050044" />
    <public type="color" name="colorPrimary3" id="0x7f050045" />
    <public type="color" name="colorPrimaryDark" id="0x7f050046" />
    <public type="color" name="colorPrimaryLight" id="0x7f050047" />
    <public type="color" name="danger" id="0x7f050048" />
    <public type="color" name="dark" id="0x7f050049" />
    <public type="color" name="defaultTextColor" id="0x7f05004a" />
    <public type="color" name="design_bottom_navigation_shadow_color" id="0x7f05004b" />
    <public type="color" name="design_default_color_primary" id="0x7f05004c" />
    <public type="color" name="design_default_color_primary_dark" id="0x7f05004d" />
    <public type="color" name="design_error" id="0x7f05004e" />
    <public type="color" name="design_fab_shadow_end_color" id="0x7f05004f" />
    <public type="color" name="design_fab_shadow_mid_color" id="0x7f050050" />
    <public type="color" name="design_fab_shadow_start_color" id="0x7f050051" />
    <public type="color" name="design_fab_stroke_end_inner_color" id="0x7f050052" />
    <public type="color" name="design_fab_stroke_end_outer_color" id="0x7f050053" />
    <public type="color" name="design_fab_stroke_top_inner_color" id="0x7f050054" />
    <public type="color" name="design_fab_stroke_top_outer_color" id="0x7f050055" />
    <public type="color" name="design_snackbar_background_color" id="0x7f050056" />
    <public type="color" name="design_tint_password_toggle" id="0x7f050057" />
    <public type="color" name="dim_foreground_disabled_material_dark" id="0x7f050058" />
    <public type="color" name="dim_foreground_disabled_material_light" id="0x7f050059" />
    <public type="color" name="dim_foreground_material_dark" id="0x7f05005a" />
    <public type="color" name="dim_foreground_material_light" id="0x7f05005b" />
    <public type="color" name="errorColor" id="0x7f05005c" />
    <public type="color" name="error_color_material_dark" id="0x7f05005d" />
    <public type="color" name="error_color_material_light" id="0x7f05005e" />
    <public type="color" name="exo_edit_mode_background_color" id="0x7f05005f" />
    <public type="color" name="exo_error_message_background_color" id="0x7f050060" />
    <public type="color" name="foreground_material_dark" id="0x7f050061" />
    <public type="color" name="foreground_material_light" id="0x7f050062" />
    <public type="color" name="glass2" id="0x7f050063" />
    <public type="color" name="gray" id="0x7f050064" />
    <public type="color" name="highlighted_text_material_dark" id="0x7f050065" />
    <public type="color" name="highlighted_text_material_light" id="0x7f050066" />
    <public type="color" name="infoColor" id="0x7f050067" />
    <public type="color" name="isb_selector_tick_marks_color" id="0x7f050068" />
    <public type="color" name="isb_selector_tick_texts_color" id="0x7f050069" />
    <public type="color" name="line1" id="0x7f05006a" />
    <public type="color" name="line1_night" id="0x7f05006b" />
    <public type="color" name="material_blue_grey_800" id="0x7f05006c" />
    <public type="color" name="material_blue_grey_900" id="0x7f05006d" />
    <public type="color" name="material_blue_grey_950" id="0x7f05006e" />
    <public type="color" name="material_deep_teal_200" id="0x7f05006f" />
    <public type="color" name="material_deep_teal_500" id="0x7f050070" />
    <public type="color" name="material_grey_100" id="0x7f050071" />
    <public type="color" name="material_grey_300" id="0x7f050072" />
    <public type="color" name="material_grey_50" id="0x7f050073" />
    <public type="color" name="material_grey_600" id="0x7f050074" />
    <public type="color" name="material_grey_800" id="0x7f050075" />
    <public type="color" name="material_grey_850" id="0x7f050076" />
    <public type="color" name="material_grey_900" id="0x7f050077" />
    <public type="color" name="mtrl_bottom_nav_colored_item_tint" id="0x7f050078" />
    <public type="color" name="mtrl_bottom_nav_item_tint" id="0x7f050079" />
    <public type="color" name="mtrl_btn_bg_color_disabled" id="0x7f05007a" />
    <public type="color" name="mtrl_btn_bg_color_selector" id="0x7f05007b" />
    <public type="color" name="mtrl_btn_ripple_color" id="0x7f05007c" />
    <public type="color" name="mtrl_btn_stroke_color_selector" id="0x7f05007d" />
    <public type="color" name="mtrl_btn_text_btn_ripple_color" id="0x7f05007e" />
    <public type="color" name="mtrl_btn_text_color_disabled" id="0x7f05007f" />
    <public type="color" name="mtrl_btn_text_color_selector" id="0x7f050080" />
    <public type="color" name="mtrl_btn_transparent_bg_color" id="0x7f050081" />
    <public type="color" name="mtrl_chip_background_color" id="0x7f050082" />
    <public type="color" name="mtrl_chip_close_icon_tint" id="0x7f050083" />
    <public type="color" name="mtrl_chip_ripple_color" id="0x7f050084" />
    <public type="color" name="mtrl_chip_text_color" id="0x7f050085" />
    <public type="color" name="mtrl_fab_ripple_color" id="0x7f050086" />
    <public type="color" name="mtrl_scrim_color" id="0x7f050087" />
    <public type="color" name="mtrl_tabs_colored_ripple_color" id="0x7f050088" />
    <public type="color" name="mtrl_tabs_icon_color_selector" id="0x7f050089" />
    <public type="color" name="mtrl_tabs_icon_color_selector_colored" id="0x7f05008a" />
    <public type="color" name="mtrl_tabs_legacy_text_color_selector" id="0x7f05008b" />
    <public type="color" name="mtrl_tabs_ripple_color" id="0x7f05008c" />
    <public type="color" name="mtrl_text_btn_text_color_selector" id="0x7f05008d" />
    <public type="color" name="mtrl_textinput_default_box_stroke_color" id="0x7f05008e" />
    <public type="color" name="mtrl_textinput_disabled_color" id="0x7f05008f" />
    <public type="color" name="mtrl_textinput_filled_box_default_background_color" id="0x7f050090" />
    <public type="color" name="mtrl_textinput_hovered_box_stroke_color" id="0x7f050091" />
    <public type="color" name="normalColor" id="0x7f050092" />
    <public type="color" name="notification_action_color_filter" id="0x7f050093" />
    <public type="color" name="notification_icon_bg_color" id="0x7f050094" />
    <public type="color" name="notification_material_background_media_default_color" id="0x7f050095" />
    <public type="color" name="primary_dark_material_dark" id="0x7f050096" />
    <public type="color" name="primary_dark_material_light" id="0x7f050097" />
    <public type="color" name="primary_material_dark" id="0x7f050098" />
    <public type="color" name="primary_material_light" id="0x7f050099" />
    <public type="color" name="primary_text_default_material_dark" id="0x7f05009a" />
    <public type="color" name="primary_text_default_material_light" id="0x7f05009b" />
    <public type="color" name="primary_text_disabled_material_dark" id="0x7f05009c" />
    <public type="color" name="primary_text_disabled_material_light" id="0x7f05009d" />
    <public type="color" name="qmui_btn_blue_bg" id="0x7f05009e" />
    <public type="color" name="qmui_btn_blue_border" id="0x7f05009f" />
    <public type="color" name="qmui_btn_blue_text" id="0x7f0500a0" />
    <public type="color" name="qmui_common_list_item_text_color" id="0x7f0500a1" />
    <public type="color" name="qmui_config_color_10_pure_black" id="0x7f0500a2" />
    <public type="color" name="qmui_config_color_10_white" id="0x7f0500a3" />
    <public type="color" name="qmui_config_color_15_pure_black" id="0x7f0500a4" />
    <public type="color" name="qmui_config_color_15_white" id="0x7f0500a5" />
    <public type="color" name="qmui_config_color_25_pure_black" id="0x7f0500a6" />
    <public type="color" name="qmui_config_color_25_white" id="0x7f0500a7" />
    <public type="color" name="qmui_config_color_50_blue" id="0x7f0500a8" />
    <public type="color" name="qmui_config_color_50_pure_black" id="0x7f0500a9" />
    <public type="color" name="qmui_config_color_50_white" id="0x7f0500aa" />
    <public type="color" name="qmui_config_color_60_pure_black" id="0x7f0500ab" />
    <public type="color" name="qmui_config_color_75_pure_black" id="0x7f0500ac" />
    <public type="color" name="qmui_config_color_75_white" id="0x7f0500ad" />
    <public type="color" name="qmui_config_color_background" id="0x7f0500ae" />
    <public type="color" name="qmui_config_color_background_pressed" id="0x7f0500af" />
    <public type="color" name="qmui_config_color_black" id="0x7f0500b0" />
    <public type="color" name="qmui_config_color_blue" id="0x7f0500b1" />
    <public type="color" name="qmui_config_color_gray_1" id="0x7f0500b2" />
    <public type="color" name="qmui_config_color_gray_2" id="0x7f0500b3" />
    <public type="color" name="qmui_config_color_gray_3" id="0x7f0500b4" />
    <public type="color" name="qmui_config_color_gray_4" id="0x7f0500b5" />
    <public type="color" name="qmui_config_color_gray_5" id="0x7f0500b6" />
    <public type="color" name="qmui_config_color_gray_6" id="0x7f0500b7" />
    <public type="color" name="qmui_config_color_gray_7" id="0x7f0500b8" />
    <public type="color" name="qmui_config_color_gray_8" id="0x7f0500b9" />
    <public type="color" name="qmui_config_color_gray_9" id="0x7f0500ba" />
    <public type="color" name="qmui_config_color_link" id="0x7f0500bb" />
    <public type="color" name="qmui_config_color_pressed" id="0x7f0500bc" />
    <public type="color" name="qmui_config_color_pure_black" id="0x7f0500bd" />
    <public type="color" name="qmui_config_color_red" id="0x7f0500be" />
    <public type="color" name="qmui_config_color_separator" id="0x7f0500bf" />
    <public type="color" name="qmui_config_color_separator_darken" id="0x7f0500c0" />
    <public type="color" name="qmui_config_color_transparent" id="0x7f0500c1" />
    <public type="color" name="qmui_config_color_white" id="0x7f0500c2" />
    <public type="color" name="qmui_drawable_color_list_pressed" id="0x7f0500c3" />
    <public type="color" name="qmui_drawable_color_list_separator" id="0x7f0500c4" />
    <public type="color" name="qmui_group_list_section_header_text_color" id="0x7f0500c5" />
    <public type="color" name="qmui_s_link_color" id="0x7f0500c6" />
    <public type="color" name="qmui_s_list_item_text_color" id="0x7f0500c7" />
    <public type="color" name="qmui_s_switch_text_color" id="0x7f0500c8" />
    <public type="color" name="qmui_s_transparent" id="0x7f0500c9" />
    <public type="color" name="qmui_tab_segment_bottom_line_color" id="0x7f0500ca" />
    <public type="color" name="qmui_tab_segment_text_color" id="0x7f0500cb" />
    <public type="color" name="qmui_topbar_text_color" id="0x7f0500cc" />
    <public type="color" name="radio1" id="0x7f0500cd" />
    <public type="color" name="radio2" id="0x7f0500ce" />
    <public type="color" name="ripple_material_dark" id="0x7f0500cf" />
    <public type="color" name="ripple_material_light" id="0x7f0500d0" />
    <public type="color" name="secondary_text_default_material_dark" id="0x7f0500d1" />
    <public type="color" name="secondary_text_default_material_light" id="0x7f0500d2" />
    <public type="color" name="secondary_text_disabled_material_dark" id="0x7f0500d3" />
    <public type="color" name="secondary_text_disabled_material_light" id="0x7f0500d4" />
    <public type="color" name="style_color" id="0x7f0500d5" />
    <public type="color" name="successColor" id="0x7f0500d6" />
    <public type="color" name="switch_thumb_disabled_material_dark" id="0x7f0500d7" />
    <public type="color" name="switch_thumb_disabled_material_light" id="0x7f0500d8" />
    <public type="color" name="switch_thumb_material_dark" id="0x7f0500d9" />
    <public type="color" name="switch_thumb_material_light" id="0x7f0500da" />
    <public type="color" name="switch_thumb_normal_material_dark" id="0x7f0500db" />
    <public type="color" name="switch_thumb_normal_material_light" id="0x7f0500dc" />
    <public type="color" name="tagBackground" id="0x7f0500dd" />
    <public type="color" name="tagBackground_night" id="0x7f0500de" />
    <public type="color" name="text" id="0x7f0500df" />
    <public type="color" name="text2" id="0x7f0500e0" />
    <public type="color" name="text2_night" id="0x7f0500e1" />
    <public type="color" name="text3" id="0x7f0500e2" />
    <public type="color" name="text4" id="0x7f0500e3" />
    <public type="color" name="textWhite" id="0x7f0500e4" />
    <public type="color" name="text_night" id="0x7f0500e5" />
    <public type="color" name="tipBg" id="0x7f0500e6" />
    <public type="color" name="tooltip_background_dark" id="0x7f0500e7" />
    <public type="color" name="tooltip_background_light" id="0x7f0500e8" />
    <public type="color" name="transparent" id="0x7f0500e9" />
    <public type="color" name="tv_background" id="0x7f0500ea" />
    <public type="color" name="tv_background2" id="0x7f0500eb" />
    <public type="color" name="warning" id="0x7f0500ec" />
    <public type="color" name="warningColor" id="0x7f0500ed" />
    <public type="color" name="wechat" id="0x7f0500ee" />
    <public type="color" name="white" id="0x7f0500ef" />
    <public type="dimen" name="abc_action_bar_content_inset_material" id="0x7f060000" />
    <public type="dimen" name="abc_action_bar_content_inset_with_nav" id="0x7f060001" />
    <public type="dimen" name="abc_action_bar_default_height_material" id="0x7f060002" />
    <public type="dimen" name="abc_action_bar_default_padding_end_material" id="0x7f060003" />
    <public type="dimen" name="abc_action_bar_default_padding_start_material" id="0x7f060004" />
    <public type="dimen" name="abc_action_bar_elevation_material" id="0x7f060005" />
    <public type="dimen" name="abc_action_bar_icon_vertical_padding_material" id="0x7f060006" />
    <public type="dimen" name="abc_action_bar_overflow_padding_end_material" id="0x7f060007" />
    <public type="dimen" name="abc_action_bar_overflow_padding_start_material" id="0x7f060008" />
    <public type="dimen" name="abc_action_bar_stacked_max_height" id="0x7f060009" />
    <public type="dimen" name="abc_action_bar_stacked_tab_max_width" id="0x7f06000a" />
    <public type="dimen" name="abc_action_bar_subtitle_bottom_margin_material" id="0x7f06000b" />
    <public type="dimen" name="abc_action_bar_subtitle_top_margin_material" id="0x7f06000c" />
    <public type="dimen" name="abc_action_button_min_height_material" id="0x7f06000d" />
    <public type="dimen" name="abc_action_button_min_width_material" id="0x7f06000e" />
    <public type="dimen" name="abc_action_button_min_width_overflow_material" id="0x7f06000f" />
    <public type="dimen" name="abc_alert_dialog_button_bar_height" id="0x7f060010" />
    <public type="dimen" name="abc_alert_dialog_button_dimen" id="0x7f060011" />
    <public type="dimen" name="abc_button_inset_horizontal_material" id="0x7f060012" />
    <public type="dimen" name="abc_button_inset_vertical_material" id="0x7f060013" />
    <public type="dimen" name="abc_button_padding_horizontal_material" id="0x7f060014" />
    <public type="dimen" name="abc_button_padding_vertical_material" id="0x7f060015" />
    <public type="dimen" name="abc_cascading_menus_min_smallest_width" id="0x7f060016" />
    <public type="dimen" name="abc_config_prefDialogWidth" id="0x7f060017" />
    <public type="dimen" name="abc_control_corner_material" id="0x7f060018" />
    <public type="dimen" name="abc_control_inset_material" id="0x7f060019" />
    <public type="dimen" name="abc_control_padding_material" id="0x7f06001a" />
    <public type="dimen" name="abc_dialog_corner_radius_material" id="0x7f06001b" />
    <public type="dimen" name="abc_dialog_fixed_height_major" id="0x7f06001c" />
    <public type="dimen" name="abc_dialog_fixed_height_minor" id="0x7f06001d" />
    <public type="dimen" name="abc_dialog_fixed_width_major" id="0x7f06001e" />
    <public type="dimen" name="abc_dialog_fixed_width_minor" id="0x7f06001f" />
    <public type="dimen" name="abc_dialog_list_padding_bottom_no_buttons" id="0x7f060020" />
    <public type="dimen" name="abc_dialog_list_padding_top_no_title" id="0x7f060021" />
    <public type="dimen" name="abc_dialog_min_width_major" id="0x7f060022" />
    <public type="dimen" name="abc_dialog_min_width_minor" id="0x7f060023" />
    <public type="dimen" name="abc_dialog_padding_material" id="0x7f060024" />
    <public type="dimen" name="abc_dialog_padding_top_material" id="0x7f060025" />
    <public type="dimen" name="abc_dialog_title_divider_material" id="0x7f060026" />
    <public type="dimen" name="abc_disabled_alpha_material_dark" id="0x7f060027" />
    <public type="dimen" name="abc_disabled_alpha_material_light" id="0x7f060028" />
    <public type="dimen" name="abc_dropdownitem_icon_width" id="0x7f060029" />
    <public type="dimen" name="abc_dropdownitem_text_padding_left" id="0x7f06002a" />
    <public type="dimen" name="abc_dropdownitem_text_padding_right" id="0x7f06002b" />
    <public type="dimen" name="abc_edit_text_inset_bottom_material" id="0x7f06002c" />
    <public type="dimen" name="abc_edit_text_inset_horizontal_material" id="0x7f06002d" />
    <public type="dimen" name="abc_edit_text_inset_top_material" id="0x7f06002e" />
    <public type="dimen" name="abc_floating_window_z" id="0x7f06002f" />
    <public type="dimen" name="abc_list_item_padding_horizontal_material" id="0x7f060030" />
    <public type="dimen" name="abc_panel_menu_list_width" id="0x7f060031" />
    <public type="dimen" name="abc_progress_bar_height_material" id="0x7f060032" />
    <public type="dimen" name="abc_search_view_preferred_height" id="0x7f060033" />
    <public type="dimen" name="abc_search_view_preferred_width" id="0x7f060034" />
    <public type="dimen" name="abc_seekbar_track_background_height_material" id="0x7f060035" />
    <public type="dimen" name="abc_seekbar_track_progress_height_material" id="0x7f060036" />
    <public type="dimen" name="abc_select_dialog_padding_start_material" id="0x7f060037" />
    <public type="dimen" name="abc_switch_padding" id="0x7f060038" />
    <public type="dimen" name="abc_text_size_body_1_material" id="0x7f060039" />
    <public type="dimen" name="abc_text_size_body_2_material" id="0x7f06003a" />
    <public type="dimen" name="abc_text_size_button_material" id="0x7f06003b" />
    <public type="dimen" name="abc_text_size_caption_material" id="0x7f06003c" />
    <public type="dimen" name="abc_text_size_display_1_material" id="0x7f06003d" />
    <public type="dimen" name="abc_text_size_display_2_material" id="0x7f06003e" />
    <public type="dimen" name="abc_text_size_display_3_material" id="0x7f06003f" />
    <public type="dimen" name="abc_text_size_display_4_material" id="0x7f060040" />
    <public type="dimen" name="abc_text_size_headline_material" id="0x7f060041" />
    <public type="dimen" name="abc_text_size_large_material" id="0x7f060042" />
    <public type="dimen" name="abc_text_size_medium_material" id="0x7f060043" />
    <public type="dimen" name="abc_text_size_menu_header_material" id="0x7f060044" />
    <public type="dimen" name="abc_text_size_menu_material" id="0x7f060045" />
    <public type="dimen" name="abc_text_size_small_material" id="0x7f060046" />
    <public type="dimen" name="abc_text_size_subhead_material" id="0x7f060047" />
    <public type="dimen" name="abc_text_size_subtitle_material_toolbar" id="0x7f060048" />
    <public type="dimen" name="abc_text_size_title_material" id="0x7f060049" />
    <public type="dimen" name="abc_text_size_title_material_toolbar" id="0x7f06004a" />
    <public type="dimen" name="brightness_icon" id="0x7f06004b" />
    <public type="dimen" name="cardview_compat_inset_shadow" id="0x7f06004c" />
    <public type="dimen" name="cardview_default_elevation" id="0x7f06004d" />
    <public type="dimen" name="cardview_default_radius" id="0x7f06004e" />
    <public type="dimen" name="compat_button_inset_horizontal_material" id="0x7f06004f" />
    <public type="dimen" name="compat_button_inset_vertical_material" id="0x7f060050" />
    <public type="dimen" name="compat_button_padding_horizontal_material" id="0x7f060051" />
    <public type="dimen" name="compat_button_padding_vertical_material" id="0x7f060052" />
    <public type="dimen" name="compat_control_corner_material" id="0x7f060053" />
    <public type="dimen" name="compat_notification_large_icon_max_height" id="0x7f060054" />
    <public type="dimen" name="compat_notification_large_icon_max_width" id="0x7f060055" />
    <public type="dimen" name="design_appbar_elevation" id="0x7f060056" />
    <public type="dimen" name="design_bottom_navigation_active_item_max_width" id="0x7f060057" />
    <public type="dimen" name="design_bottom_navigation_active_item_min_width" id="0x7f060058" />
    <public type="dimen" name="design_bottom_navigation_active_text_size" id="0x7f060059" />
    <public type="dimen" name="design_bottom_navigation_elevation" id="0x7f06005a" />
    <public type="dimen" name="design_bottom_navigation_height" id="0x7f06005b" />
    <public type="dimen" name="design_bottom_navigation_icon_size" id="0x7f06005c" />
    <public type="dimen" name="design_bottom_navigation_item_max_width" id="0x7f06005d" />
    <public type="dimen" name="design_bottom_navigation_item_min_width" id="0x7f06005e" />
    <public type="dimen" name="design_bottom_navigation_margin" id="0x7f06005f" />
    <public type="dimen" name="design_bottom_navigation_shadow_height" id="0x7f060060" />
    <public type="dimen" name="design_bottom_navigation_text_size" id="0x7f060061" />
    <public type="dimen" name="design_bottom_sheet_modal_elevation" id="0x7f060062" />
    <public type="dimen" name="design_bottom_sheet_peek_height_min" id="0x7f060063" />
    <public type="dimen" name="design_fab_border_width" id="0x7f060064" />
    <public type="dimen" name="design_fab_elevation" id="0x7f060065" />
    <public type="dimen" name="design_fab_image_size" id="0x7f060066" />
    <public type="dimen" name="design_fab_size_mini" id="0x7f060067" />
    <public type="dimen" name="design_fab_size_normal" id="0x7f060068" />
    <public type="dimen" name="design_fab_translation_z_hovered_focused" id="0x7f060069" />
    <public type="dimen" name="design_fab_translation_z_pressed" id="0x7f06006a" />
    <public type="dimen" name="design_navigation_elevation" id="0x7f06006b" />
    <public type="dimen" name="design_navigation_icon_padding" id="0x7f06006c" />
    <public type="dimen" name="design_navigation_icon_size" id="0x7f06006d" />
    <public type="dimen" name="design_navigation_item_horizontal_padding" id="0x7f06006e" />
    <public type="dimen" name="design_navigation_item_icon_padding" id="0x7f06006f" />
    <public type="dimen" name="design_navigation_max_width" id="0x7f060070" />
    <public type="dimen" name="design_navigation_padding_bottom" id="0x7f060071" />
    <public type="dimen" name="design_navigation_separator_vertical_padding" id="0x7f060072" />
    <public type="dimen" name="design_snackbar_action_inline_max_width" id="0x7f060073" />
    <public type="dimen" name="design_snackbar_background_corner_radius" id="0x7f060074" />
    <public type="dimen" name="design_snackbar_elevation" id="0x7f060075" />
    <public type="dimen" name="design_snackbar_extra_spacing_horizontal" id="0x7f060076" />
    <public type="dimen" name="design_snackbar_max_width" id="0x7f060077" />
    <public type="dimen" name="design_snackbar_min_width" id="0x7f060078" />
    <public type="dimen" name="design_snackbar_padding_horizontal" id="0x7f060079" />
    <public type="dimen" name="design_snackbar_padding_vertical" id="0x7f06007a" />
    <public type="dimen" name="design_snackbar_padding_vertical_2lines" id="0x7f06007b" />
    <public type="dimen" name="design_snackbar_text_size" id="0x7f06007c" />
    <public type="dimen" name="design_tab_max_width" id="0x7f06007d" />
    <public type="dimen" name="design_tab_scrollable_min_width" id="0x7f06007e" />
    <public type="dimen" name="design_tab_text_size" id="0x7f06007f" />
    <public type="dimen" name="design_tab_text_size_2line" id="0x7f060080" />
    <public type="dimen" name="design_textinput_caption_translate_y" id="0x7f060081" />
    <public type="dimen" name="disabled_alpha_material_dark" id="0x7f060082" />
    <public type="dimen" name="disabled_alpha_material_light" id="0x7f060083" />
    <public type="dimen" name="exo_media_button_height" id="0x7f060084" />
    <public type="dimen" name="exo_media_button_width" id="0x7f060085" />
    <public type="dimen" name="fastscroll_default_thickness" id="0x7f060086" />
    <public type="dimen" name="fastscroll_margin" id="0x7f060087" />
    <public type="dimen" name="fastscroll_minimum_range" id="0x7f060088" />
    <public type="dimen" name="highlight_alpha_material_colored" id="0x7f060089" />
    <public type="dimen" name="highlight_alpha_material_dark" id="0x7f06008a" />
    <public type="dimen" name="highlight_alpha_material_light" id="0x7f06008b" />
    <public type="dimen" name="hint_alpha_material_dark" id="0x7f06008c" />
    <public type="dimen" name="hint_alpha_material_light" id="0x7f06008d" />
    <public type="dimen" name="hint_pressed_alpha_material_dark" id="0x7f06008e" />
    <public type="dimen" name="hint_pressed_alpha_material_light" id="0x7f06008f" />
    <public type="dimen" name="item_touch_helper_max_drag_scroll_per_frame" id="0x7f060090" />
    <public type="dimen" name="item_touch_helper_swipe_escape_max_velocity" id="0x7f060091" />
    <public type="dimen" name="item_touch_helper_swipe_escape_velocity" id="0x7f060092" />
    <public type="dimen" name="mtrl_bottomappbar_fabOffsetEndMode" id="0x7f060093" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_margin" id="0x7f060094" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius" id="0x7f060095" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_vertical_offset" id="0x7f060096" />
    <public type="dimen" name="mtrl_bottomappbar_height" id="0x7f060097" />
    <public type="dimen" name="mtrl_btn_corner_radius" id="0x7f060098" />
    <public type="dimen" name="mtrl_btn_dialog_btn_min_width" id="0x7f060099" />
    <public type="dimen" name="mtrl_btn_disabled_elevation" id="0x7f06009a" />
    <public type="dimen" name="mtrl_btn_disabled_z" id="0x7f06009b" />
    <public type="dimen" name="mtrl_btn_elevation" id="0x7f06009c" />
    <public type="dimen" name="mtrl_btn_focused_z" id="0x7f06009d" />
    <public type="dimen" name="mtrl_btn_hovered_z" id="0x7f06009e" />
    <public type="dimen" name="mtrl_btn_icon_btn_padding_left" id="0x7f06009f" />
    <public type="dimen" name="mtrl_btn_icon_padding" id="0x7f0600a0" />
    <public type="dimen" name="mtrl_btn_inset" id="0x7f0600a1" />
    <public type="dimen" name="mtrl_btn_letter_spacing" id="0x7f0600a2" />
    <public type="dimen" name="mtrl_btn_padding_bottom" id="0x7f0600a3" />
    <public type="dimen" name="mtrl_btn_padding_left" id="0x7f0600a4" />
    <public type="dimen" name="mtrl_btn_padding_right" id="0x7f0600a5" />
    <public type="dimen" name="mtrl_btn_padding_top" id="0x7f0600a6" />
    <public type="dimen" name="mtrl_btn_pressed_z" id="0x7f0600a7" />
    <public type="dimen" name="mtrl_btn_stroke_size" id="0x7f0600a8" />
    <public type="dimen" name="mtrl_btn_text_btn_icon_padding" id="0x7f0600a9" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_left" id="0x7f0600aa" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_right" id="0x7f0600ab" />
    <public type="dimen" name="mtrl_btn_text_size" id="0x7f0600ac" />
    <public type="dimen" name="mtrl_btn_z" id="0x7f0600ad" />
    <public type="dimen" name="mtrl_card_elevation" id="0x7f0600ae" />
    <public type="dimen" name="mtrl_card_spacing" id="0x7f0600af" />
    <public type="dimen" name="mtrl_chip_pressed_translation_z" id="0x7f0600b0" />
    <public type="dimen" name="mtrl_chip_text_size" id="0x7f0600b1" />
    <public type="dimen" name="mtrl_fab_elevation" id="0x7f0600b2" />
    <public type="dimen" name="mtrl_fab_translation_z_hovered_focused" id="0x7f0600b3" />
    <public type="dimen" name="mtrl_fab_translation_z_pressed" id="0x7f0600b4" />
    <public type="dimen" name="mtrl_navigation_elevation" id="0x7f0600b5" />
    <public type="dimen" name="mtrl_navigation_item_horizontal_padding" id="0x7f0600b6" />
    <public type="dimen" name="mtrl_navigation_item_icon_padding" id="0x7f0600b7" />
    <public type="dimen" name="mtrl_snackbar_background_corner_radius" id="0x7f0600b8" />
    <public type="dimen" name="mtrl_snackbar_margin" id="0x7f0600b9" />
    <public type="dimen" name="mtrl_textinput_box_bottom_offset" id="0x7f0600ba" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_medium" id="0x7f0600bb" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_small" id="0x7f0600bc" />
    <public type="dimen" name="mtrl_textinput_box_label_cutout_padding" id="0x7f0600bd" />
    <public type="dimen" name="mtrl_textinput_box_padding_end" id="0x7f0600be" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_default" id="0x7f0600bf" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_focused" id="0x7f0600c0" />
    <public type="dimen" name="mtrl_textinput_outline_box_expanded_padding" id="0x7f0600c1" />
    <public type="dimen" name="mtrl_toolbar_default_height" id="0x7f0600c2" />
    <public type="dimen" name="notification_action_icon_size" id="0x7f0600c3" />
    <public type="dimen" name="notification_action_text_size" id="0x7f0600c4" />
    <public type="dimen" name="notification_big_circle_margin" id="0x7f0600c5" />
    <public type="dimen" name="notification_content_margin_start" id="0x7f0600c6" />
    <public type="dimen" name="notification_large_icon_height" id="0x7f0600c7" />
    <public type="dimen" name="notification_large_icon_width" id="0x7f0600c8" />
    <public type="dimen" name="notification_main_column_padding_top" id="0x7f0600c9" />
    <public type="dimen" name="notification_media_narrow_margin" id="0x7f0600ca" />
    <public type="dimen" name="notification_right_icon_size" id="0x7f0600cb" />
    <public type="dimen" name="notification_right_side_padding_top" id="0x7f0600cc" />
    <public type="dimen" name="notification_small_icon_background_padding" id="0x7f0600cd" />
    <public type="dimen" name="notification_small_icon_size_as_large" id="0x7f0600ce" />
    <public type="dimen" name="notification_subtext_size" id="0x7f0600cf" />
    <public type="dimen" name="notification_top_pad" id="0x7f0600d0" />
    <public type="dimen" name="notification_top_pad_large_text" id="0x7f0600d1" />
    <public type="dimen" name="qmui_btn_border_width" id="0x7f0600d2" />
    <public type="dimen" name="qmui_btn_text_size" id="0x7f0600d3" />
    <public type="dimen" name="qmui_content_padding_horizontal" id="0x7f0600d4" />
    <public type="dimen" name="qmui_content_spacing_horizontal" id="0x7f0600d5" />
    <public type="dimen" name="qmui_dialog_radius" id="0x7f0600d6" />
    <public type="dimen" name="qmui_group_list_section_header_footer_padding_vertical" id="0x7f0600d7" />
    <public type="dimen" name="qmui_group_list_section_header_footer_textSize" id="0x7f0600d8" />
    <public type="dimen" name="qmui_group_list_section_header_footer_text_size" id="0x7f0600d9" />
    <public type="dimen" name="qmui_list_divider_height" id="0x7f0600da" />
    <public type="dimen" name="qmui_list_divider_height_negative" id="0x7f0600db" />
    <public type="dimen" name="qmui_list_item_height" id="0x7f0600dc" />
    <public type="dimen" name="qmui_list_item_height_higher" id="0x7f0600dd" />
    <public type="dimen" name="qmui_list_item_inset_left" id="0x7f0600de" />
    <public type="dimen" name="qmui_switch_size" id="0x7f0600df" />
    <public type="dimen" name="qmui_tab_segment_indicator_height" id="0x7f0600e0" />
    <public type="dimen" name="qmui_tab_segment_text_size" id="0x7f0600e1" />
    <public type="dimen" name="qmui_tab_sign_count_view_minSize" id="0x7f0600e2" />
    <public type="dimen" name="qmui_tab_sign_count_view_minSize_with_text" id="0x7f0600e3" />
    <public type="dimen" name="qmui_tips_point_size" id="0x7f0600e4" />
    <public type="dimen" name="seek_bar_image" id="0x7f0600e5" />
    <public type="dimen" name="subtitle_corner_radius" id="0x7f0600e6" />
    <public type="dimen" name="subtitle_outline_width" id="0x7f0600e7" />
    <public type="dimen" name="subtitle_shadow_offset" id="0x7f0600e8" />
    <public type="dimen" name="subtitle_shadow_radius" id="0x7f0600e9" />
    <public type="dimen" name="tooltip_corner_radius" id="0x7f0600ea" />
    <public type="dimen" name="tooltip_horizontal_padding" id="0x7f0600eb" />
    <public type="dimen" name="tooltip_margin" id="0x7f0600ec" />
    <public type="dimen" name="tooltip_precise_anchor_extra_offset" id="0x7f0600ed" />
    <public type="dimen" name="tooltip_precise_anchor_threshold" id="0x7f0600ee" />
    <public type="dimen" name="tooltip_vertical_padding" id="0x7f0600ef" />
    <public type="dimen" name="tooltip_y_offset_non_touch" id="0x7f0600f0" />
    <public type="dimen" name="tooltip_y_offset_touch" id="0x7f0600f1" />
    <public type="dimen" name="video_progress_dialog_margin_top" id="0x7f0600f2" />
    <public type="dimen" name="video_volume_dialog_margin_left" id="0x7f0600f3" />
    <public type="drawable" name="$avd_hide_password__0" id="0x7f070000" />
    <public type="drawable" name="$avd_hide_password__1" id="0x7f070001" />
    <public type="drawable" name="$avd_hide_password__2" id="0x7f070002" />
    <public type="drawable" name="$avd_show_password__0" id="0x7f070003" />
    <public type="drawable" name="$avd_show_password__1" id="0x7f070004" />
    <public type="drawable" name="$avd_show_password__2" id="0x7f070005" />
    <public type="drawable" name="abc_ab_share_pack_mtrl_alpha" id="0x7f070006" />
    <public type="drawable" name="abc_action_bar_item_background_material" id="0x7f070007" />
    <public type="drawable" name="abc_btn_borderless_material" id="0x7f070008" />
    <public type="drawable" name="abc_btn_check_material" id="0x7f070009" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_000" id="0x7f07000a" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_015" id="0x7f07000b" />
    <public type="drawable" name="abc_btn_colored_material" id="0x7f07000c" />
    <public type="drawable" name="abc_btn_default_mtrl_shape" id="0x7f07000d" />
    <public type="drawable" name="abc_btn_radio_material" id="0x7f07000e" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_000" id="0x7f07000f" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_015" id="0x7f070010" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00001" id="0x7f070011" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00012" id="0x7f070012" />
    <public type="drawable" name="abc_cab_background_internal_bg" id="0x7f070013" />
    <public type="drawable" name="abc_cab_background_top_material" id="0x7f070014" />
    <public type="drawable" name="abc_cab_background_top_mtrl_alpha" id="0x7f070015" />
    <public type="drawable" name="abc_control_background_material" id="0x7f070016" />
    <public type="drawable" name="abc_dialog_material_background" id="0x7f070017" />
    <public type="drawable" name="abc_edit_text_material" id="0x7f070018" />
    <public type="drawable" name="abc_ic_ab_back_material" id="0x7f070019" />
    <public type="drawable" name="abc_ic_arrow_drop_right_black_24dp" id="0x7f07001a" />
    <public type="drawable" name="abc_ic_clear_material" id="0x7f07001b" />
    <public type="drawable" name="abc_ic_commit_search_api_mtrl_alpha" id="0x7f07001c" />
    <public type="drawable" name="abc_ic_go_search_api_material" id="0x7f07001d" />
    <public type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha" id="0x7f07001e" />
    <public type="drawable" name="abc_ic_menu_cut_mtrl_alpha" id="0x7f07001f" />
    <public type="drawable" name="abc_ic_menu_overflow_material" id="0x7f070020" />
    <public type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha" id="0x7f070021" />
    <public type="drawable" name="abc_ic_menu_selectall_mtrl_alpha" id="0x7f070022" />
    <public type="drawable" name="abc_ic_menu_share_mtrl_alpha" id="0x7f070023" />
    <public type="drawable" name="abc_ic_search_api_material" id="0x7f070024" />
    <public type="drawable" name="abc_ic_star_black_16dp" id="0x7f070025" />
    <public type="drawable" name="abc_ic_star_black_36dp" id="0x7f070026" />
    <public type="drawable" name="abc_ic_star_black_48dp" id="0x7f070027" />
    <public type="drawable" name="abc_ic_star_half_black_16dp" id="0x7f070028" />
    <public type="drawable" name="abc_ic_star_half_black_36dp" id="0x7f070029" />
    <public type="drawable" name="abc_ic_star_half_black_48dp" id="0x7f07002a" />
    <public type="drawable" name="abc_ic_voice_search_api_material" id="0x7f07002b" />
    <public type="drawable" name="abc_item_background_holo_dark" id="0x7f07002c" />
    <public type="drawable" name="abc_item_background_holo_light" id="0x7f07002d" />
    <public type="drawable" name="abc_list_divider_material" id="0x7f07002e" />
    <public type="drawable" name="abc_list_divider_mtrl_alpha" id="0x7f07002f" />
    <public type="drawable" name="abc_list_focused_holo" id="0x7f070030" />
    <public type="drawable" name="abc_list_longpressed_holo" id="0x7f070031" />
    <public type="drawable" name="abc_list_pressed_holo_dark" id="0x7f070032" />
    <public type="drawable" name="abc_list_pressed_holo_light" id="0x7f070033" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_dark" id="0x7f070034" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_light" id="0x7f070035" />
    <public type="drawable" name="abc_list_selector_disabled_holo_dark" id="0x7f070036" />
    <public type="drawable" name="abc_list_selector_disabled_holo_light" id="0x7f070037" />
    <public type="drawable" name="abc_list_selector_holo_dark" id="0x7f070038" />
    <public type="drawable" name="abc_list_selector_holo_light" id="0x7f070039" />
    <public type="drawable" name="abc_menu_hardkey_panel_mtrl_mult" id="0x7f07003a" />
    <public type="drawable" name="abc_popup_background_mtrl_mult" id="0x7f07003b" />
    <public type="drawable" name="abc_ratingbar_indicator_material" id="0x7f07003c" />
    <public type="drawable" name="abc_ratingbar_material" id="0x7f07003d" />
    <public type="drawable" name="abc_ratingbar_small_material" id="0x7f07003e" />
    <public type="drawable" name="abc_scrubber_control_off_mtrl_alpha" id="0x7f07003f" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000" id="0x7f070040" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005" id="0x7f070041" />
    <public type="drawable" name="abc_scrubber_primary_mtrl_alpha" id="0x7f070042" />
    <public type="drawable" name="abc_scrubber_track_mtrl_alpha" id="0x7f070043" />
    <public type="drawable" name="abc_seekbar_thumb_material" id="0x7f070044" />
    <public type="drawable" name="abc_seekbar_tick_mark_material" id="0x7f070045" />
    <public type="drawable" name="abc_seekbar_track_material" id="0x7f070046" />
    <public type="drawable" name="abc_spinner_mtrl_am_alpha" id="0x7f070047" />
    <public type="drawable" name="abc_spinner_textfield_background_material" id="0x7f070048" />
    <public type="drawable" name="abc_switch_thumb_material" id="0x7f070049" />
    <public type="drawable" name="abc_switch_track_mtrl_alpha" id="0x7f07004a" />
    <public type="drawable" name="abc_tab_indicator_material" id="0x7f07004b" />
    <public type="drawable" name="abc_tab_indicator_mtrl_alpha" id="0x7f07004c" />
    <public type="drawable" name="abc_text_cursor_material" id="0x7f07004d" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl_dark" id="0x7f07004e" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl_light" id="0x7f07004f" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl_dark" id="0x7f070050" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl_light" id="0x7f070051" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl_dark" id="0x7f070052" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl_light" id="0x7f070053" />
    <public type="drawable" name="abc_textfield_activated_mtrl_alpha" id="0x7f070054" />
    <public type="drawable" name="abc_textfield_default_mtrl_alpha" id="0x7f070055" />
    <public type="drawable" name="abc_textfield_search_activated_mtrl_alpha" id="0x7f070056" />
    <public type="drawable" name="abc_textfield_search_default_mtrl_alpha" id="0x7f070057" />
    <public type="drawable" name="abc_textfield_search_material" id="0x7f070058" />
    <public type="drawable" name="abc_vector_test" id="0x7f070059" />
    <public type="drawable" name="avd_hide_password" id="0x7f07005a" />
    <public type="drawable" name="avd_show_password" id="0x7f07005b" />
    <public type="drawable" name="cursor_bg" id="0x7f07005c" />
    <public type="drawable" name="design_bottom_navigation_item_background" id="0x7f07005d" />
    <public type="drawable" name="design_fab_background" id="0x7f07005e" />
    <public type="drawable" name="design_ic_visibility" id="0x7f07005f" />
    <public type="drawable" name="design_ic_visibility_off" id="0x7f070060" />
    <public type="drawable" name="design_password_eye" id="0x7f070061" />
    <public type="drawable" name="design_snackbar_background" id="0x7f070062" />
    <public type="drawable" name="edit_text_bg" id="0x7f070063" />
    <public type="drawable" name="empty_drawable" id="0x7f070064" />
    <public type="drawable" name="exo_controls_fastforward" id="0x7f070065" />
    <public type="drawable" name="exo_controls_fullscreen_enter" id="0x7f070066" />
    <public type="drawable" name="exo_controls_fullscreen_exit" id="0x7f070067" />
    <public type="drawable" name="exo_controls_next" id="0x7f070068" />
    <public type="drawable" name="exo_controls_pause" id="0x7f070069" />
    <public type="drawable" name="exo_controls_play" id="0x7f07006a" />
    <public type="drawable" name="exo_controls_previous" id="0x7f07006b" />
    <public type="drawable" name="exo_controls_repeat_all" id="0x7f07006c" />
    <public type="drawable" name="exo_controls_repeat_off" id="0x7f07006d" />
    <public type="drawable" name="exo_controls_repeat_one" id="0x7f07006e" />
    <public type="drawable" name="exo_controls_rewind" id="0x7f07006f" />
    <public type="drawable" name="exo_controls_shuffle" id="0x7f070070" />
    <public type="drawable" name="exo_edit_mode_logo" id="0x7f070071" />
    <public type="drawable" name="exo_icon_fastforward" id="0x7f070072" />
    <public type="drawable" name="exo_icon_next" id="0x7f070073" />
    <public type="drawable" name="exo_icon_pause" id="0x7f070074" />
    <public type="drawable" name="exo_icon_play" id="0x7f070075" />
    <public type="drawable" name="exo_icon_previous" id="0x7f070076" />
    <public type="drawable" name="exo_icon_rewind" id="0x7f070077" />
    <public type="drawable" name="exo_icon_stop" id="0x7f070078" />
    <public type="drawable" name="exo_notification_fastforward" id="0x7f070079" />
    <public type="drawable" name="exo_notification_next" id="0x7f07007a" />
    <public type="drawable" name="exo_notification_pause" id="0x7f07007b" />
    <public type="drawable" name="exo_notification_play" id="0x7f07007c" />
    <public type="drawable" name="exo_notification_previous" id="0x7f07007d" />
    <public type="drawable" name="exo_notification_rewind" id="0x7f07007e" />
    <public type="drawable" name="exo_notification_small_icon" id="0x7f07007f" />
    <public type="drawable" name="exo_notification_stop" id="0x7f070080" />
    <public type="drawable" name="ic_1080" id="0x7f070081" />
    <public type="drawable" name="ic_360" id="0x7f070082" />
    <public type="drawable" name="ic_720" id="0x7f070083" />
    <public type="drawable" name="ic_app" id="0x7f070084" />
    <public type="drawable" name="ic_back" id="0x7f070085" />
    <public type="drawable" name="ic_back_white" id="0x7f070086" />
    <public type="drawable" name="ic_buy_white" id="0x7f070087" />
    <public type="drawable" name="ic_check_white_24dp" id="0x7f070088" />
    <public type="drawable" name="ic_clear_white_24dp" id="0x7f070089" />
    <public type="drawable" name="ic_close" id="0x7f07008a" />
    <public type="drawable" name="ic_comment" id="0x7f07008b" />
    <public type="drawable" name="ic_discover" id="0x7f07008c" />
    <public type="drawable" name="ic_discover_default" id="0x7f07008d" />
    <public type="drawable" name="ic_download" id="0x7f07008e" />
    <public type="drawable" name="ic_download_white" id="0x7f07008f" />
    <public type="drawable" name="ic_error_outline_white_24dp" id="0x7f070090" />
    <public type="drawable" name="ic_fast_move" id="0x7f070091" />
    <public type="drawable" name="ic_first" id="0x7f070092" />
    <public type="drawable" name="ic_full_enter" id="0x7f070093" />
    <public type="drawable" name="ic_full_exit" id="0x7f070094" />
    <public type="drawable" name="ic_info" id="0x7f070095" />
    <public type="drawable" name="ic_info_outline_white_24dp" id="0x7f070096" />
    <public type="drawable" name="ic_last" id="0x7f070097" />
    <public type="drawable" name="ic_like" id="0x7f070098" />
    <public type="drawable" name="ic_like_white" id="0x7f070099" />
    <public type="drawable" name="ic_liked" id="0x7f07009a" />
    <public type="drawable" name="ic_link" id="0x7f07009b" />
    <public type="drawable" name="ic_lock" id="0x7f07009c" />
    <public type="drawable" name="ic_me" id="0x7f07009d" />
    <public type="drawable" name="ic_me_active" id="0x7f07009e" />
    <public type="drawable" name="ic_me_default" id="0x7f07009f" />
    <public type="drawable" name="ic_me_gray" id="0x7f0700a0" />
    <public type="drawable" name="ic_me_white" id="0x7f0700a1" />
    <public type="drawable" name="ic_more" id="0x7f0700a2" />
    <public type="drawable" name="ic_mtrl_chip_checked_black" id="0x7f0700a3" />
    <public type="drawable" name="ic_mtrl_chip_checked_circle" id="0x7f0700a4" />
    <public type="drawable" name="ic_mtrl_chip_close_circle" id="0x7f0700a5" />
    <public type="drawable" name="ic_nav" id="0x7f0700a6" />
    <public type="drawable" name="ic_new" id="0x7f0700a7" />
    <public type="drawable" name="ic_new_active" id="0x7f0700a8" />
    <public type="drawable" name="ic_new_default" id="0x7f0700a9" />
    <public type="drawable" name="ic_new_gray" id="0x7f0700aa" />
    <public type="drawable" name="ic_new_white" id="0x7f0700ab" />
    <public type="drawable" name="ic_next" id="0x7f0700ac" />
    <public type="drawable" name="ic_pasue_fill_white" id="0x7f0700ad" />
    <public type="drawable" name="ic_pause" id="0x7f0700ae" />
    <public type="drawable" name="ic_pause2" id="0x7f0700af" />
    <public type="drawable" name="ic_play" id="0x7f0700b0" />
    <public type="drawable" name="ic_play2" id="0x7f0700b1" />
    <public type="drawable" name="ic_play_fill_white" id="0x7f0700b2" />
    <public type="drawable" name="ic_play_white" id="0x7f0700b3" />
    <public type="drawable" name="ic_share" id="0x7f0700b4" />
    <public type="drawable" name="ic_speed_0_5" id="0x7f0700b5" />
    <public type="drawable" name="ic_speed_0_7" id="0x7f0700b6" />
    <public type="drawable" name="ic_speed_1" id="0x7f0700b7" />
    <public type="drawable" name="ic_speed_1_2" id="0x7f0700b8" />
    <public type="drawable" name="ic_speed_1_5" id="0x7f0700b9" />
    <public type="drawable" name="ic_speed_2" id="0x7f0700ba" />
    <public type="drawable" name="ic_sun" id="0x7f0700bb" />
    <public type="drawable" name="ic_unlock" id="0x7f0700bc" />
    <public type="drawable" name="ic_volume" id="0x7f0700bd" />
    <public type="drawable" name="ic_wechat" id="0x7f0700be" />
    <public type="drawable" name="ic_wechat_friends" id="0x7f0700bf" />
    <public type="drawable" name="ic_wechat_momnet" id="0x7f0700c0" />
    <public type="drawable" name="isb_indicator_rounded_corners" id="0x7f0700c1" />
    <public type="drawable" name="isb_indicator_square_corners" id="0x7f0700c2" />
    <public type="drawable" name="keyword_select" id="0x7f0700c3" />
    <public type="drawable" name="lock" id="0x7f0700c4" />
    <public type="drawable" name="login" id="0x7f0700c5" />
    <public type="drawable" name="logo_header" id="0x7f0700c6" />
    <public type="drawable" name="logo_tiyatir" id="0x7f0700c7" />
    <public type="drawable" name="movie_light" id="0x7f0700c8" />
    <public type="drawable" name="mtrl_snackbar_background" id="0x7f0700c9" />
    <public type="drawable" name="mtrl_tabs_default_indicator" id="0x7f0700ca" />
    <public type="drawable" name="music_holder_1" id="0x7f0700cb" />
    <public type="drawable" name="navigation_empty_icon" id="0x7f0700cc" />
    <public type="drawable" name="notification_action_background" id="0x7f0700cd" />
    <public type="drawable" name="notification_bg" id="0x7f0700ce" />
    <public type="drawable" name="notification_bg_low" id="0x7f0700cf" />
    <public type="drawable" name="notification_bg_low_normal" id="0x7f0700d0" />
    <public type="drawable" name="notification_bg_low_pressed" id="0x7f0700d1" />
    <public type="drawable" name="notification_bg_normal" id="0x7f0700d2" />
    <public type="drawable" name="notification_bg_normal_pressed" id="0x7f0700d3" />
    <public type="drawable" name="notification_icon_background" id="0x7f0700d4" />
    <public type="drawable" name="notification_template_icon_bg" id="0x7f0700d5" />
    <public type="drawable" name="notification_template_icon_low_bg" id="0x7f0700d6" />
    <public type="drawable" name="notification_tile_bg" id="0x7f0700d7" />
    <public type="drawable" name="notify_panel_notification_icon_bg" id="0x7f0700d8" />
    <public type="drawable" name="ok" id="0x7f0700d9" />
    <public type="drawable" name="qmui_dialog_bg" id="0x7f0700da" />
    <public type="drawable" name="qmui_divider" id="0x7f0700db" />
    <public type="drawable" name="qmui_divider_bottom_bitmap" id="0x7f0700dc" />
    <public type="drawable" name="qmui_divider_top_bitmap" id="0x7f0700dd" />
    <public type="drawable" name="qmui_edittext_bg_border_bottom" id="0x7f0700de" />
    <public type="drawable" name="qmui_icon_checkbox_checked" id="0x7f0700df" />
    <public type="drawable" name="qmui_icon_checkbox_normal" id="0x7f0700e0" />
    <public type="drawable" name="qmui_icon_checkmark" id="0x7f0700e1" />
    <public type="drawable" name="qmui_icon_chevron" id="0x7f0700e2" />
    <public type="drawable" name="qmui_icon_notify_done" id="0x7f0700e3" />
    <public type="drawable" name="qmui_icon_notify_error" id="0x7f0700e4" />
    <public type="drawable" name="qmui_icon_notify_info" id="0x7f0700e5" />
    <public type="drawable" name="qmui_icon_scroll_bar" id="0x7f0700e6" />
    <public type="drawable" name="qmui_icon_switch_checked" id="0x7f0700e7" />
    <public type="drawable" name="qmui_icon_switch_normal" id="0x7f0700e8" />
    <public type="drawable" name="qmui_icon_tip_new" id="0x7f0700e9" />
    <public type="drawable" name="qmui_icon_topbar_back" id="0x7f0700ea" />
    <public type="drawable" name="qmui_list_item_bg_with_border_bottom" id="0x7f0700eb" />
    <public type="drawable" name="qmui_list_item_bg_with_border_bottom_inset" id="0x7f0700ec" />
    <public type="drawable" name="qmui_list_item_bg_with_border_bottom_inset_left" id="0x7f0700ed" />
    <public type="drawable" name="qmui_list_item_bg_with_border_bottom_inset_left_pressed" id="0x7f0700ee" />
    <public type="drawable" name="qmui_list_item_bg_with_border_bottom_inset_pressed" id="0x7f0700ef" />
    <public type="drawable" name="qmui_list_item_bg_with_border_bottom_pressed" id="0x7f0700f0" />
    <public type="drawable" name="qmui_list_item_bg_with_border_top" id="0x7f0700f1" />
    <public type="drawable" name="qmui_list_item_bg_with_border_top_inset" id="0x7f0700f2" />
    <public type="drawable" name="qmui_list_item_bg_with_border_top_inset_left" id="0x7f0700f3" />
    <public type="drawable" name="qmui_list_item_bg_with_border_top_inset_left_pressed" id="0x7f0700f4" />
    <public type="drawable" name="qmui_list_item_bg_with_border_top_inset_pressed" id="0x7f0700f5" />
    <public type="drawable" name="qmui_list_item_bg_with_border_top_pressed" id="0x7f0700f6" />
    <public type="drawable" name="qmui_list_item_bg_with_double_border" id="0x7f0700f7" />
    <public type="drawable" name="qmui_list_item_bg_with_double_border_pressed" id="0x7f0700f8" />
    <public type="drawable" name="qmui_popup_arrow_down" id="0x7f0700f9" />
    <public type="drawable" name="qmui_popup_arrow_up" id="0x7f0700fa" />
    <public type="drawable" name="qmui_popup_bg" id="0x7f0700fb" />
    <public type="drawable" name="qmui_s_checkbox" id="0x7f0700fc" />
    <public type="drawable" name="qmui_s_dialog_check_mark" id="0x7f0700fd" />
    <public type="drawable" name="qmui_s_icon_switch" id="0x7f0700fe" />
    <public type="drawable" name="qmui_s_list_item_bg_with_border_bottom" id="0x7f0700ff" />
    <public type="drawable" name="qmui_s_list_item_bg_with_border_bottom_inset" id="0x7f070100" />
    <public type="drawable" name="qmui_s_list_item_bg_with_border_bottom_inset_left" id="0x7f070101" />
    <public type="drawable" name="qmui_s_list_item_bg_with_border_double" id="0x7f070102" />
    <public type="drawable" name="qmui_s_list_item_bg_with_border_none" id="0x7f070103" />
    <public type="drawable" name="qmui_s_list_item_bg_with_border_top" id="0x7f070104" />
    <public type="drawable" name="qmui_s_list_item_bg_with_border_top_inset" id="0x7f070105" />
    <public type="drawable" name="qmui_s_list_item_bg_with_border_top_inset_left" id="0x7f070106" />
    <public type="drawable" name="qmui_s_switch_thumb" id="0x7f070107" />
    <public type="drawable" name="qmui_s_switch_track" id="0x7f070108" />
    <public type="drawable" name="qmui_sign_count_view_bg" id="0x7f070109" />
    <public type="drawable" name="qmui_switch_thumb" id="0x7f07010a" />
    <public type="drawable" name="qmui_switch_thumb_checked" id="0x7f07010b" />
    <public type="drawable" name="qmui_switch_track" id="0x7f07010c" />
    <public type="drawable" name="qmui_switch_track_checked" id="0x7f07010d" />
    <public type="drawable" name="qmui_tip_dialog_bg" id="0x7f07010e" />
    <public type="drawable" name="qmui_tips_point" id="0x7f07010f" />
    <public type="drawable" name="splash_night" id="0x7f070110" />
    <public type="drawable" name="tiyatir_logo" id="0x7f070111" />
    <public type="drawable" name="tiyatir_new_logo" id="0x7f070112" />
    <public type="drawable" name="tiyatir_new_logo_2" id="0x7f070113" />
    <public type="drawable" name="toast_frame" id="0x7f070114" />
    <public type="drawable" name="tooltip_frame_dark" id="0x7f070115" />
    <public type="drawable" name="tooltip_frame_light" id="0x7f070116" />
    <public type="drawable" name="tranparent_ripple" id="0x7f070117" />
    <public type="drawable" name="tv_item_selector" id="0x7f070118" />
    <public type="drawable" name="tv_item_selector_6" id="0x7f070119" />
    <public type="drawable" name="tv_item_selector_primary" id="0x7f07011a" />
    <public type="drawable" name="tv_keyword_backgorund" id="0x7f07011b" />
    <public type="drawable" name="unlock" id="0x7f07011c" />
    <public type="drawable" name="utils_toast_bg" id="0x7f07011d" />
    <public type="drawable" name="video_back" id="0x7f07011e" />
    <public type="drawable" name="video_backward_icon" id="0x7f07011f" />
    <public type="drawable" name="video_brightness_6_white_36dp" id="0x7f070120" />
    <public type="drawable" name="video_click_error_selector" id="0x7f070121" />
    <public type="drawable" name="video_click_pause_selector" id="0x7f070122" />
    <public type="drawable" name="video_click_play_selector" id="0x7f070123" />
    <public type="drawable" name="video_dialog_progress" id="0x7f070124" />
    <public type="drawable" name="video_dialog_progress_bg" id="0x7f070125" />
    <public type="drawable" name="video_enlarge" id="0x7f070126" />
    <public type="drawable" name="video_error_normal" id="0x7f070127" />
    <public type="drawable" name="video_error_pressed" id="0x7f070128" />
    <public type="drawable" name="video_forward_icon" id="0x7f070129" />
    <public type="drawable" name="video_jump_btn_bg" id="0x7f07012a" />
    <public type="drawable" name="video_loading" id="0x7f07012b" />
    <public type="drawable" name="video_loading_bg" id="0x7f07012c" />
    <public type="drawable" name="video_pause_normal" id="0x7f07012d" />
    <public type="drawable" name="video_pause_pressed" id="0x7f07012e" />
    <public type="drawable" name="video_play_normal" id="0x7f07012f" />
    <public type="drawable" name="video_play_pressed" id="0x7f070130" />
    <public type="drawable" name="video_progress" id="0x7f070131" />
    <public type="drawable" name="video_seek_progress" id="0x7f070132" />
    <public type="drawable" name="video_seek_thumb" id="0x7f070133" />
    <public type="drawable" name="video_seek_thumb_normal" id="0x7f070134" />
    <public type="drawable" name="video_seek_thumb_pressed" id="0x7f070135" />
    <public type="drawable" name="video_shrink" id="0x7f070136" />
    <public type="drawable" name="video_small_close" id="0x7f070137" />
    <public type="drawable" name="video_title_bg" id="0x7f070138" />
    <public type="drawable" name="video_volume_icon" id="0x7f070139" />
    <public type="drawable" name="video_volume_progress_bg" id="0x7f07013a" />
    <public type="drawable" name="vip_tip" id="0x7f07013b" />
    <public type="id" name="ALT" id="0x7f080000" />
    <public type="id" name="CTRL" id="0x7f080001" />
    <public type="id" name="FUNCTION" id="0x7f080002" />
    <public type="id" name="META" id="0x7f080003" />
    <public type="id" name="SHIFT" id="0x7f080004" />
    <public type="id" name="SYM" id="0x7f080005" />
    <public type="id" name="action0" id="0x7f080006" />
    <public type="id" name="action_bar" id="0x7f080007" />
    <public type="id" name="action_bar_activity_content" id="0x7f080008" />
    <public type="id" name="action_bar_container" id="0x7f080009" />
    <public type="id" name="action_bar_root" id="0x7f08000a" />
    <public type="id" name="action_bar_spinner" id="0x7f08000b" />
    <public type="id" name="action_bar_subtitle" id="0x7f08000c" />
    <public type="id" name="action_bar_title" id="0x7f08000d" />
    <public type="id" name="action_container" id="0x7f08000e" />
    <public type="id" name="action_context_bar" id="0x7f08000f" />
    <public type="id" name="action_divider" id="0x7f080010" />
    <public type="id" name="action_image" id="0x7f080011" />
    <public type="id" name="action_menu_divider" id="0x7f080012" />
    <public type="id" name="action_menu_presenter" id="0x7f080013" />
    <public type="id" name="action_mode_bar" id="0x7f080014" />
    <public type="id" name="action_mode_bar_stub" id="0x7f080015" />
    <public type="id" name="action_mode_close_button" id="0x7f080016" />
    <public type="id" name="action_text" id="0x7f080017" />
    <public type="id" name="actions" id="0x7f080018" />
    <public type="id" name="activity_chooser_view_content" id="0x7f080019" />
    <public type="id" name="ad_full_id" id="0x7f08001a" />
    <public type="id" name="ad_small_id" id="0x7f08001b" />
    <public type="id" name="ad_time" id="0x7f08001c" />
    <public type="id" name="add" id="0x7f08001d" />
    <public type="id" name="alertTitle" id="0x7f08001e" />
    <public type="id" name="all" id="0x7f08001f" />
    <public type="id" name="always" id="0x7f080020" />
    <public type="id" name="anchor_bottom" id="0x7f080021" />
    <public type="id" name="anchor_top" id="0x7f080022" />
    <public type="id" name="app_video_brightness" id="0x7f080023" />
    <public type="id" name="app_video_brightness_box" id="0x7f080024" />
    <public type="id" name="app_video_brightness_icon" id="0x7f080025" />
    <public type="id" name="arrow_down" id="0x7f080026" />
    <public type="id" name="arrow_up" id="0x7f080027" />
    <public type="id" name="async" id="0x7f080028" />
    <public type="id" name="auto" id="0x7f080029" />
    <public type="id" name="back" id="0x7f08002a" />
    <public type="id" name="back_tiny" id="0x7f08002b" />
    <public type="id" name="barrier" id="0x7f08002c" />
    <public type="id" name="beginning" id="0x7f08002d" />
    <public type="id" name="blocking" id="0x7f08002e" />
    <public type="id" name="bottom" id="0x7f08002f" />
    <public type="id" name="bottom_dialog_list_item_img" id="0x7f080030" />
    <public type="id" name="bottom_dialog_list_item_mark" id="0x7f080031" />
    <public type="id" name="bottom_dialog_list_item_mark_view_stub" id="0x7f080032" />
    <public type="id" name="bottom_dialog_list_item_point" id="0x7f080033" />
    <public type="id" name="bottom_dialog_list_item_title" id="0x7f080034" />
    <public type="id" name="bottom_progressbar" id="0x7f080035" />
    <public type="id" name="bottom_sheet_button_container" id="0x7f080036" />
    <public type="id" name="bottom_sheet_close_button" id="0x7f080037" />
    <public type="id" name="bottom_sheet_first_linear_layout" id="0x7f080038" />
    <public type="id" name="bottom_sheet_second_linear_layout" id="0x7f080039" />
    <public type="id" name="box" id="0x7f08003a" />
    <public type="id" name="buttonPanel" id="0x7f08003b" />
    <public type="id" name="cancel_action" id="0x7f08003c" />
    <public type="id" name="center" id="0x7f08003d" />
    <public type="id" name="center_horizontal" id="0x7f08003e" />
    <public type="id" name="center_vertical" id="0x7f08003f" />
    <public type="id" name="chains" id="0x7f080040" />
    <public type="id" name="checkbox" id="0x7f080041" />
    <public type="id" name="chevron" id="0x7f080042" />
    <public type="id" name="chronometer" id="0x7f080043" />
    <public type="id" name="circular_bubble" id="0x7f080044" />
    <public type="id" name="clip_horizontal" id="0x7f080045" />
    <public type="id" name="clip_vertical" id="0x7f080046" />
    <public type="id" name="collapseActionView" id="0x7f080047" />
    <public type="id" name="container" id="0x7f080048" />
    <public type="id" name="content" id="0x7f080049" />
    <public type="id" name="contentPanel" id="0x7f08004a" />
    <public type="id" name="contentWrap" id="0x7f08004b" />
    <public type="id" name="coordinator" id="0x7f08004c" />
    <public type="id" name="current" id="0x7f08004d" />
    <public type="id" name="current_scene" id="0x7f08004e" />
    <public type="id" name="custom" id="0x7f08004f" />
    <public type="id" name="customPanel" id="0x7f080050" />
    <public type="id" name="decor_content_parent" id="0x7f080051" />
    <public type="id" name="default_activity_button" id="0x7f080052" />
    <public type="id" name="design_bottom_sheet" id="0x7f080053" />
    <public type="id" name="design_menu_item_action_area" id="0x7f080054" />
    <public type="id" name="design_menu_item_action_area_stub" id="0x7f080055" />
    <public type="id" name="design_menu_item_text" id="0x7f080056" />
    <public type="id" name="design_navigation_view" id="0x7f080057" />
    <public type="id" name="dialog" id="0x7f080058" />
    <public type="id" name="dialog_wrapper" id="0x7f080059" />
    <public type="id" name="dimensions" id="0x7f08005a" />
    <public type="id" name="direct" id="0x7f08005b" />
    <public type="id" name="disableHome" id="0x7f08005c" />
    <public type="id" name="disposable" id="0x7f08005d" />
    <public type="id" name="divider" id="0x7f08005e" />
    <public type="id" name="duration_image_tip" id="0x7f08005f" />
    <public type="id" name="duration_progressbar" id="0x7f080060" />
    <public type="id" name="edit_query" id="0x7f080061" />
    <public type="id" name="empty_view_button" id="0x7f080062" />
    <public type="id" name="empty_view_detail" id="0x7f080063" />
    <public type="id" name="empty_view_loading" id="0x7f080064" />
    <public type="id" name="empty_view_title" id="0x7f080065" />
    <public type="id" name="end" id="0x7f080066" />
    <public type="id" name="end_padder" id="0x7f080067" />
    <public type="id" name="enterAlways" id="0x7f080068" />
    <public type="id" name="enterAlwaysCollapsed" id="0x7f080069" />
    <public type="id" name="exitUntilCollapsed" id="0x7f08006a" />
    <public type="id" name="exo_artwork" id="0x7f08006b" />
    <public type="id" name="exo_buffering" id="0x7f08006c" />
    <public type="id" name="exo_content_frame" id="0x7f08006d" />
    <public type="id" name="exo_controller" id="0x7f08006e" />
    <public type="id" name="exo_controller_placeholder" id="0x7f08006f" />
    <public type="id" name="exo_duration" id="0x7f080070" />
    <public type="id" name="exo_error_message" id="0x7f080071" />
    <public type="id" name="exo_ffwd" id="0x7f080072" />
    <public type="id" name="exo_next" id="0x7f080073" />
    <public type="id" name="exo_overlay" id="0x7f080074" />
    <public type="id" name="exo_pause" id="0x7f080075" />
    <public type="id" name="exo_play" id="0x7f080076" />
    <public type="id" name="exo_position" id="0x7f080077" />
    <public type="id" name="exo_prev" id="0x7f080078" />
    <public type="id" name="exo_progress" id="0x7f080079" />
    <public type="id" name="exo_repeat_toggle" id="0x7f08007a" />
    <public type="id" name="exo_rew" id="0x7f08007b" />
    <public type="id" name="exo_shuffle" id="0x7f08007c" />
    <public type="id" name="exo_shutter" id="0x7f08007d" />
    <public type="id" name="exo_subtitles" id="0x7f08007e" />
    <public type="id" name="exo_track_selection_view" id="0x7f08007f" />
    <public type="id" name="expand_activities_button" id="0x7f080080" />
    <public type="id" name="expanded_menu" id="0x7f080081" />
    <public type="id" name="fade_in" id="0x7f080082" />
    <public type="id" name="fade_in_out" id="0x7f080083" />
    <public type="id" name="fade_out" id="0x7f080084" />
    <public type="id" name="fill" id="0x7f080085" />
    <public type="id" name="fill_horizontal" id="0x7f080086" />
    <public type="id" name="fill_vertical" id="0x7f080087" />
    <public type="id" name="filled" id="0x7f080088" />
    <public type="id" name="fit" id="0x7f080089" />
    <public type="id" name="fixed" id="0x7f08008a" />
    <public type="id" name="fixed_height" id="0x7f08008b" />
    <public type="id" name="fixed_width" id="0x7f08008c" />
    <public type="id" name="forever" id="0x7f08008d" />
    <public type="id" name="full_id" id="0x7f08008e" />
    <public type="id" name="fullscreen" id="0x7f08008f" />
    <public type="id" name="ghost_view" id="0x7f080090" />
    <public type="id" name="gone" id="0x7f080091" />
    <public type="id" name="grid_item_image" id="0x7f080092" />
    <public type="id" name="grid_item_subscript" id="0x7f080093" />
    <public type="id" name="grid_item_title" id="0x7f080094" />
    <public type="id" name="group_divider" id="0x7f080095" />
    <public type="id" name="group_layouttransition_backup" id="0x7f080096" />
    <public type="id" name="group_list_item_accessoryView" id="0x7f080097" />
    <public type="id" name="group_list_item_detailTextView" id="0x7f080098" />
    <public type="id" name="group_list_item_imageView" id="0x7f080099" />
    <public type="id" name="group_list_item_space" id="0x7f08009a" />
    <public type="id" name="group_list_item_textContainer" id="0x7f08009b" />
    <public type="id" name="group_list_item_textView" id="0x7f08009c" />
    <public type="id" name="group_list_item_tips_dot" id="0x7f08009d" />
    <public type="id" name="group_list_item_tips_new" id="0x7f08009e" />
    <public type="id" name="group_list_section_header_textView" id="0x7f08009f" />
    <public type="id" name="groups" id="0x7f0800a0" />
    <public type="id" name="home" id="0x7f0800a1" />
    <public type="id" name="homeAsUp" id="0x7f0800a2" />
    <public type="id" name="horizontal" id="0x7f0800a3" />
    <public type="id" name="icon" id="0x7f0800a4" />
    <public type="id" name="icon_group" id="0x7f0800a5" />
    <public type="id" name="ifRoom" id="0x7f0800a6" />
    <public type="id" name="image" id="0x7f0800a7" />
    <public type="id" name="incompressible" id="0x7f0800a8" />
    <public type="id" name="indicator_arrow" id="0x7f0800a9" />
    <public type="id" name="indicator_container" id="0x7f0800aa" />
    <public type="id" name="info" id="0x7f0800ab" />
    <public type="id" name="invisible" id="0x7f0800ac" />
    <public type="id" name="isb_progress" id="0x7f0800ad" />
    <public type="id" name="italic" id="0x7f0800ae" />
    <public type="id" name="item_touch_helper_previous_elevation" id="0x7f0800af" />
    <public type="id" name="jump_ad" id="0x7f0800b0" />
    <public type="id" name="labeled" id="0x7f0800b1" />
    <public type="id" name="largeLabel" id="0x7f0800b2" />
    <public type="id" name="layout_bottom" id="0x7f0800b3" />
    <public type="id" name="layout_top" id="0x7f0800b4" />
    <public type="id" name="left" id="0x7f0800b5" />
    <public type="id" name="left_center" id="0x7f0800b6" />
    <public type="id" name="line1" id="0x7f0800b7" />
    <public type="id" name="line3" id="0x7f0800b8" />
    <public type="id" name="listMode" id="0x7f0800b9" />
    <public type="id" name="list_item" id="0x7f0800ba" />
    <public type="id" name="listview" id="0x7f0800bb" />
    <public type="id" name="loading" id="0x7f0800bc" />
    <public type="id" name="lock_screen" id="0x7f0800bd" />
    <public type="id" name="masked" id="0x7f0800be" />
    <public type="id" name="media_actions" id="0x7f0800bf" />
    <public type="id" name="message" id="0x7f0800c0" />
    <public type="id" name="middle" id="0x7f0800c1" />
    <public type="id" name="mini" id="0x7f0800c2" />
    <public type="id" name="mini_content_protection" id="0x7f0800c3" />
    <public type="id" name="mode_in" id="0x7f0800c4" />
    <public type="id" name="mode_out" id="0x7f0800c5" />
    <public type="id" name="monospace" id="0x7f0800c6" />
    <public type="id" name="mtrl_child_content_container" id="0x7f0800c7" />
    <public type="id" name="mtrl_internal_children_alpha_tag" id="0x7f0800c8" />
    <public type="id" name="multiply" id="0x7f0800c9" />
    <public type="id" name="navigation_header_container" id="0x7f0800ca" />
    <public type="id" name="never" id="0x7f0800cb" />
    <public type="id" name="none" id="0x7f0800cc" />
    <public type="id" name="normal" id="0x7f0800cd" />
    <public type="id" name="notification_background" id="0x7f0800ce" />
    <public type="id" name="notification_main_column" id="0x7f0800cf" />
    <public type="id" name="notification_main_column_container" id="0x7f0800d0" />
    <public type="id" name="one" id="0x7f0800d1" />
    <public type="id" name="outline" id="0x7f0800d2" />
    <public type="id" name="oval" id="0x7f0800d3" />
    <public type="id" name="overlay_layout_params_backup" id="0x7f0800d4" />
    <public type="id" name="overlay_view" id="0x7f0800d5" />
    <public type="id" name="packed" id="0x7f0800d6" />
    <public type="id" name="parallax" id="0x7f0800d7" />
    <public type="id" name="parent" id="0x7f0800d8" />
    <public type="id" name="parentMatrix" id="0x7f0800d9" />
    <public type="id" name="parentPanel" id="0x7f0800da" />
    <public type="id" name="parent_matrix" id="0x7f0800db" />
    <public type="id" name="percent" id="0x7f0800dc" />
    <public type="id" name="pin" id="0x7f0800dd" />
    <public type="id" name="preview_layout" id="0x7f0800de" />
    <public type="id" name="progress" id="0x7f0800df" />
    <public type="id" name="progress_circular" id="0x7f0800e0" />
    <public type="id" name="progress_horizontal" id="0x7f0800e1" />
    <public type="id" name="qmui_dialog_edit_input" id="0x7f0800e2" />
    <public type="id" name="qmui_dialog_edit_right_icon" id="0x7f0800e3" />
    <public type="id" name="qmui_tab_segment_item_id" id="0x7f0800e4" />
    <public type="id" name="qmui_topbar_item_left_back" id="0x7f0800e5" />
    <public type="id" name="qmui_view_can_not_cache_tag" id="0x7f0800e6" />
    <public type="id" name="qmui_view_offset_helper" id="0x7f0800e7" />
    <public type="id" name="qmui_window_inset_keyboard_area_consumer" id="0x7f0800e8" />
    <public type="id" name="radio" id="0x7f0800e9" />
    <public type="id" name="rectangle" id="0x7f0800ea" />
    <public type="id" name="right" id="0x7f0800eb" />
    <public type="id" name="right_icon" id="0x7f0800ec" />
    <public type="id" name="right_side" id="0x7f0800ed" />
    <public type="id" name="rounded_rectangle" id="0x7f0800ee" />
    <public type="id" name="runningTransitions" id="0x7f0800ef" />
    <public type="id" name="sans" id="0x7f0800f0" />
    <public type="id" name="save_image_matrix" id="0x7f0800f1" />
    <public type="id" name="save_non_transition_alpha" id="0x7f0800f2" />
    <public type="id" name="save_scale_type" id="0x7f0800f3" />
    <public type="id" name="scene_layoutid_cache" id="0x7f0800f4" />
    <public type="id" name="screen" id="0x7f0800f5" />
    <public type="id" name="scroll" id="0x7f0800f6" />
    <public type="id" name="scrollIndicatorDown" id="0x7f0800f7" />
    <public type="id" name="scrollIndicatorUp" id="0x7f0800f8" />
    <public type="id" name="scrollView" id="0x7f0800f9" />
    <public type="id" name="scrollable" id="0x7f0800fa" />
    <public type="id" name="search_badge" id="0x7f0800fb" />
    <public type="id" name="search_bar" id="0x7f0800fc" />
    <public type="id" name="search_button" id="0x7f0800fd" />
    <public type="id" name="search_close_btn" id="0x7f0800fe" />
    <public type="id" name="search_edit_frame" id="0x7f0800ff" />
    <public type="id" name="search_go_btn" id="0x7f080100" />
    <public type="id" name="search_mag_icon" id="0x7f080101" />
    <public type="id" name="search_plate" id="0x7f080102" />
    <public type="id" name="search_src_text" id="0x7f080103" />
    <public type="id" name="search_voice_btn" id="0x7f080104" />
    <public type="id" name="select_dialog_listview" id="0x7f080105" />
    <public type="id" name="selected" id="0x7f080106" />
    <public type="id" name="sequential" id="0x7f080107" />
    <public type="id" name="serif" id="0x7f080108" />
    <public type="id" name="shortcut" id="0x7f080109" />
    <public type="id" name="showCustom" id="0x7f08010a" />
    <public type="id" name="showHome" id="0x7f08010b" />
    <public type="id" name="showTitle" id="0x7f08010c" />
    <public type="id" name="smallLabel" id="0x7f08010d" />
    <public type="id" name="small_close" id="0x7f08010e" />
    <public type="id" name="small_id" id="0x7f08010f" />
    <public type="id" name="snackbar_action" id="0x7f080110" />
    <public type="id" name="snackbar_text" id="0x7f080111" />
    <public type="id" name="snap" id="0x7f080112" />
    <public type="id" name="snapMargins" id="0x7f080113" />
    <public type="id" name="spacer" id="0x7f080114" />
    <public type="id" name="spherical_view" id="0x7f080115" />
    <public type="id" name="split_action_bar" id="0x7f080116" />
    <public type="id" name="spread" id="0x7f080117" />
    <public type="id" name="spread_inside" id="0x7f080118" />
    <public type="id" name="square" id="0x7f080119" />
    <public type="id" name="src_atop" id="0x7f08011a" />
    <public type="id" name="src_in" id="0x7f08011b" />
    <public type="id" name="src_over" id="0x7f08011c" />
    <public type="id" name="standard" id="0x7f08011d" />
    <public type="id" name="start" id="0x7f08011e" />
    <public type="id" name="status_bar_latest_event_content" id="0x7f08011f" />
    <public type="id" name="stretch" id="0x7f080120" />
    <public type="id" name="submenuarrow" id="0x7f080121" />
    <public type="id" name="submit_area" id="0x7f080122" />
    <public type="id" name="surface_container" id="0x7f080123" />
    <public type="id" name="surface_view" id="0x7f080124" />
    <public type="id" name="switcher" id="0x7f080125" />
    <public type="id" name="tabMode" id="0x7f080126" />
    <public type="id" name="tag_layout_helper_bg" id="0x7f080127" />
    <public type="id" name="tag_transition_group" id="0x7f080128" />
    <public type="id" name="tag_unhandled_key_event_manager" id="0x7f080129" />
    <public type="id" name="tag_unhandled_key_listeners" id="0x7f08012a" />
    <public type="id" name="text" id="0x7f08012b" />
    <public type="id" name="text2" id="0x7f08012c" />
    <public type="id" name="textSpacerNoButtons" id="0x7f08012d" />
    <public type="id" name="textSpacerNoTitle" id="0x7f08012e" />
    <public type="id" name="textStart" id="0x7f08012f" />
    <public type="id" name="text_input_password_toggle" id="0x7f080130" />
    <public type="id" name="textinput_counter" id="0x7f080131" />
    <public type="id" name="textinput_error" id="0x7f080132" />
    <public type="id" name="textinput_helper_text" id="0x7f080133" />
    <public type="id" name="texture_view" id="0x7f080134" />
    <public type="id" name="thumb" id="0x7f080135" />
    <public type="id" name="time" id="0x7f080136" />
    <public type="id" name="title" id="0x7f080137" />
    <public type="id" name="titleDividerNoCustom" id="0x7f080138" />
    <public type="id" name="title_template" id="0x7f080139" />
    <public type="id" name="toast_icon" id="0x7f08013a" />
    <public type="id" name="toast_root" id="0x7f08013b" />
    <public type="id" name="toast_text" id="0x7f08013c" />
    <public type="id" name="together" id="0x7f08013d" />
    <public type="id" name="top" id="0x7f08013e" />
    <public type="id" name="topPanel" id="0x7f08013f" />
    <public type="id" name="total" id="0x7f080140" />
    <public type="id" name="touch_outside" id="0x7f080141" />
    <public type="id" name="transitionAlpha" id="0x7f080142" />
    <public type="id" name="transitionName" id="0x7f080143" />
    <public type="id" name="transitionPosition" id="0x7f080144" />
    <public type="id" name="transitionTransform" id="0x7f080145" />
    <public type="id" name="transition_current_scene" id="0x7f080146" />
    <public type="id" name="transition_layout_save" id="0x7f080147" />
    <public type="id" name="transition_position" id="0x7f080148" />
    <public type="id" name="transition_scene_layoutid_cache" id="0x7f080149" />
    <public type="id" name="transition_transform" id="0x7f08014a" />
    <public type="id" name="tv_current" id="0x7f08014b" />
    <public type="id" name="tv_duration" id="0x7f08014c" />
    <public type="id" name="type_circle" id="0x7f08014d" />
    <public type="id" name="type_rect" id="0x7f08014e" />
    <public type="id" name="uniform" id="0x7f08014f" />
    <public type="id" name="unlabeled" id="0x7f080150" />
    <public type="id" name="up" id="0x7f080151" />
    <public type="id" name="useLogo" id="0x7f080152" />
    <public type="id" name="utvBottomIconView" id="0x7f080153" />
    <public type="id" name="utvLeftIconView" id="0x7f080154" />
    <public type="id" name="utvRightIconView" id="0x7f080155" />
    <public type="id" name="utvTopIconView" id="0x7f080156" />
    <public type="id" name="vertical" id="0x7f080157" />
    <public type="id" name="view_offset_helper" id="0x7f080158" />
    <public type="id" name="visible" id="0x7f080159" />
    <public type="id" name="volume_progressbar" id="0x7f08015a" />
    <public type="id" name="when_playing" id="0x7f08015b" />
    <public type="id" name="widget_container" id="0x7f08015c" />
    <public type="id" name="withText" id="0x7f08015d" />
    <public type="id" name="wrap" id="0x7f08015e" />
    <public type="id" name="wrap_content" id="0x7f08015f" />
    <public type="id" name="zoom" id="0x7f080160" />
    <public type="integer" name="abc_config_activityDefaultDur" id="0x7f090000" />
    <public type="integer" name="abc_config_activityShortDur" id="0x7f090001" />
    <public type="integer" name="app_bar_elevation_anim_duration" id="0x7f090002" />
    <public type="integer" name="bottom_sheet_slide_duration" id="0x7f090003" />
    <public type="integer" name="cancel_button_image_alpha" id="0x7f090004" />
    <public type="integer" name="config_tooltipAnimTime" id="0x7f090005" />
    <public type="integer" name="design_snackbar_text_max_lines" id="0x7f090006" />
    <public type="integer" name="design_tab_indicator_anim_duration_ms" id="0x7f090007" />
    <public type="integer" name="hide_password_duration" id="0x7f090008" />
    <public type="integer" name="mtrl_btn_anim_delay_ms" id="0x7f090009" />
    <public type="integer" name="mtrl_btn_anim_duration_ms" id="0x7f09000a" />
    <public type="integer" name="mtrl_chip_anim_duration" id="0x7f09000b" />
    <public type="integer" name="mtrl_tab_indicator_anim_duration_ms" id="0x7f09000c" />
    <public type="integer" name="show_password_duration" id="0x7f09000d" />
    <public type="integer" name="status_bar_notification_info_maxnum" id="0x7f09000e" />
    <public type="interpolator" name="mtrl_fast_out_linear_in" id="0x7f0a0000" />
    <public type="interpolator" name="mtrl_fast_out_slow_in" id="0x7f0a0001" />
    <public type="interpolator" name="mtrl_linear" id="0x7f0a0002" />
    <public type="interpolator" name="mtrl_linear_out_slow_in" id="0x7f0a0003" />
    <public type="layout" name="abc_action_bar_title_item" id="0x7f0b0000" />
    <public type="layout" name="abc_action_bar_up_container" id="0x7f0b0001" />
    <public type="layout" name="abc_action_menu_item_layout" id="0x7f0b0002" />
    <public type="layout" name="abc_action_menu_layout" id="0x7f0b0003" />
    <public type="layout" name="abc_action_mode_bar" id="0x7f0b0004" />
    <public type="layout" name="abc_action_mode_close_item_material" id="0x7f0b0005" />
    <public type="layout" name="abc_activity_chooser_view" id="0x7f0b0006" />
    <public type="layout" name="abc_activity_chooser_view_list_item" id="0x7f0b0007" />
    <public type="layout" name="abc_alert_dialog_button_bar_material" id="0x7f0b0008" />
    <public type="layout" name="abc_alert_dialog_material" id="0x7f0b0009" />
    <public type="layout" name="abc_alert_dialog_title_material" id="0x7f0b000a" />
    <public type="layout" name="abc_cascading_menu_item_layout" id="0x7f0b000b" />
    <public type="layout" name="abc_dialog_title_material" id="0x7f0b000c" />
    <public type="layout" name="abc_expanded_menu_layout" id="0x7f0b000d" />
    <public type="layout" name="abc_list_menu_item_checkbox" id="0x7f0b000e" />
    <public type="layout" name="abc_list_menu_item_icon" id="0x7f0b000f" />
    <public type="layout" name="abc_list_menu_item_layout" id="0x7f0b0010" />
    <public type="layout" name="abc_list_menu_item_radio" id="0x7f0b0011" />
    <public type="layout" name="abc_popup_menu_header_item_layout" id="0x7f0b0012" />
    <public type="layout" name="abc_popup_menu_item_layout" id="0x7f0b0013" />
    <public type="layout" name="abc_screen_content_include" id="0x7f0b0014" />
    <public type="layout" name="abc_screen_simple" id="0x7f0b0015" />
    <public type="layout" name="abc_screen_simple_overlay_action_mode" id="0x7f0b0016" />
    <public type="layout" name="abc_screen_toolbar" id="0x7f0b0017" />
    <public type="layout" name="abc_search_dropdown_item_icons_2line" id="0x7f0b0018" />
    <public type="layout" name="abc_search_view" id="0x7f0b0019" />
    <public type="layout" name="abc_select_dialog_material" id="0x7f0b001a" />
    <public type="layout" name="abc_tooltip" id="0x7f0b001b" />
    <public type="layout" name="design_bottom_navigation_item" id="0x7f0b001c" />
    <public type="layout" name="design_bottom_sheet_dialog" id="0x7f0b001d" />
    <public type="layout" name="design_layout_snackbar" id="0x7f0b001e" />
    <public type="layout" name="design_layout_snackbar_include" id="0x7f0b001f" />
    <public type="layout" name="design_layout_tab_icon" id="0x7f0b0020" />
    <public type="layout" name="design_layout_tab_text" id="0x7f0b0021" />
    <public type="layout" name="design_menu_item_action_area" id="0x7f0b0022" />
    <public type="layout" name="design_navigation_item" id="0x7f0b0023" />
    <public type="layout" name="design_navigation_item_header" id="0x7f0b0024" />
    <public type="layout" name="design_navigation_item_separator" id="0x7f0b0025" />
    <public type="layout" name="design_navigation_item_subheader" id="0x7f0b0026" />
    <public type="layout" name="design_navigation_menu" id="0x7f0b0027" />
    <public type="layout" name="design_navigation_menu_item" id="0x7f0b0028" />
    <public type="layout" name="design_text_input_password_icon" id="0x7f0b0029" />
    <public type="layout" name="exo_list_divider" id="0x7f0b002a" />
    <public type="layout" name="exo_playback_control_view" id="0x7f0b002b" />
    <public type="layout" name="exo_player_control_view" id="0x7f0b002c" />
    <public type="layout" name="exo_player_view" id="0x7f0b002d" />
    <public type="layout" name="exo_simple_player_view" id="0x7f0b002e" />
    <public type="layout" name="exo_track_selection_dialog" id="0x7f0b002f" />
    <public type="layout" name="isb_indicator" id="0x7f0b0030" />
    <public type="layout" name="mtrl_layout_snackbar" id="0x7f0b0031" />
    <public type="layout" name="mtrl_layout_snackbar_include" id="0x7f0b0032" />
    <public type="layout" name="notification_action" id="0x7f0b0033" />
    <public type="layout" name="notification_action_tombstone" id="0x7f0b0034" />
    <public type="layout" name="notification_media_action" id="0x7f0b0035" />
    <public type="layout" name="notification_media_cancel_action" id="0x7f0b0036" />
    <public type="layout" name="notification_template_big_media" id="0x7f0b0037" />
    <public type="layout" name="notification_template_big_media_custom" id="0x7f0b0038" />
    <public type="layout" name="notification_template_big_media_narrow" id="0x7f0b0039" />
    <public type="layout" name="notification_template_big_media_narrow_custom" id="0x7f0b003a" />
    <public type="layout" name="notification_template_custom_big" id="0x7f0b003b" />
    <public type="layout" name="notification_template_icon_group" id="0x7f0b003c" />
    <public type="layout" name="notification_template_lines_media" id="0x7f0b003d" />
    <public type="layout" name="notification_template_media" id="0x7f0b003e" />
    <public type="layout" name="notification_template_media_custom" id="0x7f0b003f" />
    <public type="layout" name="notification_template_part_chronometer" id="0x7f0b0040" />
    <public type="layout" name="notification_template_part_time" id="0x7f0b0041" />
    <public type="layout" name="qmui_bottom_sheet_grid" id="0x7f0b0042" />
    <public type="layout" name="qmui_bottom_sheet_grid_item" id="0x7f0b0043" />
    <public type="layout" name="qmui_bottom_sheet_grid_item_subscript" id="0x7f0b0044" />
    <public type="layout" name="qmui_bottom_sheet_list" id="0x7f0b0045" />
    <public type="layout" name="qmui_bottom_sheet_list_item" id="0x7f0b0046" />
    <public type="layout" name="qmui_bottom_sheet_list_item_mark" id="0x7f0b0047" />
    <public type="layout" name="qmui_common_list_item" id="0x7f0b0048" />
    <public type="layout" name="qmui_common_list_item_tip_new_layout" id="0x7f0b0049" />
    <public type="layout" name="qmui_dialog_layout" id="0x7f0b004a" />
    <public type="layout" name="qmui_empty_view" id="0x7f0b004b" />
    <public type="layout" name="qmui_group_list_section_layout" id="0x7f0b004c" />
    <public type="layout" name="qmui_popup_layout" id="0x7f0b004d" />
    <public type="layout" name="qmui_tip_dialog_layout" id="0x7f0b004e" />
    <public type="layout" name="select_dialog_item_material" id="0x7f0b004f" />
    <public type="layout" name="select_dialog_multichoice_material" id="0x7f0b0050" />
    <public type="layout" name="select_dialog_singlechoice_material" id="0x7f0b0051" />
    <public type="layout" name="support_simple_spinner_dropdown_item" id="0x7f0b0052" />
    <public type="layout" name="surface_container" id="0x7f0b0053" />
    <public type="layout" name="toast_layout" id="0x7f0b0054" />
    <public type="layout" name="utils_toast_view" id="0x7f0b0055" />
    <public type="layout" name="video_brightness" id="0x7f0b0056" />
    <public type="layout" name="video_layout_ad" id="0x7f0b0057" />
    <public type="layout" name="video_layout_custom" id="0x7f0b0058" />
    <public type="layout" name="video_layout_normal" id="0x7f0b0059" />
    <public type="layout" name="video_layout_sample_ad" id="0x7f0b005a" />
    <public type="layout" name="video_layout_standard" id="0x7f0b005b" />
    <public type="layout" name="video_progress_dialog" id="0x7f0b005c" />
    <public type="layout" name="video_view" id="0x7f0b005d" />
    <public type="layout" name="video_volume_dialog" id="0x7f0b005e" />
    <public type="mipmap" name="ic_launcher" id="0x7f0c0000" />
    <public type="string" name="abc_action_bar_home_description" id="0x7f0d0000" />
    <public type="string" name="abc_action_bar_up_description" id="0x7f0d0001" />
    <public type="string" name="abc_action_menu_overflow_description" id="0x7f0d0002" />
    <public type="string" name="abc_action_mode_done" id="0x7f0d0003" />
    <public type="string" name="abc_activity_chooser_view_see_all" id="0x7f0d0004" />
    <public type="string" name="abc_activitychooserview_choose_application" id="0x7f0d0005" />
    <public type="string" name="abc_capital_off" id="0x7f0d0006" />
    <public type="string" name="abc_capital_on" id="0x7f0d0007" />
    <public type="string" name="abc_font_family_body_1_material" id="0x7f0d0008" />
    <public type="string" name="abc_font_family_body_2_material" id="0x7f0d0009" />
    <public type="string" name="abc_font_family_button_material" id="0x7f0d000a" />
    <public type="string" name="abc_font_family_caption_material" id="0x7f0d000b" />
    <public type="string" name="abc_font_family_display_1_material" id="0x7f0d000c" />
    <public type="string" name="abc_font_family_display_2_material" id="0x7f0d000d" />
    <public type="string" name="abc_font_family_display_3_material" id="0x7f0d000e" />
    <public type="string" name="abc_font_family_display_4_material" id="0x7f0d000f" />
    <public type="string" name="abc_font_family_headline_material" id="0x7f0d0010" />
    <public type="string" name="abc_font_family_menu_material" id="0x7f0d0011" />
    <public type="string" name="abc_font_family_subhead_material" id="0x7f0d0012" />
    <public type="string" name="abc_font_family_title_material" id="0x7f0d0013" />
    <public type="string" name="abc_menu_alt_shortcut_label" id="0x7f0d0014" />
    <public type="string" name="abc_menu_ctrl_shortcut_label" id="0x7f0d0015" />
    <public type="string" name="abc_menu_delete_shortcut_label" id="0x7f0d0016" />
    <public type="string" name="abc_menu_enter_shortcut_label" id="0x7f0d0017" />
    <public type="string" name="abc_menu_function_shortcut_label" id="0x7f0d0018" />
    <public type="string" name="abc_menu_meta_shortcut_label" id="0x7f0d0019" />
    <public type="string" name="abc_menu_shift_shortcut_label" id="0x7f0d001a" />
    <public type="string" name="abc_menu_space_shortcut_label" id="0x7f0d001b" />
    <public type="string" name="abc_menu_sym_shortcut_label" id="0x7f0d001c" />
    <public type="string" name="abc_prepend_shortcut_label" id="0x7f0d001d" />
    <public type="string" name="abc_search_hint" id="0x7f0d001e" />
    <public type="string" name="abc_searchview_description_clear" id="0x7f0d001f" />
    <public type="string" name="abc_searchview_description_query" id="0x7f0d0020" />
    <public type="string" name="abc_searchview_description_search" id="0x7f0d0021" />
    <public type="string" name="abc_searchview_description_submit" id="0x7f0d0022" />
    <public type="string" name="abc_searchview_description_voice" id="0x7f0d0023" />
    <public type="string" name="abc_shareactionprovider_share_with" id="0x7f0d0024" />
    <public type="string" name="abc_shareactionprovider_share_with_application" id="0x7f0d0025" />
    <public type="string" name="abc_toolbar_collapse_description" id="0x7f0d0026" />
    <public type="string" name="accept" id="0x7f0d0027" />
    <public type="string" name="agent_center" id="0x7f0d0028" />
    <public type="string" name="agent_center_desc" id="0x7f0d0029" />
    <public type="string" name="all" id="0x7f0d002a" />
    <public type="string" name="apk_download_error" id="0x7f0d002b" />
    <public type="string" name="app_name" id="0x7f0d002c" />
    <public type="string" name="appbar_scrolling_view_behavior" id="0x7f0d002d" />
    <public type="string" name="balance_amount_tip" id="0x7f0d002e" />
    <public type="string" name="balance_tip2" id="0x7f0d002f" />
    <public type="string" name="book_subtitle" id="0x7f0d0030" />
    <public type="string" name="bottom_sheet_behavior" id="0x7f0d0031" />
    <public type="string" name="cancel" id="0x7f0d0032" />
    <public type="string" name="canceled" id="0x7f0d0033" />
    <public type="string" name="character_counter_content_description" id="0x7f0d0034" />
    <public type="string" name="character_counter_pattern" id="0x7f0d0035" />
    <public type="string" name="checkin" id="0x7f0d0036" />
    <public type="string" name="choose_login_type" id="0x7f0d0037" />
    <public type="string" name="clear" id="0x7f0d0038" />
    <public type="string" name="commnet" id="0x7f0d0039" />
    <public type="string" name="confirm_cancel" id="0x7f0d003a" />
    <public type="string" name="confirm_logout" id="0x7f0d003b" />
    <public type="string" name="confirm_no" id="0x7f0d003c" />
    <public type="string" name="confirm_yes" id="0x7f0d003d" />
    <public type="string" name="confrim_delete" id="0x7f0d003e" />
    <public type="string" name="confrim_delete_yes" id="0x7f0d003f" />
    <public type="string" name="current_playing" id="0x7f0d0040" />
    <public type="string" name="day" id="0x7f0d0041" />
    <public type="string" name="decline" id="0x7f0d0042" />
    <public type="string" name="delete" id="0x7f0d0043" />
    <public type="string" name="developing" id="0x7f0d0044" />
    <public type="string" name="discover" id="0x7f0d0045" />
    <public type="string" name="download" id="0x7f0d0046" />
    <public type="string" name="episode_not_exit" id="0x7f0d0047" />
    <public type="string" name="episodes" id="0x7f0d0048" />
    <public type="string" name="epsidoe_selector" id="0x7f0d0049" />
    <public type="string" name="exo" id="0x7f0d004a" />
    <public type="string" name="exo_controls_fastforward_description" id="0x7f0d004b" />
    <public type="string" name="exo_controls_fullscreen_description" id="0x7f0d004c" />
    <public type="string" name="exo_controls_next_description" id="0x7f0d004d" />
    <public type="string" name="exo_controls_pause_description" id="0x7f0d004e" />
    <public type="string" name="exo_controls_play_description" id="0x7f0d004f" />
    <public type="string" name="exo_controls_previous_description" id="0x7f0d0050" />
    <public type="string" name="exo_controls_repeat_all_description" id="0x7f0d0051" />
    <public type="string" name="exo_controls_repeat_off_description" id="0x7f0d0052" />
    <public type="string" name="exo_controls_repeat_one_description" id="0x7f0d0053" />
    <public type="string" name="exo_controls_rewind_description" id="0x7f0d0054" />
    <public type="string" name="exo_controls_shuffle_description" id="0x7f0d0055" />
    <public type="string" name="exo_controls_stop_description" id="0x7f0d0056" />
    <public type="string" name="exo_download_completed" id="0x7f0d0057" />
    <public type="string" name="exo_download_description" id="0x7f0d0058" />
    <public type="string" name="exo_download_downloading" id="0x7f0d0059" />
    <public type="string" name="exo_download_failed" id="0x7f0d005a" />
    <public type="string" name="exo_download_notification_channel_name" id="0x7f0d005b" />
    <public type="string" name="exo_download_removing" id="0x7f0d005c" />
    <public type="string" name="exo_item_list" id="0x7f0d005d" />
    <public type="string" name="exo_track_bitrate" id="0x7f0d005e" />
    <public type="string" name="exo_track_mono" id="0x7f0d005f" />
    <public type="string" name="exo_track_resolution" id="0x7f0d0060" />
    <public type="string" name="exo_track_selection_auto" id="0x7f0d0061" />
    <public type="string" name="exo_track_selection_none" id="0x7f0d0062" />
    <public type="string" name="exo_track_selection_title_audio" id="0x7f0d0063" />
    <public type="string" name="exo_track_selection_title_text" id="0x7f0d0064" />
    <public type="string" name="exo_track_selection_title_video" id="0x7f0d0065" />
    <public type="string" name="exo_track_stereo" id="0x7f0d0066" />
    <public type="string" name="exo_track_surround" id="0x7f0d0067" />
    <public type="string" name="exo_track_surround_5_point_1" id="0x7f0d0068" />
    <public type="string" name="exo_track_surround_7_point_1" id="0x7f0d0069" />
    <public type="string" name="exo_track_unknown" id="0x7f0d006a" />
    <public type="string" name="fab_transformation_scrim_behavior" id="0x7f0d006b" />
    <public type="string" name="fab_transformation_sheet_behavior" id="0x7f0d006c" />
    <public type="string" name="favorited" id="0x7f0d006d" />
    <public type="string" name="find_new_version" id="0x7f0d006e" />
    <public type="string" name="gift" id="0x7f0d006f" />
    <public type="string" name="help" id="0x7f0d0070" />
    <public type="string" name="help_desc" id="0x7f0d0071" />
    <public type="string" name="hide_bottom_view_on_scroll_behavior" id="0x7f0d0072" />
    <public type="string" name="home" id="0x7f0d0073" />
    <public type="string" name="hour" id="0x7f0d0074" />
    <public type="string" name="ijk" id="0x7f0d0075" />
    <public type="string" name="ijkplayer_dummy" id="0x7f0d0076" />
    <public type="string" name="install_faild_contact_stuff" id="0x7f0d0077" />
    <public type="string" name="jump_ad" id="0x7f0d0078" />
    <public type="string" name="just" id="0x7f0d0079" />
    <public type="string" name="like" id="0x7f0d007a" />
    <public type="string" name="liked" id="0x7f0d007b" />
    <public type="string" name="link" id="0x7f0d007c" />
    <public type="string" name="login" id="0x7f0d007d" />
    <public type="string" name="login_meta" id="0x7f0d007e" />
    <public type="string" name="login_with_wechat" id="0x7f0d007f" />
    <public type="string" name="logout" id="0x7f0d0080" />
    <public type="string" name="menu" id="0x7f0d0081" />
    <public type="string" name="minute" id="0x7f0d0082" />
    <public type="string" name="month" id="0x7f0d0083" />
    <public type="string" name="more" id="0x7f0d0084" />
    <public type="string" name="movie_not_exist" id="0x7f0d0085" />
    <public type="string" name="movie_update" id="0x7f0d0086" />
    <public type="string" name="movie_update_complete" id="0x7f0d0087" />
    <public type="string" name="mtrl_chip_close_icon_content_description" id="0x7f0d0088" />
    <public type="string" name="my" id="0x7f0d0089" />
    <public type="string" name="new_version" id="0x7f0d008a" />
    <public type="string" name="new_video" id="0x7f0d008b" />
    <public type="string" name="no_detail" id="0x7f0d008c" />
    <public type="string" name="no_net" id="0x7f0d008d" />
    <public type="string" name="no_url" id="0x7f0d008e" />
    <public type="string" name="order_fail" id="0x7f0d008f" />
    <public type="string" name="order_in_process" id="0x7f0d0090" />
    <public type="string" name="order_list" id="0x7f0d0091" />
    <public type="string" name="order_list_desc" id="0x7f0d0092" />
    <public type="string" name="order_new" id="0x7f0d0093" />
    <public type="string" name="origin_price" id="0x7f0d0094" />
    <public type="string" name="password_toggle_content_description" id="0x7f0d0095" />
    <public type="string" name="path_password_eye" id="0x7f0d0096" />
    <public type="string" name="path_password_eye_mask_strike_through" id="0x7f0d0097" />
    <public type="string" name="path_password_eye_mask_visible" id="0x7f0d0098" />
    <public type="string" name="path_password_strike_through" id="0x7f0d0099" />
    <public type="string" name="play" id="0x7f0d009a" />
    <public type="string" name="play_count" id="0x7f0d009b" />
    <public type="string" name="play_history" id="0x7f0d009c" />
    <public type="string" name="player_kit" id="0x7f0d009d" />
    <public type="string" name="press_again_to_exit" id="0x7f0d009e" />
    <public type="string" name="privacy_content" id="0x7f0d009f" />
    <public type="string" name="purchase" id="0x7f0d00a0" />
    <public type="string" name="purchase_alone" id="0x7f0d00a1" />
    <public type="string" name="purchase_alone_desc" id="0x7f0d00a2" />
    <public type="string" name="purchase_and_continue" id="0x7f0d00a3" />
    <public type="string" name="purchase_and_continue_time" id="0x7f0d00a4" />
    <public type="string" name="purchase_success" id="0x7f0d00a5" />
    <public type="string" name="purchase_vip" id="0x7f0d00a6" />
    <public type="string" name="purchase_vip_continue" id="0x7f0d00a7" />
    <public type="string" name="qmui_tool_fixellipsize" id="0x7f0d00a8" />
    <public type="string" name="qrcode_search_label" id="0x7f0d00a9" />
    <public type="string" name="quality" id="0x7f0d00aa" />
    <public type="string" name="rand" id="0x7f0d00ab" />
    <public type="string" name="related_movies" id="0x7f0d00ac" />
    <public type="string" name="score_label" id="0x7f0d00ad" />
    <public type="string" name="score_label_text" id="0x7f0d00ae" />
    <public type="string" name="search" id="0x7f0d00af" />
    <public type="string" name="search_menu_title" id="0x7f0d00b0" />
    <public type="string" name="secont" id="0x7f0d00b1" />
    <public type="string" name="select_quality" id="0x7f0d00b2" />
    <public type="string" name="setting" id="0x7f0d00b3" />
    <public type="string" name="setting_desc" id="0x7f0d00b4" />
    <public type="string" name="share" id="0x7f0d00b5" />
    <public type="string" name="status_bar_notification_info_overflow" id="0x7f0d00b6" />
    <public type="string" name="storeage_permission" id="0x7f0d00b7" />
    <public type="string" name="stuff" id="0x7f0d00b8" />
    <public type="string" name="system" id="0x7f0d00b9" />
    <public type="string" name="tips_not_wifi" id="0x7f0d00ba" />
    <public type="string" name="tips_not_wifi_cancel" id="0x7f0d00bb" />
    <public type="string" name="tips_not_wifi_confirm" id="0x7f0d00bc" />
    <public type="string" name="tiyatir" id="0x7f0d00bd" />
    <public type="string" name="toast_message" id="0x7f0d00be" />
    <public type="string" name="top" id="0x7f0d00bf" />
    <public type="string" name="total_episode" id="0x7f0d00c0" />
    <public type="string" name="try_end_and_purchase" id="0x7f0d00c1" />
    <public type="string" name="try_play" id="0x7f0d00c2" />
    <public type="string" name="try_play_short" id="0x7f0d00c3" />
    <public type="string" name="update" id="0x7f0d00c4" />
    <public type="string" name="update_tip" id="0x7f0d00c5" />
    <public type="string" name="user_id_meta" id="0x7f0d00c6" />
    <public type="string" name="vip" id="0x7f0d00c7" />
    <public type="string" name="vip_center" id="0x7f0d00c8" />
    <public type="string" name="vip_center_desc" id="0x7f0d00c9" />
    <public type="string" name="vip_day" id="0x7f0d00ca" />
    <public type="string" name="vip_label" id="0x7f0d00cb" />
    <public type="string" name="voice_book" id="0x7f0d00cc" />
    <public type="string" name="watching_label" id="0x7f0d00cd" />
    <public type="string" name="wechat" id="0x7f0d00ce" />
    <public type="string" name="wechat_moment" id="0x7f0d00cf" />
    <public type="string" name="withdraw" id="0x7f0d00d0" />
    <public type="string" name="withdraw_success" id="0x7f0d00d1" />
    <public type="string" name="year" id="0x7f0d00d2" />
    <public type="style" name="AVLoadingIndicatorView" id="0x7f0e0000" />
    <public type="style" name="AVLoadingIndicatorView.Large" id="0x7f0e0001" />
    <public type="style" name="AVLoadingIndicatorView.Small" id="0x7f0e0002" />
    <public type="style" name="ActionBarBase" id="0x7f0e0003" />
    <public type="style" name="ActionBarTitleTextBase" id="0x7f0e0004" />
    <public type="style" name="ActivityTranslucent" id="0x7f0e0005" />
    <public type="style" name="AlertDialog.AppCompat" id="0x7f0e0006" />
    <public type="style" name="AlertDialog.AppCompat.Light" id="0x7f0e0007" />
    <public type="style" name="Animation.AppCompat.Dialog" id="0x7f0e0008" />
    <public type="style" name="Animation.AppCompat.DropDownUp" id="0x7f0e0009" />
    <public type="style" name="Animation.AppCompat.Tooltip" id="0x7f0e000a" />
    <public type="style" name="Animation.Design.BottomSheetDialog" id="0x7f0e000b" />
    <public type="style" name="AppBaseTheme" id="0x7f0e000c" />
    <public type="style" name="AppBaseTheme.Compat" id="0x7f0e000d" />
    <public type="style" name="AppConfigTheme" id="0x7f0e000e" />
    <public type="style" name="AppConfigTheme.Compat" id="0x7f0e000f" />
    <public type="style" name="AppRootTheme" id="0x7f0e0010" />
    <public type="style" name="AppRootTheme.Compat" id="0x7f0e0011" />
    <public type="style" name="AutoCompleteTextView" id="0x7f0e0012" />
    <public type="style" name="AutoCompleteTextView.Compat" id="0x7f0e0013" />
    <public type="style" name="AutoCompleteTextViewBase" id="0x7f0e0014" />
    <public type="style" name="AutoCompleteTextViewBase.Compat" id="0x7f0e0015" />
    <public type="style" name="Base.AlertDialog.AppCompat" id="0x7f0e0016" />
    <public type="style" name="Base.AlertDialog.AppCompat.Light" id="0x7f0e0017" />
    <public type="style" name="Base.Animation.AppCompat.Dialog" id="0x7f0e0018" />
    <public type="style" name="Base.Animation.AppCompat.DropDownUp" id="0x7f0e0019" />
    <public type="style" name="Base.Animation.AppCompat.Tooltip" id="0x7f0e001a" />
    <public type="style" name="Base.CardView" id="0x7f0e001b" />
    <public type="style" name="Base.DialogWindowTitle.AppCompat" id="0x7f0e001c" />
    <public type="style" name="Base.DialogWindowTitleBackground.AppCompat" id="0x7f0e001d" />
    <public type="style" name="Base.TextAppearance.AppCompat" id="0x7f0e001e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body1" id="0x7f0e001f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body2" id="0x7f0e0020" />
    <public type="style" name="Base.TextAppearance.AppCompat.Button" id="0x7f0e0021" />
    <public type="style" name="Base.TextAppearance.AppCompat.Caption" id="0x7f0e0022" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display1" id="0x7f0e0023" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display2" id="0x7f0e0024" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display3" id="0x7f0e0025" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display4" id="0x7f0e0026" />
    <public type="style" name="Base.TextAppearance.AppCompat.Headline" id="0x7f0e0027" />
    <public type="style" name="Base.TextAppearance.AppCompat.Inverse" id="0x7f0e0028" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large" id="0x7f0e0029" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large.Inverse" id="0x7f0e002a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f0e002b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f0e002c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium" id="0x7f0e002d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium.Inverse" id="0x7f0e002e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Menu" id="0x7f0e002f" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult" id="0x7f0e0030" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f0e0031" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Title" id="0x7f0e0032" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small" id="0x7f0e0033" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small.Inverse" id="0x7f0e0034" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead" id="0x7f0e0035" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead.Inverse" id="0x7f0e0036" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title" id="0x7f0e0037" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title.Inverse" id="0x7f0e0038" />
    <public type="style" name="Base.TextAppearance.AppCompat.Tooltip" id="0x7f0e0039" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f0e003a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f0e003b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f0e003c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f0e003d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f0e003e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f0e003f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f0e0040" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button" id="0x7f0e0041" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f0e0042" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f0e0043" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f0e0044" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f0e0045" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f0e0046" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f0e0047" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f0e0048" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Switch" id="0x7f0e0049" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f0e004a" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f0e004b" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f0e004c" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f0e004d" />
    <public type="style" name="Base.Theme.AppCompat" id="0x7f0e004e" />
    <public type="style" name="Base.Theme.AppCompat.CompactMenu" id="0x7f0e004f" />
    <public type="style" name="Base.Theme.AppCompat.Dialog" id="0x7f0e0050" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.Alert" id="0x7f0e0051" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.FixedSize" id="0x7f0e0052" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.MinWidth" id="0x7f0e0053" />
    <public type="style" name="Base.Theme.AppCompat.DialogWhenLarge" id="0x7f0e0054" />
    <public type="style" name="Base.Theme.AppCompat.Light" id="0x7f0e0055" />
    <public type="style" name="Base.Theme.AppCompat.Light.DarkActionBar" id="0x7f0e0056" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog" id="0x7f0e0057" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.Alert" id="0x7f0e0058" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" id="0x7f0e0059" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f0e005a" />
    <public type="style" name="Base.Theme.AppCompat.Light.DialogWhenLarge" id="0x7f0e005b" />
    <public type="style" name="Base.Theme.MaterialComponents" id="0x7f0e005c" />
    <public type="style" name="Base.Theme.MaterialComponents.Bridge" id="0x7f0e005d" />
    <public type="style" name="Base.Theme.MaterialComponents.CompactMenu" id="0x7f0e005e" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog" id="0x7f0e005f" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Alert" id="0x7f0e0060" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.FixedSize" id="0x7f0e0061" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.MinWidth" id="0x7f0e0062" />
    <public type="style" name="Base.Theme.MaterialComponents.DialogWhenLarge" id="0x7f0e0063" />
    <public type="style" name="Base.Theme.MaterialComponents.Light" id="0x7f0e0064" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Bridge" id="0x7f0e0065" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar" id="0x7f0e0066" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f0e0067" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog" id="0x7f0e0068" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f0e0069" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f0e006a" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f0e006b" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f0e006c" />
    <public type="style" name="Base.ThemeOverlay.AppCompat" id="0x7f0e006d" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.ActionBar" id="0x7f0e006e" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark" id="0x7f0e006f" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f0e0070" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog" id="0x7f0e0071" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f0e0072" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Light" id="0x7f0e0073" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog" id="0x7f0e0074" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f0e0075" />
    <public type="style" name="Base.V14.Theme.MaterialComponents" id="0x7f0e0076" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Bridge" id="0x7f0e0077" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog" id="0x7f0e0078" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light" id="0x7f0e0079" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Bridge" id="0x7f0e007a" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f0e007b" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog" id="0x7f0e007c" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" id="0x7f0e007d" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f0e007e" />
    <public type="style" name="Base.V21.Theme.AppCompat" id="0x7f0e007f" />
    <public type="style" name="Base.V21.Theme.AppCompat.Dialog" id="0x7f0e0080" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light" id="0x7f0e0081" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light.Dialog" id="0x7f0e0082" />
    <public type="style" name="Base.V21.ThemeOverlay.AppCompat.Dialog" id="0x7f0e0083" />
    <public type="style" name="Base.V22.Theme.AppCompat" id="0x7f0e0084" />
    <public type="style" name="Base.V22.Theme.AppCompat.Light" id="0x7f0e0085" />
    <public type="style" name="Base.V23.Theme.AppCompat" id="0x7f0e0086" />
    <public type="style" name="Base.V23.Theme.AppCompat.Light" id="0x7f0e0087" />
    <public type="style" name="Base.V26.Theme.AppCompat" id="0x7f0e0088" />
    <public type="style" name="Base.V26.Theme.AppCompat.Light" id="0x7f0e0089" />
    <public type="style" name="Base.V26.Widget.AppCompat.Toolbar" id="0x7f0e008a" />
    <public type="style" name="Base.V28.Theme.AppCompat" id="0x7f0e008b" />
    <public type="style" name="Base.V28.Theme.AppCompat.Light" id="0x7f0e008c" />
    <public type="style" name="Base.V7.Theme.AppCompat" id="0x7f0e008d" />
    <public type="style" name="Base.V7.Theme.AppCompat.Dialog" id="0x7f0e008e" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light" id="0x7f0e008f" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light.Dialog" id="0x7f0e0090" />
    <public type="style" name="Base.V7.ThemeOverlay.AppCompat.Dialog" id="0x7f0e0091" />
    <public type="style" name="Base.V7.Widget.AppCompat.AutoCompleteTextView" id="0x7f0e0092" />
    <public type="style" name="Base.V7.Widget.AppCompat.EditText" id="0x7f0e0093" />
    <public type="style" name="Base.V7.Widget.AppCompat.Toolbar" id="0x7f0e0094" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar" id="0x7f0e0095" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.Solid" id="0x7f0e0096" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabBar" id="0x7f0e0097" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabText" id="0x7f0e0098" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabView" id="0x7f0e0099" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton" id="0x7f0e009a" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.CloseMode" id="0x7f0e009b" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.Overflow" id="0x7f0e009c" />
    <public type="style" name="Base.Widget.AppCompat.ActionMode" id="0x7f0e009d" />
    <public type="style" name="Base.Widget.AppCompat.ActivityChooserView" id="0x7f0e009e" />
    <public type="style" name="Base.Widget.AppCompat.AutoCompleteTextView" id="0x7f0e009f" />
    <public type="style" name="Base.Widget.AppCompat.Button" id="0x7f0e00a0" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless" id="0x7f0e00a1" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless.Colored" id="0x7f0e00a2" />
    <public type="style" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f0e00a3" />
    <public type="style" name="Base.Widget.AppCompat.Button.Colored" id="0x7f0e00a4" />
    <public type="style" name="Base.Widget.AppCompat.Button.Small" id="0x7f0e00a5" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar" id="0x7f0e00a6" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f0e00a7" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.CheckBox" id="0x7f0e00a8" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.RadioButton" id="0x7f0e00a9" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.Switch" id="0x7f0e00aa" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle" id="0x7f0e00ab" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" id="0x7f0e00ac" />
    <public type="style" name="Base.Widget.AppCompat.DropDownItem.Spinner" id="0x7f0e00ad" />
    <public type="style" name="Base.Widget.AppCompat.EditText" id="0x7f0e00ae" />
    <public type="style" name="Base.Widget.AppCompat.ImageButton" id="0x7f0e00af" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar" id="0x7f0e00b0" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.Solid" id="0x7f0e00b1" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f0e00b2" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText" id="0x7f0e00b3" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f0e00b4" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabView" id="0x7f0e00b5" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu" id="0x7f0e00b6" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f0e00b7" />
    <public type="style" name="Base.Widget.AppCompat.ListMenuView" id="0x7f0e00b8" />
    <public type="style" name="Base.Widget.AppCompat.ListPopupWindow" id="0x7f0e00b9" />
    <public type="style" name="Base.Widget.AppCompat.ListView" id="0x7f0e00ba" />
    <public type="style" name="Base.Widget.AppCompat.ListView.DropDown" id="0x7f0e00bb" />
    <public type="style" name="Base.Widget.AppCompat.ListView.Menu" id="0x7f0e00bc" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu" id="0x7f0e00bd" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu.Overflow" id="0x7f0e00be" />
    <public type="style" name="Base.Widget.AppCompat.PopupWindow" id="0x7f0e00bf" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar" id="0x7f0e00c0" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar.Horizontal" id="0x7f0e00c1" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar" id="0x7f0e00c2" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Indicator" id="0x7f0e00c3" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Small" id="0x7f0e00c4" />
    <public type="style" name="Base.Widget.AppCompat.SearchView" id="0x7f0e00c5" />
    <public type="style" name="Base.Widget.AppCompat.SearchView.ActionBar" id="0x7f0e00c6" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar" id="0x7f0e00c7" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar.Discrete" id="0x7f0e00c8" />
    <public type="style" name="Base.Widget.AppCompat.Spinner" id="0x7f0e00c9" />
    <public type="style" name="Base.Widget.AppCompat.Spinner.Underlined" id="0x7f0e00ca" />
    <public type="style" name="Base.Widget.AppCompat.TextView.SpinnerItem" id="0x7f0e00cb" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar" id="0x7f0e00cc" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f0e00cd" />
    <public type="style" name="Base.Widget.Design.TabLayout" id="0x7f0e00ce" />
    <public type="style" name="Base.Widget.MaterialComponents.Chip" id="0x7f0e00cf" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputEditText" id="0x7f0e00d0" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputLayout" id="0x7f0e00d1" />
    <public type="style" name="Button" id="0x7f0e00d2" />
    <public type="style" name="Button.Compat" id="0x7f0e00d3" />
    <public type="style" name="ButtonBase" id="0x7f0e00d4" />
    <public type="style" name="ButtonBase.Compat" id="0x7f0e00d5" />
    <public type="style" name="CardView" id="0x7f0e00d6" />
    <public type="style" name="CardView.Dark" id="0x7f0e00d7" />
    <public type="style" name="CardView.Light" id="0x7f0e00d8" />
    <public type="style" name="DropDownListView" id="0x7f0e00d9" />
    <public type="style" name="DropDownListView.Compat" id="0x7f0e00da" />
    <public type="style" name="DropDownListViewBase" id="0x7f0e00db" />
    <public type="style" name="DropDownListViewBase.Compat" id="0x7f0e00dc" />
    <public type="style" name="EditText" id="0x7f0e00dd" />
    <public type="style" name="EditText.Compat" id="0x7f0e00de" />
    <public type="style" name="EditTextBase" id="0x7f0e00df" />
    <public type="style" name="EditTextBase.Compat" id="0x7f0e00e0" />
    <public type="style" name="ExoMediaButton" id="0x7f0e00e1" />
    <public type="style" name="ExoMediaButton.FastForward" id="0x7f0e00e2" />
    <public type="style" name="ExoMediaButton.Next" id="0x7f0e00e3" />
    <public type="style" name="ExoMediaButton.Pause" id="0x7f0e00e4" />
    <public type="style" name="ExoMediaButton.Play" id="0x7f0e00e5" />
    <public type="style" name="ExoMediaButton.Previous" id="0x7f0e00e6" />
    <public type="style" name="ExoMediaButton.Rewind" id="0x7f0e00e7" />
    <public type="style" name="ExoMediaButton.Shuffle" id="0x7f0e00e8" />
    <public type="style" name="GridView" id="0x7f0e00e9" />
    <public type="style" name="GridView.Compat" id="0x7f0e00ea" />
    <public type="style" name="GridViewBase" id="0x7f0e00eb" />
    <public type="style" name="GridViewBase.Compat" id="0x7f0e00ec" />
    <public type="style" name="ImageButton" id="0x7f0e00ed" />
    <public type="style" name="ImageButton.Compat" id="0x7f0e00ee" />
    <public type="style" name="ImageButtonBase" id="0x7f0e00ef" />
    <public type="style" name="ImageButtonBase.Compat" id="0x7f0e00f0" />
    <public type="style" name="ListView" id="0x7f0e00f1" />
    <public type="style" name="ListView.Compat" id="0x7f0e00f2" />
    <public type="style" name="ListViewBase" id="0x7f0e00f3" />
    <public type="style" name="ListViewBase.Compat" id="0x7f0e00f4" />
    <public type="style" name="Main" id="0x7f0e00f5" />
    <public type="style" name="Main.Launcher" id="0x7f0e00f6" />
    <public type="style" name="Platform.AppCompat" id="0x7f0e00f7" />
    <public type="style" name="Platform.AppCompat.Light" id="0x7f0e00f8" />
    <public type="style" name="Platform.MaterialComponents" id="0x7f0e00f9" />
    <public type="style" name="Platform.MaterialComponents.Dialog" id="0x7f0e00fa" />
    <public type="style" name="Platform.MaterialComponents.Light" id="0x7f0e00fb" />
    <public type="style" name="Platform.MaterialComponents.Light.Dialog" id="0x7f0e00fc" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat" id="0x7f0e00fd" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Dark" id="0x7f0e00fe" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Light" id="0x7f0e00ff" />
    <public type="style" name="Platform.V21.AppCompat" id="0x7f0e0100" />
    <public type="style" name="Platform.V21.AppCompat.Light" id="0x7f0e0101" />
    <public type="style" name="Platform.V25.AppCompat" id="0x7f0e0102" />
    <public type="style" name="Platform.V25.AppCompat.Light" id="0x7f0e0103" />
    <public type="style" name="Platform.Widget.AppCompat.Spinner" id="0x7f0e0104" />
    <public type="style" name="QMUI" id="0x7f0e0105" />
    <public type="style" name="QMUI.Animation" id="0x7f0e0106" />
    <public type="style" name="QMUI.Animation.PopDownMenu" id="0x7f0e0107" />
    <public type="style" name="QMUI.Animation.PopDownMenu.Center" id="0x7f0e0108" />
    <public type="style" name="QMUI.Animation.PopDownMenu.Left" id="0x7f0e0109" />
    <public type="style" name="QMUI.Animation.PopDownMenu.Right" id="0x7f0e010a" />
    <public type="style" name="QMUI.Animation.PopUpMenu" id="0x7f0e010b" />
    <public type="style" name="QMUI.Animation.PopUpMenu.Center" id="0x7f0e010c" />
    <public type="style" name="QMUI.Animation.PopUpMenu.Left" id="0x7f0e010d" />
    <public type="style" name="QMUI.Animation.PopUpMenu.Right" id="0x7f0e010e" />
    <public type="style" name="QMUI.BottomSheet" id="0x7f0e010f" />
    <public type="style" name="QMUI.CollapsingTopBarLayoutCollapsed" id="0x7f0e0110" />
    <public type="style" name="QMUI.CollapsingTopBarLayoutExpanded" id="0x7f0e0111" />
    <public type="style" name="QMUI.CommonListItemView" id="0x7f0e0112" />
    <public type="style" name="QMUI.Compat" id="0x7f0e0113" />
    <public type="style" name="QMUI.Compat.NoActionBar" id="0x7f0e0114" />
    <public type="style" name="QMUI.Dialog" id="0x7f0e0115" />
    <public type="style" name="QMUI.Dialog.Action" id="0x7f0e0116" />
    <public type="style" name="QMUI.Dialog.ActionContainer" id="0x7f0e0117" />
    <public type="style" name="QMUI.Dialog.EditContent" id="0x7f0e0118" />
    <public type="style" name="QMUI.Dialog.FullWidth" id="0x7f0e0119" />
    <public type="style" name="QMUI.Dialog.FullWidth.NoAnimation" id="0x7f0e011a" />
    <public type="style" name="QMUI.Dialog.MenuContainer" id="0x7f0e011b" />
    <public type="style" name="QMUI.Dialog.MessageContent" id="0x7f0e011c" />
    <public type="style" name="QMUI.Dialog.Title" id="0x7f0e011d" />
    <public type="style" name="QMUI.Dialog.Wrapper" id="0x7f0e011e" />
    <public type="style" name="QMUI.Dialog.Wrapper.FullScreen" id="0x7f0e011f" />
    <public type="style" name="QMUI.Dialog_MenuItem" id="0x7f0e0120" />
    <public type="style" name="QMUI.GroupListSectionView" id="0x7f0e0121" />
    <public type="style" name="QMUI.GroupListView" id="0x7f0e0122" />
    <public type="style" name="QMUI.Loading" id="0x7f0e0123" />
    <public type="style" name="QMUI.Loading.White" id="0x7f0e0124" />
    <public type="style" name="QMUI.NoActionBar" id="0x7f0e0125" />
    <public type="style" name="QMUI.PullRefreshLayout" id="0x7f0e0126" />
    <public type="style" name="QMUI.QQFaceView" id="0x7f0e0127" />
    <public type="style" name="QMUI.RadiusImageView" id="0x7f0e0128" />
    <public type="style" name="QMUI.RoundButton" id="0x7f0e0129" />
    <public type="style" name="QMUI.TabSegment" id="0x7f0e012a" />
    <public type="style" name="QMUI.TipDialog" id="0x7f0e012b" />
    <public type="style" name="QMUI.TipNew" id="0x7f0e012c" />
    <public type="style" name="QMUI.TipPoint" id="0x7f0e012d" />
    <public type="style" name="QMUI.TopBar" id="0x7f0e012e" />
    <public type="style" name="QMUITextAppearance" id="0x7f0e012f" />
    <public type="style" name="QMUITextAppearance.GridItem" id="0x7f0e0130" />
    <public type="style" name="QMUITextAppearance.GridItem.Small" id="0x7f0e0131" />
    <public type="style" name="QMUITextAppearance.ListItem" id="0x7f0e0132" />
    <public type="style" name="QMUITextAppearance.Title" id="0x7f0e0133" />
    <public type="style" name="QMUITextAppearance.Title.Gray" id="0x7f0e0134" />
    <public type="style" name="QMUITextAppearance.Title.Large" id="0x7f0e0135" />
    <public type="style" name="RtlOverlay.DialogWindowTitle.AppCompat" id="0x7f0e0136" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" id="0x7f0e0137" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" id="0x7f0e0138" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" id="0x7f0e0139" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" id="0x7f0e013a" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" id="0x7f0e013b" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" id="0x7f0e013c" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" id="0x7f0e013d" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" id="0x7f0e013e" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown" id="0x7f0e013f" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" id="0x7f0e0140" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" id="0x7f0e0141" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" id="0x7f0e0142" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" id="0x7f0e0143" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" id="0x7f0e0144" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton" id="0x7f0e0145" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" id="0x7f0e0146" />
    <public type="style" name="TextAppearance.AppCompat" id="0x7f0e0147" />
    <public type="style" name="TextAppearance.AppCompat.Body1" id="0x7f0e0148" />
    <public type="style" name="TextAppearance.AppCompat.Body2" id="0x7f0e0149" />
    <public type="style" name="TextAppearance.AppCompat.Button" id="0x7f0e014a" />
    <public type="style" name="TextAppearance.AppCompat.Caption" id="0x7f0e014b" />
    <public type="style" name="TextAppearance.AppCompat.Display1" id="0x7f0e014c" />
    <public type="style" name="TextAppearance.AppCompat.Display2" id="0x7f0e014d" />
    <public type="style" name="TextAppearance.AppCompat.Display3" id="0x7f0e014e" />
    <public type="style" name="TextAppearance.AppCompat.Display4" id="0x7f0e014f" />
    <public type="style" name="TextAppearance.AppCompat.Headline" id="0x7f0e0150" />
    <public type="style" name="TextAppearance.AppCompat.Inverse" id="0x7f0e0151" />
    <public type="style" name="TextAppearance.AppCompat.Large" id="0x7f0e0152" />
    <public type="style" name="TextAppearance.AppCompat.Large.Inverse" id="0x7f0e0153" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" id="0x7f0e0154" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Title" id="0x7f0e0155" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f0e0156" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f0e0157" />
    <public type="style" name="TextAppearance.AppCompat.Medium" id="0x7f0e0158" />
    <public type="style" name="TextAppearance.AppCompat.Medium.Inverse" id="0x7f0e0159" />
    <public type="style" name="TextAppearance.AppCompat.Menu" id="0x7f0e015a" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f0e015b" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Title" id="0x7f0e015c" />
    <public type="style" name="TextAppearance.AppCompat.Small" id="0x7f0e015d" />
    <public type="style" name="TextAppearance.AppCompat.Small.Inverse" id="0x7f0e015e" />
    <public type="style" name="TextAppearance.AppCompat.Subhead" id="0x7f0e015f" />
    <public type="style" name="TextAppearance.AppCompat.Subhead.Inverse" id="0x7f0e0160" />
    <public type="style" name="TextAppearance.AppCompat.Title" id="0x7f0e0161" />
    <public type="style" name="TextAppearance.AppCompat.Title.Inverse" id="0x7f0e0162" />
    <public type="style" name="TextAppearance.AppCompat.Tooltip" id="0x7f0e0163" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f0e0164" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f0e0165" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f0e0166" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f0e0167" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f0e0168" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f0e0169" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" id="0x7f0e016a" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f0e016b" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" id="0x7f0e016c" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button" id="0x7f0e016d" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f0e016e" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f0e016f" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f0e0170" />
    <public type="style" name="TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f0e0171" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f0e0172" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f0e0173" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f0e0174" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Switch" id="0x7f0e0175" />
    <public type="style" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f0e0176" />
    <public type="style" name="TextAppearance.Compat.Notification" id="0x7f0e0177" />
    <public type="style" name="TextAppearance.Compat.Notification.Info" id="0x7f0e0178" />
    <public type="style" name="TextAppearance.Compat.Notification.Info.Media" id="0x7f0e0179" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2" id="0x7f0e017a" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2.Media" id="0x7f0e017b" />
    <public type="style" name="TextAppearance.Compat.Notification.Media" id="0x7f0e017c" />
    <public type="style" name="TextAppearance.Compat.Notification.Time" id="0x7f0e017d" />
    <public type="style" name="TextAppearance.Compat.Notification.Time.Media" id="0x7f0e017e" />
    <public type="style" name="TextAppearance.Compat.Notification.Title" id="0x7f0e017f" />
    <public type="style" name="TextAppearance.Compat.Notification.Title.Media" id="0x7f0e0180" />
    <public type="style" name="TextAppearance.Design.CollapsingToolbar.Expanded" id="0x7f0e0181" />
    <public type="style" name="TextAppearance.Design.Counter" id="0x7f0e0182" />
    <public type="style" name="TextAppearance.Design.Counter.Overflow" id="0x7f0e0183" />
    <public type="style" name="TextAppearance.Design.Error" id="0x7f0e0184" />
    <public type="style" name="TextAppearance.Design.HelperText" id="0x7f0e0185" />
    <public type="style" name="TextAppearance.Design.Hint" id="0x7f0e0186" />
    <public type="style" name="TextAppearance.Design.Snackbar.Message" id="0x7f0e0187" />
    <public type="style" name="TextAppearance.Design.Tab" id="0x7f0e0188" />
    <public type="style" name="TextAppearance.MaterialComponents.Body1" id="0x7f0e0189" />
    <public type="style" name="TextAppearance.MaterialComponents.Body2" id="0x7f0e018a" />
    <public type="style" name="TextAppearance.MaterialComponents.Button" id="0x7f0e018b" />
    <public type="style" name="TextAppearance.MaterialComponents.Caption" id="0x7f0e018c" />
    <public type="style" name="TextAppearance.MaterialComponents.Chip" id="0x7f0e018d" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline1" id="0x7f0e018e" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline2" id="0x7f0e018f" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline3" id="0x7f0e0190" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline4" id="0x7f0e0191" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline5" id="0x7f0e0192" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline6" id="0x7f0e0193" />
    <public type="style" name="TextAppearance.MaterialComponents.Overline" id="0x7f0e0194" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle1" id="0x7f0e0195" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle2" id="0x7f0e0196" />
    <public type="style" name="TextAppearance.MaterialComponents.Tab" id="0x7f0e0197" />
    <public type="style" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f0e0198" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f0e0199" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f0e019a" />
    <public type="style" name="TextAppearanceBase" id="0x7f0e019b" />
    <public type="style" name="TextView" id="0x7f0e019c" />
    <public type="style" name="TextView.Compat" id="0x7f0e019d" />
    <public type="style" name="Theme.AppCompat" id="0x7f0e019e" />
    <public type="style" name="Theme.AppCompat.CompactMenu" id="0x7f0e019f" />
    <public type="style" name="Theme.AppCompat.DayNight" id="0x7f0e01a0" />
    <public type="style" name="Theme.AppCompat.DayNight.DarkActionBar" id="0x7f0e01a1" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog" id="0x7f0e01a2" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.Alert" id="0x7f0e01a3" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.MinWidth" id="0x7f0e01a4" />
    <public type="style" name="Theme.AppCompat.DayNight.DialogWhenLarge" id="0x7f0e01a5" />
    <public type="style" name="Theme.AppCompat.DayNight.NoActionBar" id="0x7f0e01a6" />
    <public type="style" name="Theme.AppCompat.Dialog" id="0x7f0e01a7" />
    <public type="style" name="Theme.AppCompat.Dialog.Alert" id="0x7f0e01a8" />
    <public type="style" name="Theme.AppCompat.Dialog.MinWidth" id="0x7f0e01a9" />
    <public type="style" name="Theme.AppCompat.DialogWhenLarge" id="0x7f0e01aa" />
    <public type="style" name="Theme.AppCompat.Light" id="0x7f0e01ab" />
    <public type="style" name="Theme.AppCompat.Light.DarkActionBar" id="0x7f0e01ac" />
    <public type="style" name="Theme.AppCompat.Light.Dialog" id="0x7f0e01ad" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.Alert" id="0x7f0e01ae" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f0e01af" />
    <public type="style" name="Theme.AppCompat.Light.DialogWhenLarge" id="0x7f0e01b0" />
    <public type="style" name="Theme.AppCompat.Light.NoActionBar" id="0x7f0e01b1" />
    <public type="style" name="Theme.AppCompat.NoActionBar" id="0x7f0e01b2" />
    <public type="style" name="Theme.Design" id="0x7f0e01b3" />
    <public type="style" name="Theme.Design.BottomSheetDialog" id="0x7f0e01b4" />
    <public type="style" name="Theme.Design.Light" id="0x7f0e01b5" />
    <public type="style" name="Theme.Design.Light.BottomSheetDialog" id="0x7f0e01b6" />
    <public type="style" name="Theme.Design.Light.NoActionBar" id="0x7f0e01b7" />
    <public type="style" name="Theme.Design.NoActionBar" id="0x7f0e01b8" />
    <public type="style" name="Theme.MaterialComponents" id="0x7f0e01b9" />
    <public type="style" name="Theme.MaterialComponents.BottomSheetDialog" id="0x7f0e01ba" />
    <public type="style" name="Theme.MaterialComponents.Bridge" id="0x7f0e01bb" />
    <public type="style" name="Theme.MaterialComponents.CompactMenu" id="0x7f0e01bc" />
    <public type="style" name="Theme.MaterialComponents.Dialog" id="0x7f0e01bd" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert" id="0x7f0e01be" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth" id="0x7f0e01bf" />
    <public type="style" name="Theme.MaterialComponents.DialogWhenLarge" id="0x7f0e01c0" />
    <public type="style" name="Theme.MaterialComponents.Light" id="0x7f0e01c1" />
    <public type="style" name="Theme.MaterialComponents.Light.BottomSheetDialog" id="0x7f0e01c2" />
    <public type="style" name="Theme.MaterialComponents.Light.Bridge" id="0x7f0e01c3" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar" id="0x7f0e01c4" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f0e01c5" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog" id="0x7f0e01c6" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f0e01c7" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f0e01c8" />
    <public type="style" name="Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f0e01c9" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar" id="0x7f0e01ca" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar.Bridge" id="0x7f0e01cb" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar" id="0x7f0e01cc" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar.Bridge" id="0x7f0e01cd" />
    <public type="style" name="ThemeOverlay.AppCompat" id="0x7f0e01ce" />
    <public type="style" name="ThemeOverlay.AppCompat.ActionBar" id="0x7f0e01cf" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark" id="0x7f0e01d0" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f0e01d1" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog" id="0x7f0e01d2" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f0e01d3" />
    <public type="style" name="ThemeOverlay.AppCompat.Light" id="0x7f0e01d4" />
    <public type="style" name="ThemeOverlay.MaterialComponents" id="0x7f0e01d5" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar" id="0x7f0e01d6" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark" id="0x7f0e01d7" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark.ActionBar" id="0x7f0e01d8" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog" id="0x7f0e01d9" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f0e01da" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light" id="0x7f0e01db" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText" id="0x7f0e01dc" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" id="0x7f0e01dd" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f0e01de" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f0e01df" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f0e01e0" />
    <public type="style" name="Widget.AppCompat.ActionBar" id="0x7f0e01e1" />
    <public type="style" name="Widget.AppCompat.ActionBar.Solid" id="0x7f0e01e2" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabBar" id="0x7f0e01e3" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabText" id="0x7f0e01e4" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabView" id="0x7f0e01e5" />
    <public type="style" name="Widget.AppCompat.ActionButton" id="0x7f0e01e6" />
    <public type="style" name="Widget.AppCompat.ActionButton.CloseMode" id="0x7f0e01e7" />
    <public type="style" name="Widget.AppCompat.ActionButton.Overflow" id="0x7f0e01e8" />
    <public type="style" name="Widget.AppCompat.ActionMode" id="0x7f0e01e9" />
    <public type="style" name="Widget.AppCompat.ActivityChooserView" id="0x7f0e01ea" />
    <public type="style" name="Widget.AppCompat.AutoCompleteTextView" id="0x7f0e01eb" />
    <public type="style" name="Widget.AppCompat.Button" id="0x7f0e01ec" />
    <public type="style" name="Widget.AppCompat.Button.Borderless" id="0x7f0e01ed" />
    <public type="style" name="Widget.AppCompat.Button.Borderless.Colored" id="0x7f0e01ee" />
    <public type="style" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f0e01ef" />
    <public type="style" name="Widget.AppCompat.Button.Colored" id="0x7f0e01f0" />
    <public type="style" name="Widget.AppCompat.Button.Small" id="0x7f0e01f1" />
    <public type="style" name="Widget.AppCompat.ButtonBar" id="0x7f0e01f2" />
    <public type="style" name="Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f0e01f3" />
    <public type="style" name="Widget.AppCompat.CompoundButton.CheckBox" id="0x7f0e01f4" />
    <public type="style" name="Widget.AppCompat.CompoundButton.RadioButton" id="0x7f0e01f5" />
    <public type="style" name="Widget.AppCompat.CompoundButton.Switch" id="0x7f0e01f6" />
    <public type="style" name="Widget.AppCompat.DrawerArrowToggle" id="0x7f0e01f7" />
    <public type="style" name="Widget.AppCompat.DropDownItem.Spinner" id="0x7f0e01f8" />
    <public type="style" name="Widget.AppCompat.EditText" id="0x7f0e01f9" />
    <public type="style" name="Widget.AppCompat.ImageButton" id="0x7f0e01fa" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar" id="0x7f0e01fb" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid" id="0x7f0e01fc" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" id="0x7f0e01fd" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f0e01fe" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" id="0x7f0e01ff" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText" id="0x7f0e0200" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f0e0201" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView" id="0x7f0e0202" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" id="0x7f0e0203" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton" id="0x7f0e0204" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.CloseMode" id="0x7f0e0205" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.Overflow" id="0x7f0e0206" />
    <public type="style" name="Widget.AppCompat.Light.ActionMode.Inverse" id="0x7f0e0207" />
    <public type="style" name="Widget.AppCompat.Light.ActivityChooserView" id="0x7f0e0208" />
    <public type="style" name="Widget.AppCompat.Light.AutoCompleteTextView" id="0x7f0e0209" />
    <public type="style" name="Widget.AppCompat.Light.DropDownItem.Spinner" id="0x7f0e020a" />
    <public type="style" name="Widget.AppCompat.Light.ListPopupWindow" id="0x7f0e020b" />
    <public type="style" name="Widget.AppCompat.Light.ListView.DropDown" id="0x7f0e020c" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu" id="0x7f0e020d" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f0e020e" />
    <public type="style" name="Widget.AppCompat.Light.SearchView" id="0x7f0e020f" />
    <public type="style" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" id="0x7f0e0210" />
    <public type="style" name="Widget.AppCompat.ListMenuView" id="0x7f0e0211" />
    <public type="style" name="Widget.AppCompat.ListPopupWindow" id="0x7f0e0212" />
    <public type="style" name="Widget.AppCompat.ListView" id="0x7f0e0213" />
    <public type="style" name="Widget.AppCompat.ListView.DropDown" id="0x7f0e0214" />
    <public type="style" name="Widget.AppCompat.ListView.Menu" id="0x7f0e0215" />
    <public type="style" name="Widget.AppCompat.PopupMenu" id="0x7f0e0216" />
    <public type="style" name="Widget.AppCompat.PopupMenu.Overflow" id="0x7f0e0217" />
    <public type="style" name="Widget.AppCompat.PopupWindow" id="0x7f0e0218" />
    <public type="style" name="Widget.AppCompat.ProgressBar" id="0x7f0e0219" />
    <public type="style" name="Widget.AppCompat.ProgressBar.Horizontal" id="0x7f0e021a" />
    <public type="style" name="Widget.AppCompat.RatingBar" id="0x7f0e021b" />
    <public type="style" name="Widget.AppCompat.RatingBar.Indicator" id="0x7f0e021c" />
    <public type="style" name="Widget.AppCompat.RatingBar.Small" id="0x7f0e021d" />
    <public type="style" name="Widget.AppCompat.SearchView" id="0x7f0e021e" />
    <public type="style" name="Widget.AppCompat.SearchView.ActionBar" id="0x7f0e021f" />
    <public type="style" name="Widget.AppCompat.SeekBar" id="0x7f0e0220" />
    <public type="style" name="Widget.AppCompat.SeekBar.Discrete" id="0x7f0e0221" />
    <public type="style" name="Widget.AppCompat.Spinner" id="0x7f0e0222" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown" id="0x7f0e0223" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown.ActionBar" id="0x7f0e0224" />
    <public type="style" name="Widget.AppCompat.Spinner.Underlined" id="0x7f0e0225" />
    <public type="style" name="Widget.AppCompat.TextView.SpinnerItem" id="0x7f0e0226" />
    <public type="style" name="Widget.AppCompat.Toolbar" id="0x7f0e0227" />
    <public type="style" name="Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f0e0228" />
    <public type="style" name="Widget.Compat.NotificationActionContainer" id="0x7f0e0229" />
    <public type="style" name="Widget.Compat.NotificationActionText" id="0x7f0e022a" />
    <public type="style" name="Widget.Design.AppBarLayout" id="0x7f0e022b" />
    <public type="style" name="Widget.Design.BottomNavigationView" id="0x7f0e022c" />
    <public type="style" name="Widget.Design.BottomSheet.Modal" id="0x7f0e022d" />
    <public type="style" name="Widget.Design.CollapsingToolbar" id="0x7f0e022e" />
    <public type="style" name="Widget.Design.FloatingActionButton" id="0x7f0e022f" />
    <public type="style" name="Widget.Design.NavigationView" id="0x7f0e0230" />
    <public type="style" name="Widget.Design.ScrimInsetsFrameLayout" id="0x7f0e0231" />
    <public type="style" name="Widget.Design.Snackbar" id="0x7f0e0232" />
    <public type="style" name="Widget.Design.TabLayout" id="0x7f0e0233" />
    <public type="style" name="Widget.Design.TextInputLayout" id="0x7f0e0234" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar" id="0x7f0e0235" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.Colored" id="0x7f0e0236" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView" id="0x7f0e0237" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.Colored" id="0x7f0e0238" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet.Modal" id="0x7f0e0239" />
    <public type="style" name="Widget.MaterialComponents.Button" id="0x7f0e023a" />
    <public type="style" name="Widget.MaterialComponents.Button.Icon" id="0x7f0e023b" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton" id="0x7f0e023c" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton.Icon" id="0x7f0e023d" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton" id="0x7f0e023e" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog" id="0x7f0e023f" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" id="0x7f0e0240" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Icon" id="0x7f0e0241" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton" id="0x7f0e0242" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" id="0x7f0e0243" />
    <public type="style" name="Widget.MaterialComponents.CardView" id="0x7f0e0244" />
    <public type="style" name="Widget.MaterialComponents.Chip.Action" id="0x7f0e0245" />
    <public type="style" name="Widget.MaterialComponents.Chip.Choice" id="0x7f0e0246" />
    <public type="style" name="Widget.MaterialComponents.Chip.Entry" id="0x7f0e0247" />
    <public type="style" name="Widget.MaterialComponents.Chip.Filter" id="0x7f0e0248" />
    <public type="style" name="Widget.MaterialComponents.ChipGroup" id="0x7f0e0249" />
    <public type="style" name="Widget.MaterialComponents.FloatingActionButton" id="0x7f0e024a" />
    <public type="style" name="Widget.MaterialComponents.NavigationView" id="0x7f0e024b" />
    <public type="style" name="Widget.MaterialComponents.Snackbar" id="0x7f0e024c" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.FullWidth" id="0x7f0e024d" />
    <public type="style" name="Widget.MaterialComponents.TabLayout" id="0x7f0e024e" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.Colored" id="0x7f0e024f" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox" id="0x7f0e0250" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f0e0251" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f0e0252" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f0e0253" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox" id="0x7f0e0254" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" id="0x7f0e0255" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" id="0x7f0e0256" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" id="0x7f0e0257" />
    <public type="style" name="Widget.MaterialComponents.Toolbar" id="0x7f0e0258" />
    <public type="style" name="Widget.Support.CoordinatorLayout" id="0x7f0e0259" />
    <public type="style" name="qmui_dialog_wrap" id="0x7f0e025a" />
    <public type="style" name="qmui_tab_sign_count_view" id="0x7f0e025b" />
    <public type="style" name="qmui_tip_dialog_wrap" id="0x7f0e025c" />
    <public type="style" name="video_popup_toast_anim" id="0x7f0e025d" />
    <public type="style" name="video_style_dialog_progress" id="0x7f0e025e" />
    <public type="style" name="video_vertical_progressBar" id="0x7f0e025f" />
    <public type="xml" name="network_security_config" id="0x7f100000" />
    <public type="xml" name="provider_paths" id="0x7f100001" />
    <public type="xml" name="util_code_provider_paths" id="0x7f100002" />
</resources>
