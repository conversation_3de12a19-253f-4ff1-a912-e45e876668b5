<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="id" name="action0" />
    <item type="id" name="action_bar" />
    <item type="id" name="action_bar_activity_content" />
    <item type="id" name="action_bar_container" />
    <item type="id" name="action_bar_root" />
    <item type="id" name="action_bar_spinner" />
    <item type="id" name="action_bar_subtitle" />
    <item type="id" name="action_bar_title" />
    <item type="id" name="action_container" />
    <item type="id" name="action_context_bar" />
    <item type="id" name="action_divider" />
    <item type="id" name="action_image" />
    <item type="id" name="action_menu_divider" />
    <item type="id" name="action_menu_presenter" />
    <item type="id" name="action_mode_bar" />
    <item type="id" name="action_mode_bar_stub" />
    <item type="id" name="action_mode_close_button" />
    <item type="id" name="action_text" />
    <item type="id" name="actions" />
    <item type="id" name="activity_chooser_view_content" />
    <item type="id" name="ad_full_id" />
    <item type="id" name="ad_small_id" />
    <item type="id" name="ad_time" />
    <item type="id" name="alertTitle" />
    <item type="id" name="anchor_bottom" />
    <item type="id" name="anchor_top" />
    <item type="id" name="app_video_brightness" />
    <item type="id" name="app_video_brightness_box" />
    <item type="id" name="app_video_brightness_icon" />
    <item type="id" name="arrow_down" />
    <item type="id" name="arrow_up" />
    <item type="id" name="back" />
    <item type="id" name="back_tiny" />
    <item type="id" name="bottom_dialog_list_item_img" />
    <item type="id" name="bottom_dialog_list_item_mark" />
    <item type="id" name="bottom_dialog_list_item_mark_view_stub" />
    <item type="id" name="bottom_dialog_list_item_point" />
    <item type="id" name="bottom_dialog_list_item_title" />
    <item type="id" name="bottom_progressbar" />
    <item type="id" name="bottom_sheet_button_container" />
    <item type="id" name="bottom_sheet_close_button" />
    <item type="id" name="bottom_sheet_first_linear_layout" />
    <item type="id" name="bottom_sheet_second_linear_layout" />
    <item type="id" name="box" />
    <item type="id" name="buttonPanel" />
    <item type="id" name="cancel_action" />
    <item type="id" name="checkbox" />
    <item type="id" name="chronometer" />
    <item type="id" name="container" />
    <item type="id" name="content" />
    <item type="id" name="contentPanel" />
    <item type="id" name="contentWrap" />
    <item type="id" name="coordinator" />
    <item type="id" name="current" />
    <item type="id" name="current_scene" />
    <item type="id" name="customPanel" />
    <item type="id" name="decor_content_parent" />
    <item type="id" name="default_activity_button" />
    <item type="id" name="design_bottom_sheet" />
    <item type="id" name="design_menu_item_action_area" />
    <item type="id" name="design_menu_item_action_area_stub" />
    <item type="id" name="design_menu_item_text" />
    <item type="id" name="design_navigation_view" />
    <item type="id" name="dialog" />
    <item type="id" name="dialog_wrapper" />
    <item type="id" name="duration_image_tip" />
    <item type="id" name="duration_progressbar" />
    <item type="id" name="edit_query" />
    <item type="id" name="empty_view_button" />
    <item type="id" name="empty_view_detail" />
    <item type="id" name="empty_view_loading" />
    <item type="id" name="empty_view_title" />
    <item type="id" name="end_padder" />
    <item type="id" name="exo_artwork" />
    <item type="id" name="exo_buffering" />
    <item type="id" name="exo_content_frame" />
    <item type="id" name="exo_controller" />
    <item type="id" name="exo_controller_placeholder" />
    <item type="id" name="exo_duration" />
    <item type="id" name="exo_error_message" />
    <item type="id" name="exo_ffwd" />
    <item type="id" name="exo_next" />
    <item type="id" name="exo_overlay" />
    <item type="id" name="exo_pause" />
    <item type="id" name="exo_play" />
    <item type="id" name="exo_position" />
    <item type="id" name="exo_prev" />
    <item type="id" name="exo_progress" />
    <item type="id" name="exo_repeat_toggle" />
    <item type="id" name="exo_rew" />
    <item type="id" name="exo_shuffle" />
    <item type="id" name="exo_shutter" />
    <item type="id" name="exo_subtitles" />
    <item type="id" name="exo_track_selection_view" />
    <item type="id" name="expand_activities_button" />
    <item type="id" name="expanded_menu" />
    <item type="id" name="full_id" />
    <item type="id" name="fullscreen" />
    <item type="id" name="ghost_view" />
    <item type="id" name="grid_item_image" />
    <item type="id" name="grid_item_subscript" />
    <item type="id" name="grid_item_title" />
    <item type="id" name="group_divider" />
    <item type="id" name="group_layouttransition_backup" />
    <item type="id" name="group_list_item_accessoryView" />
    <item type="id" name="group_list_item_detailTextView" />
    <item type="id" name="group_list_item_imageView" />
    <item type="id" name="group_list_item_space" />
    <item type="id" name="group_list_item_textContainer" />
    <item type="id" name="group_list_item_textView" />
    <item type="id" name="group_list_item_tips_dot" />
    <item type="id" name="group_list_item_tips_new" />
    <item type="id" name="group_list_section_header_textView" />
    <item type="id" name="home" />
    <item type="id" name="icon" />
    <item type="id" name="icon_group" />
    <item type="id" name="image" />
    <item type="id" name="indicator_arrow" />
    <item type="id" name="indicator_container" />
    <item type="id" name="info" />
    <item type="id" name="isb_progress" />
    <item type="id" name="item_touch_helper_previous_elevation" />
    <item type="id" name="jump_ad" />
    <item type="id" name="largeLabel" />
    <item type="id" name="layout_bottom" />
    <item type="id" name="layout_top" />
    <item type="id" name="line1" />
    <item type="id" name="line3" />
    <item type="id" name="list_item" />
    <item type="id" name="listview" />
    <item type="id" name="loading" />
    <item type="id" name="lock_screen" />
    <item type="id" name="masked" />
    <item type="id" name="media_actions" />
    <item type="id" name="message" />
    <item type="id" name="mtrl_child_content_container" />
    <item type="id" name="mtrl_internal_children_alpha_tag" />
    <item type="id" name="navigation_header_container" />
    <item type="id" name="notification_background" />
    <item type="id" name="notification_main_column" />
    <item type="id" name="notification_main_column_container" />
    <item type="id" name="overlay_layout_params_backup" />
    <item type="id" name="overlay_view" />
    <item type="id" name="parentMatrix" />
    <item type="id" name="parentPanel" />
    <item type="id" name="parent_matrix" />
    <item type="id" name="preview_layout" />
    <item type="id" name="progress" />
    <item type="id" name="progress_circular" />
    <item type="id" name="progress_horizontal" />
    <item type="id" name="qmui_dialog_edit_input" />
    <item type="id" name="qmui_dialog_edit_right_icon" />
    <item type="id" name="qmui_tab_segment_item_id" />
    <item type="id" name="qmui_topbar_item_left_back" />
    <item type="id" name="qmui_view_can_not_cache_tag" />
    <item type="id" name="qmui_view_offset_helper" />
    <item type="id" name="qmui_window_inset_keyboard_area_consumer" />
    <item type="id" name="radio" />
    <item type="id" name="right_icon" />
    <item type="id" name="right_side" />
    <item type="id" name="runningTransitions" />
    <item type="id" name="save_image_matrix" />
    <item type="id" name="save_non_transition_alpha" />
    <item type="id" name="save_scale_type" />
    <item type="id" name="scene_layoutid_cache" />
    <item type="id" name="scrollIndicatorDown" />
    <item type="id" name="scrollIndicatorUp" />
    <item type="id" name="scrollView" />
    <item type="id" name="search_badge" />
    <item type="id" name="search_bar" />
    <item type="id" name="search_button" />
    <item type="id" name="search_close_btn" />
    <item type="id" name="search_edit_frame" />
    <item type="id" name="search_go_btn" />
    <item type="id" name="search_mag_icon" />
    <item type="id" name="search_plate" />
    <item type="id" name="search_src_text" />
    <item type="id" name="search_voice_btn" />
    <item type="id" name="select_dialog_listview" />
    <item type="id" name="shortcut" />
    <item type="id" name="smallLabel" />
    <item type="id" name="small_close" />
    <item type="id" name="small_id" />
    <item type="id" name="snackbar_action" />
    <item type="id" name="snackbar_text" />
    <item type="id" name="spacer" />
    <item type="id" name="split_action_bar" />
    <item type="id" name="status_bar_latest_event_content" />
    <item type="id" name="submenuarrow" />
    <item type="id" name="submit_area" />
    <item type="id" name="surface_container" />
    <item type="id" name="tag_layout_helper_bg" />
    <item type="id" name="tag_transition_group" />
    <item type="id" name="tag_unhandled_key_event_manager" />
    <item type="id" name="tag_unhandled_key_listeners" />
    <item type="id" name="text" />
    <item type="id" name="text2" />
    <item type="id" name="textSpacerNoButtons" />
    <item type="id" name="textSpacerNoTitle" />
    <item type="id" name="text_input_password_toggle" />
    <item type="id" name="textinput_counter" />
    <item type="id" name="textinput_error" />
    <item type="id" name="textinput_helper_text" />
    <item type="id" name="thumb" />
    <item type="id" name="time" />
    <item type="id" name="title" />
    <item type="id" name="titleDividerNoCustom" />
    <item type="id" name="title_template" />
    <item type="id" name="toast_icon" />
    <item type="id" name="toast_root" />
    <item type="id" name="toast_text" />
    <item type="id" name="topPanel" />
    <item type="id" name="total" />
    <item type="id" name="touch_outside" />
    <item type="id" name="transitionAlpha" />
    <item type="id" name="transitionName" />
    <item type="id" name="transitionPosition" />
    <item type="id" name="transitionTransform" />
    <item type="id" name="transition_current_scene" />
    <item type="id" name="transition_layout_save" />
    <item type="id" name="transition_position" />
    <item type="id" name="transition_scene_layoutid_cache" />
    <item type="id" name="transition_transform" />
    <item type="id" name="tv_current" />
    <item type="id" name="tv_duration" />
    <item type="id" name="up" />
    <item type="id" name="utvBottomIconView" />
    <item type="id" name="utvLeftIconView" />
    <item type="id" name="utvRightIconView" />
    <item type="id" name="utvTopIconView" />
    <item type="id" name="view_offset_helper" />
    <item type="id" name="visible" />
    <item type="id" name="volume_progressbar" />
    <item type="id" name="widget_container" />
</resources>
