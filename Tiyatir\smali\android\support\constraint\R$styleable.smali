.class public final Landroid/support/constraint/R$styleable;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/constraint/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "styleable"
.end annotation


# static fields
.field public static final ConstraintLayout_Layout:[I

.field public static final ConstraintLayout_Layout_android_maxHeight:I = 0x2

.field public static final ConstraintLayout_Layout_android_maxWidth:I = 0x1

.field public static final ConstraintLayout_Layout_android_minHeight:I = 0x4

.field public static final ConstraintLayout_Layout_android_minWidth:I = 0x3

.field public static final ConstraintLayout_Layout_android_orientation:I = 0x0

.field public static final ConstraintLayout_Layout_barrierAllowsGoneWidgets:I = 0x5

.field public static final ConstraintLayout_Layout_barrierDirection:I = 0x6

.field public static final ConstraintLayout_Layout_chainUseRtl:I = 0x7

.field public static final ConstraintLayout_Layout_constraintSet:I = 0x8

.field public static final ConstraintLayout_Layout_constraint_referenced_ids:I = 0x9

.field public static final ConstraintLayout_Layout_layout_constrainedHeight:I = 0xa

.field public static final ConstraintLayout_Layout_layout_constrainedWidth:I = 0xb

.field public static final ConstraintLayout_Layout_layout_constraintBaseline_creator:I = 0xc

.field public static final ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf:I = 0xd

.field public static final ConstraintLayout_Layout_layout_constraintBottom_creator:I = 0xe

.field public static final ConstraintLayout_Layout_layout_constraintBottom_toBottomOf:I = 0xf

.field public static final ConstraintLayout_Layout_layout_constraintBottom_toTopOf:I = 0x10

.field public static final ConstraintLayout_Layout_layout_constraintCircle:I = 0x11

.field public static final ConstraintLayout_Layout_layout_constraintCircleAngle:I = 0x12

.field public static final ConstraintLayout_Layout_layout_constraintCircleRadius:I = 0x13

.field public static final ConstraintLayout_Layout_layout_constraintDimensionRatio:I = 0x14

.field public static final ConstraintLayout_Layout_layout_constraintEnd_toEndOf:I = 0x15

.field public static final ConstraintLayout_Layout_layout_constraintEnd_toStartOf:I = 0x16

.field public static final ConstraintLayout_Layout_layout_constraintGuide_begin:I = 0x17

.field public static final ConstraintLayout_Layout_layout_constraintGuide_end:I = 0x18

.field public static final ConstraintLayout_Layout_layout_constraintGuide_percent:I = 0x19

.field public static final ConstraintLayout_Layout_layout_constraintHeight_default:I = 0x1a

.field public static final ConstraintLayout_Layout_layout_constraintHeight_max:I = 0x1b

.field public static final ConstraintLayout_Layout_layout_constraintHeight_min:I = 0x1c

.field public static final ConstraintLayout_Layout_layout_constraintHeight_percent:I = 0x1d

.field public static final ConstraintLayout_Layout_layout_constraintHorizontal_bias:I = 0x1e

.field public static final ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle:I = 0x1f

.field public static final ConstraintLayout_Layout_layout_constraintHorizontal_weight:I = 0x20

.field public static final ConstraintLayout_Layout_layout_constraintLeft_creator:I = 0x21

.field public static final ConstraintLayout_Layout_layout_constraintLeft_toLeftOf:I = 0x22

.field public static final ConstraintLayout_Layout_layout_constraintLeft_toRightOf:I = 0x23

.field public static final ConstraintLayout_Layout_layout_constraintRight_creator:I = 0x24

.field public static final ConstraintLayout_Layout_layout_constraintRight_toLeftOf:I = 0x25

.field public static final ConstraintLayout_Layout_layout_constraintRight_toRightOf:I = 0x26

.field public static final ConstraintLayout_Layout_layout_constraintStart_toEndOf:I = 0x27

.field public static final ConstraintLayout_Layout_layout_constraintStart_toStartOf:I = 0x28

.field public static final ConstraintLayout_Layout_layout_constraintTop_creator:I = 0x29

.field public static final ConstraintLayout_Layout_layout_constraintTop_toBottomOf:I = 0x2a

.field public static final ConstraintLayout_Layout_layout_constraintTop_toTopOf:I = 0x2b

.field public static final ConstraintLayout_Layout_layout_constraintVertical_bias:I = 0x2c

.field public static final ConstraintLayout_Layout_layout_constraintVertical_chainStyle:I = 0x2d

.field public static final ConstraintLayout_Layout_layout_constraintVertical_weight:I = 0x2e

.field public static final ConstraintLayout_Layout_layout_constraintWidth_default:I = 0x2f

.field public static final ConstraintLayout_Layout_layout_constraintWidth_max:I = 0x30

.field public static final ConstraintLayout_Layout_layout_constraintWidth_min:I = 0x31

.field public static final ConstraintLayout_Layout_layout_constraintWidth_percent:I = 0x32

.field public static final ConstraintLayout_Layout_layout_editor_absoluteX:I = 0x33

.field public static final ConstraintLayout_Layout_layout_editor_absoluteY:I = 0x34

.field public static final ConstraintLayout_Layout_layout_goneMarginBottom:I = 0x35

.field public static final ConstraintLayout_Layout_layout_goneMarginEnd:I = 0x36

.field public static final ConstraintLayout_Layout_layout_goneMarginLeft:I = 0x37

.field public static final ConstraintLayout_Layout_layout_goneMarginRight:I = 0x38

.field public static final ConstraintLayout_Layout_layout_goneMarginStart:I = 0x39

.field public static final ConstraintLayout_Layout_layout_goneMarginTop:I = 0x3a

.field public static final ConstraintLayout_Layout_layout_optimizationLevel:I = 0x3b

.field public static final ConstraintLayout_placeholder:[I

.field public static final ConstraintLayout_placeholder_content:I = 0x0

.field public static final ConstraintLayout_placeholder_emptyVisibility:I = 0x1

.field public static final ConstraintSet:[I

.field public static final ConstraintSet_android_alpha:I = 0xd

.field public static final ConstraintSet_android_elevation:I = 0x1a

.field public static final ConstraintSet_android_id:I = 0x1

.field public static final ConstraintSet_android_layout_height:I = 0x4

.field public static final ConstraintSet_android_layout_marginBottom:I = 0x8

.field public static final ConstraintSet_android_layout_marginEnd:I = 0x18

.field public static final ConstraintSet_android_layout_marginLeft:I = 0x5

.field public static final ConstraintSet_android_layout_marginRight:I = 0x7

.field public static final ConstraintSet_android_layout_marginStart:I = 0x17

.field public static final ConstraintSet_android_layout_marginTop:I = 0x6

.field public static final ConstraintSet_android_layout_width:I = 0x3

.field public static final ConstraintSet_android_maxHeight:I = 0xa

.field public static final ConstraintSet_android_maxWidth:I = 0x9

.field public static final ConstraintSet_android_minHeight:I = 0xc

.field public static final ConstraintSet_android_minWidth:I = 0xb

.field public static final ConstraintSet_android_orientation:I = 0x0

.field public static final ConstraintSet_android_rotation:I = 0x14

.field public static final ConstraintSet_android_rotationX:I = 0x15

.field public static final ConstraintSet_android_rotationY:I = 0x16

.field public static final ConstraintSet_android_scaleX:I = 0x12

.field public static final ConstraintSet_android_scaleY:I = 0x13

.field public static final ConstraintSet_android_transformPivotX:I = 0xe

.field public static final ConstraintSet_android_transformPivotY:I = 0xf

.field public static final ConstraintSet_android_translationX:I = 0x10

.field public static final ConstraintSet_android_translationY:I = 0x11

.field public static final ConstraintSet_android_translationZ:I = 0x19

.field public static final ConstraintSet_android_visibility:I = 0x2

.field public static final ConstraintSet_barrierAllowsGoneWidgets:I = 0x1b

.field public static final ConstraintSet_barrierDirection:I = 0x1c

.field public static final ConstraintSet_chainUseRtl:I = 0x1d

.field public static final ConstraintSet_constraint_referenced_ids:I = 0x1e

.field public static final ConstraintSet_layout_constrainedHeight:I = 0x1f

.field public static final ConstraintSet_layout_constrainedWidth:I = 0x20

.field public static final ConstraintSet_layout_constraintBaseline_creator:I = 0x21

.field public static final ConstraintSet_layout_constraintBaseline_toBaselineOf:I = 0x22

.field public static final ConstraintSet_layout_constraintBottom_creator:I = 0x23

.field public static final ConstraintSet_layout_constraintBottom_toBottomOf:I = 0x24

.field public static final ConstraintSet_layout_constraintBottom_toTopOf:I = 0x25

.field public static final ConstraintSet_layout_constraintCircle:I = 0x26

.field public static final ConstraintSet_layout_constraintCircleAngle:I = 0x27

.field public static final ConstraintSet_layout_constraintCircleRadius:I = 0x28

.field public static final ConstraintSet_layout_constraintDimensionRatio:I = 0x29

.field public static final ConstraintSet_layout_constraintEnd_toEndOf:I = 0x2a

.field public static final ConstraintSet_layout_constraintEnd_toStartOf:I = 0x2b

.field public static final ConstraintSet_layout_constraintGuide_begin:I = 0x2c

.field public static final ConstraintSet_layout_constraintGuide_end:I = 0x2d

.field public static final ConstraintSet_layout_constraintGuide_percent:I = 0x2e

.field public static final ConstraintSet_layout_constraintHeight_default:I = 0x2f

.field public static final ConstraintSet_layout_constraintHeight_max:I = 0x30

.field public static final ConstraintSet_layout_constraintHeight_min:I = 0x31

.field public static final ConstraintSet_layout_constraintHeight_percent:I = 0x32

.field public static final ConstraintSet_layout_constraintHorizontal_bias:I = 0x33

.field public static final ConstraintSet_layout_constraintHorizontal_chainStyle:I = 0x34

.field public static final ConstraintSet_layout_constraintHorizontal_weight:I = 0x35

.field public static final ConstraintSet_layout_constraintLeft_creator:I = 0x36

.field public static final ConstraintSet_layout_constraintLeft_toLeftOf:I = 0x37

.field public static final ConstraintSet_layout_constraintLeft_toRightOf:I = 0x38

.field public static final ConstraintSet_layout_constraintRight_creator:I = 0x39

.field public static final ConstraintSet_layout_constraintRight_toLeftOf:I = 0x3a

.field public static final ConstraintSet_layout_constraintRight_toRightOf:I = 0x3b

.field public static final ConstraintSet_layout_constraintStart_toEndOf:I = 0x3c

.field public static final ConstraintSet_layout_constraintStart_toStartOf:I = 0x3d

.field public static final ConstraintSet_layout_constraintTop_creator:I = 0x3e

.field public static final ConstraintSet_layout_constraintTop_toBottomOf:I = 0x3f

.field public static final ConstraintSet_layout_constraintTop_toTopOf:I = 0x40

.field public static final ConstraintSet_layout_constraintVertical_bias:I = 0x41

.field public static final ConstraintSet_layout_constraintVertical_chainStyle:I = 0x42

.field public static final ConstraintSet_layout_constraintVertical_weight:I = 0x43

.field public static final ConstraintSet_layout_constraintWidth_default:I = 0x44

.field public static final ConstraintSet_layout_constraintWidth_max:I = 0x45

.field public static final ConstraintSet_layout_constraintWidth_min:I = 0x46

.field public static final ConstraintSet_layout_constraintWidth_percent:I = 0x47

.field public static final ConstraintSet_layout_editor_absoluteX:I = 0x48

.field public static final ConstraintSet_layout_editor_absoluteY:I = 0x49

.field public static final ConstraintSet_layout_goneMarginBottom:I = 0x4a

.field public static final ConstraintSet_layout_goneMarginEnd:I = 0x4b

.field public static final ConstraintSet_layout_goneMarginLeft:I = 0x4c

.field public static final ConstraintSet_layout_goneMarginRight:I = 0x4d

.field public static final ConstraintSet_layout_goneMarginStart:I = 0x4e

.field public static final ConstraintSet_layout_goneMarginTop:I = 0x4f

.field public static final LinearConstraintLayout:[I

.field public static final LinearConstraintLayout_android_orientation:I


# direct methods
.method public static constructor <clinit>()V
    .locals 3

    const/16 v0, 0x3c

    new-array v0, v0, [I

    fill-array-data v0, :array_0

    sput-object v0, Landroid/support/constraint/R$styleable;->ConstraintLayout_Layout:[I

    const/4 v0, 0x2

    new-array v0, v0, [I

    fill-array-data v0, :array_1

    sput-object v0, Landroid/support/constraint/R$styleable;->ConstraintLayout_placeholder:[I

    const/16 v0, 0x50

    new-array v0, v0, [I

    fill-array-data v0, :array_2

    sput-object v0, Landroid/support/constraint/R$styleable;->ConstraintSet:[I

    const/4 v0, 0x1

    new-array v0, v0, [I

    const/4 v1, 0x0

    const v2, 0x10100c4

    aput v2, v0, v1

    sput-object v0, Landroid/support/constraint/R$styleable;->LinearConstraintLayout:[I

    return-void

    :array_0
    .array-data 4
        0x10100c4
        0x101011f
        0x1010120
        0x101013f
        0x1010140
        0x7f030047
        0x7f030048
        0x7f030072
        0x7f0300a4
        0x7f0300a5
        0x7f030164
        0x7f030165
        0x7f030166
        0x7f030167
        0x7f030168
        0x7f030169
        0x7f03016a
        0x7f03016b
        0x7f03016c
        0x7f03016d
        0x7f03016e
        0x7f03016f
        0x7f030170
        0x7f030171
        0x7f030172
        0x7f030173
        0x7f030174
        0x7f030175
        0x7f030176
        0x7f030177
        0x7f030178
        0x7f030179
        0x7f03017a
        0x7f03017b
        0x7f03017c
        0x7f03017d
        0x7f03017e
        0x7f03017f
        0x7f030180
        0x7f030181
        0x7f030182
        0x7f030183
        0x7f030184
        0x7f030185
        0x7f030186
        0x7f030187
        0x7f030188
        0x7f030189
        0x7f03018a
        0x7f03018b
        0x7f03018c
        0x7f03018e
        0x7f03018f
        0x7f030190
        0x7f030191
        0x7f030192
        0x7f030193
        0x7f030194
        0x7f030195
        0x7f030198
    .end array-data

    :array_1
    .array-data 4
        0x7f0300a6
        0x7f0300d7
    .end array-data

    :array_2
    .array-data 4
        0x10100c4
        0x10100d0
        0x10100dc
        0x10100f4
        0x10100f5
        0x10100f7
        0x10100f8
        0x10100f9
        0x10100fa
        0x101011f
        0x1010120
        0x101013f
        0x1010140
        0x101031f
        0x1010320
        0x1010321
        0x1010322
        0x1010323
        0x1010324
        0x1010325
        0x1010326
        0x1010327
        0x1010328
        0x10103b5
        0x10103b6
        0x10103fa
        0x1010440
        0x7f030047
        0x7f030048
        0x7f030072
        0x7f0300a5
        0x7f030164
        0x7f030165
        0x7f030166
        0x7f030167
        0x7f030168
        0x7f030169
        0x7f03016a
        0x7f03016b
        0x7f03016c
        0x7f03016d
        0x7f03016e
        0x7f03016f
        0x7f030170
        0x7f030171
        0x7f030172
        0x7f030173
        0x7f030174
        0x7f030175
        0x7f030176
        0x7f030177
        0x7f030178
        0x7f030179
        0x7f03017a
        0x7f03017b
        0x7f03017c
        0x7f03017d
        0x7f03017e
        0x7f03017f
        0x7f030180
        0x7f030181
        0x7f030182
        0x7f030183
        0x7f030184
        0x7f030185
        0x7f030186
        0x7f030187
        0x7f030188
        0x7f030189
        0x7f03018a
        0x7f03018b
        0x7f03018c
        0x7f03018e
        0x7f03018f
        0x7f030190
        0x7f030191
        0x7f030192
        0x7f030193
        0x7f030194
        0x7f030195
    .end array-data
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
