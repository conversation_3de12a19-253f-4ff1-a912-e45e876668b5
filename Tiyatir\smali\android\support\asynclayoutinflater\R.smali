.class public final Landroid/support/asynclayoutinflater/R;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Landroid/support/asynclayoutinflater/R$attr;,
        Landroid/support/asynclayoutinflater/R$color;,
        Landroid/support/asynclayoutinflater/R$dimen;,
        Landroid/support/asynclayoutinflater/R$drawable;,
        Landroid/support/asynclayoutinflater/R$id;,
        Landroid/support/asynclayoutinflater/R$integer;,
        Landroid/support/asynclayoutinflater/R$layout;,
        Landroid/support/asynclayoutinflater/R$string;,
        Landroid/support/asynclayoutinflater/R$style;,
        Landroid/support/asynclayoutinflater/R$styleable;
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
