.class interface abstract Landroid/support/design/circularreveal/CircularRevealHelper$Delegate;
.super Ljava/lang/Object;
.source "CircularRevealHelper.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/design/circularreveal/CircularRevealHelper;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "Delegate"
.end annotation


# virtual methods
.method public abstract actualDraw(Landroid/graphics/Canvas;)V
.end method

.method public abstract actualIsOpaque()Z
.end method
