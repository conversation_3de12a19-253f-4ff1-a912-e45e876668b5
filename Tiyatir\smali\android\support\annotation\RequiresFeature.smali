.class public interface abstract annotation Landroid/support/annotation/RequiresFeature;
.super Ljava/lang/Object;
.source "RequiresFeature.java"

# interfaces
.implements Ljava/lang/annotation/Annotation;


# annotations
.annotation runtime Ljava/lang/annotation/Retention;
    value = .enum Ljava/lang/annotation/RetentionPolicy;->SOURCE:Ljava/lang/annotation/RetentionPolicy;
.end annotation

.annotation runtime Ljava/lang/annotation/Target;
    value = {
        .enum Ljava/lang/annotation/ElementType;->TYPE:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->FIELD:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->METHOD:Ljava/lang/annotation/ElementType;,
        .enum Ljava/lang/annotation/ElementType;->CONSTRUCTOR:Lja<PERSON>/lang/annotation/ElementType;
    }
.end annotation


# virtual methods
.method public abstract enforcement()Ljava/lang/String;
.end method

.method public abstract name()Ljava/lang/String;
.end method
