.class public final Landroid/support/asynclayoutinflater/R$drawable;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/asynclayoutinflater/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "drawable"
.end annotation


# static fields
.field public static final notification_action_background:I = 0x7f0700cd

.field public static final notification_bg:I = 0x7f0700ce

.field public static final notification_bg_low:I = 0x7f0700cf

.field public static final notification_bg_low_normal:I = 0x7f0700d0

.field public static final notification_bg_low_pressed:I = 0x7f0700d1

.field public static final notification_bg_normal:I = 0x7f0700d2

.field public static final notification_bg_normal_pressed:I = 0x7f0700d3

.field public static final notification_icon_background:I = 0x7f0700d4

.field public static final notification_template_icon_bg:I = 0x7f0700d5

.field public static final notification_template_icon_low_bg:I = 0x7f0700d6

.field public static final notification_tile_bg:I = 0x7f0700d7

.field public static final notify_panel_notification_icon_bg:I = 0x7f0700d8


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
