<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/toast_root" android:fitsSystemWindows="false" android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/toast_icon" android:layout_width="24.0dip" android:layout_height="24.0dip" android:layout_marginRight="8.0dip" android:contentDescription="@string/toast_message" />
    <TextView android:id="@id/toast_text" android:layout_width="wrap_content" android:layout_height="wrap_content"
      xmlns:android="http://schemas.android.com/apk/res/android" />
</LinearLayout>
