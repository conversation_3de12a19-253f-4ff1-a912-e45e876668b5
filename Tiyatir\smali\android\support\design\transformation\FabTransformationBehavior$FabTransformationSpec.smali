.class public Landroid/support/design/transformation/FabTransformationBehavior$FabTransformationSpec;
.super Ljava/lang/Object;
.source "FabTransformationBehavior.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Landroid/support/design/transformation/FabTransformationBehavior;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xc
    name = "FabTransformationSpec"
.end annotation


# instance fields
.field public positioning:Landroid/support/design/animation/Positioning;

.field public timings:Landroid/support/design/animation/MotionSpec;


# direct methods
.method protected constructor <init>()V
    .locals 0

    .line 709
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
